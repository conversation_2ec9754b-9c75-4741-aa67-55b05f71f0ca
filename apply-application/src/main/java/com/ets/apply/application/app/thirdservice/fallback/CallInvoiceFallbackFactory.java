package com.ets.apply.application.app.thirdservice.fallback;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallInvoiceMainFeign;
import com.ets.apply.application.app.thirdservice.request.invoiceMain.UserBindCardInfoReq;
import com.ets.apply.application.app.thirdservice.response.invoice.UserBindCardInfoResponse;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CallInvoiceFallbackFactory implements FallbackFactory<CallInvoiceMainFeign> {
    @Override
    public CallInvoiceMainFeign create(Throwable throwable) {
        return new CallInvoiceMainFeign() {
            @Override
            public JsonResult<List<UserBindCardInfoResponse>> selectUserBindCardList(UserBindCardInfoReq req) {
                return JsonResult.error("请求发票绑卡信息错误: " + throwable.getCause().getMessage());
            }

        };
    }
}
