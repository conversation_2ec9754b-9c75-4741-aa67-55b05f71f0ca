package com.ets.apply.application.common.vo.applyPage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ApplyPageVO {


    /**
     * 页面配置sn
     */
    private String pageSn;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 模板id
     */
    private String templateSn;

    /**
     * 页面类型[1-普通页面 2-模板页面]
     */
    private Integer pageType;

    /**
     * 页面状态[0-初始状态 1-上架 2-下架]
     */
    private Integer pageStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 页面配置信息
     */
    private Object content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private LocalDateTime updatedAt;

    /**
     * 操作人
     */
    private String operator;



}
