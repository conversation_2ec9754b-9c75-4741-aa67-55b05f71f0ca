package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.LogisticOrderEntity;
import com.ets.apply.application.infra.mapper.LogisticOrderMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Service
@DS("db-order")
public class LogisticOrderService extends BaseService<LogisticOrderMapper, LogisticOrderEntity> {

    public LogisticOrderEntity getByLogisticOrderSn(String logisticOrderSn) {
        Wrapper<LogisticOrderEntity> queryWrapper = Wrappers.<LogisticOrderEntity>lambdaQuery()
                .eq(LogisticOrderEntity::getLogisticOrderSn, logisticOrderSn)
                .last("limit 1");
        return getOne(queryWrapper);
    }

    public List<LogisticOrderEntity> getByLogisticOrderSns(List<String> LogisticOrderSns) {
        LambdaQueryWrapper<LogisticOrderEntity> wrapper = Wrappers.<LogisticOrderEntity>lambdaQuery()
                .in(LogisticOrderEntity::getLogisticOrderSn, LogisticOrderSns);
        return super.baseMapper.selectList(wrapper);
    }
}
