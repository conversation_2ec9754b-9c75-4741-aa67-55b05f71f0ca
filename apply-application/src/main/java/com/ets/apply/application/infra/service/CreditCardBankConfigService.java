package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.creditCardBankConfig.CreditCardBankConfigBankStatusEnum;
import com.ets.apply.application.infra.entity.CreditCardBankConfigEntity;
import com.ets.apply.application.infra.entity.CreditCardBankEntity;
import com.ets.apply.application.infra.mapper.CreditCardBankConfigMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@DS("db-etc")
public class CreditCardBankConfigService extends BaseService<CreditCardBankConfigMapper, CreditCardBankConfigEntity> {

    public List<CreditCardBankConfigEntity> getAll() {
        LambdaQueryWrapper<CreditCardBankConfigEntity> wrapper = Wrappers.<CreditCardBankConfigEntity>lambdaQuery()
                .select()
                .orderByDesc(CreditCardBankConfigEntity::getCreatedAt);
        return this.getListByWrapper(wrapper);
    }

    /**
     * 根据配置银行状态获取银行列表信息
     */
    public List<CreditCardBankConfigEntity> getListByBankStatus(Integer bankStatus){
        LambdaQueryWrapper<CreditCardBankConfigEntity> wrapper = Wrappers.<CreditCardBankConfigEntity>lambdaQuery()
                .select()
                .eq(CreditCardBankConfigEntity::getBankStatus,bankStatus)
                .orderByDesc(CreditCardBankConfigEntity::getCreatedAt);
        return this.getListByWrapper(wrapper);
    }

    // 检查银行是否在可办理的银行列表中
    public boolean checkBankCanApply(Integer whichBank) {
        LambdaQueryWrapper<CreditCardBankConfigEntity> wrapper = Wrappers.<CreditCardBankConfigEntity>lambdaQuery()
                .select()
                .eq(CreditCardBankConfigEntity::getBankId, whichBank)
                .eq(CreditCardBankConfigEntity::getBankStatus, CreditCardBankConfigBankStatusEnum.NORMAL.getStatus());
        return this.getOneByWrapper(wrapper) != null;
    }
}
