package com.ets.apply.application.common.vo.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MapThirdPartnerPackageListVO {

    private Integer id;
    private Integer partnerId;
    private String companyName;
    private String packageSn;
    private String aliasName;
    /**
     * 原始价格，精度分
     */
    private Integer originalPrice;

    /**
     * 销售价格，精度分
     */
    private Integer presentPrice;

    /**
     * 商品图片url
     */
    private String icon;
    /**
     * 商品图片url
     */
    private String productImg;

    /**
     * 排序，序号越大排前面
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

}
