package com.ets.apply.feign.feign;

import com.ets.apply.feign.fallback.ProductOrderFallbackFactory;
import com.ets.apply.feign.request.GetFinishOrdersDTO;
import com.ets.apply.feign.request.ProductOrderExternalCreateDTO;
import com.ets.apply.feign.response.ProductOrderExternalCreateVO;
import com.ets.apply.feign.response.ProductOrderResponse;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

@FeignClient(
        url = "${microUrls.apply:http://apply-application:20070}",
        name = "applyProductOrderFeign",
        contextId = "product-order",
        path = "/third/productOrder",
        fallbackFactory = ProductOrderFallbackFactory.class
)
public interface ProductOrderFeign {

    @RequestMapping("/getFinishedOrders")
    JsonResult<List<ProductOrderResponse>> getFinishedOrders(@RequestBody @Valid GetFinishOrdersDTO dto);

    @RequestMapping("/getProductOrder")
    JsonResult<ProductOrderResponse> getProductOrder(@RequestParam(value = "productOrderSn") String productOrderSn, @RequestParam(value = "uid") Long uid);

    @RequestMapping("/createOrderFromExternal")
    JsonResult<ProductOrderExternalCreateVO> createOrderFromExternal(@Valid @RequestBody ProductOrderExternalCreateDTO dto);

}

