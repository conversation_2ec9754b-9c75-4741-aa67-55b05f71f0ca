package com.ets.apply.application.common.config.cache;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import java.time.Duration;

@Slf4j
public class CustomizedRedisCacheManager extends RedisCacheManager {

    public CustomizedRedisCacheManager(RedisCacheWriter cacheWriter,
                                       RedisCacheConfiguration defaultCacheConfiguration) {
        super(cacheWriter, defaultCacheConfiguration);
    }

    @Override
    protected RedisCache createRedisCache(@NonNull String name, @Nullable RedisCacheConfiguration cacheConfig) {
        // 参数校验
        if (name.trim().isEmpty()) {
            throw new IllegalArgumentException("The name cannot be null or empty");
        }

        // 使用更安全的解析方法，并增加异常处理
        String[] array = StringUtils.delimitedListToStringArray(name, "#");
        String ttlString = array.length > 1 ? array[1] : null;
        if (!StringUtils.isEmpty(ttlString)) {
            try {
                long ttl = Long.parseLong(ttlString.trim());
                // 避免修改原参数，使用新的cacheConfig
                cacheConfig = cacheConfig.entryTtl(Duration.ofSeconds(ttl));
            } catch (NumberFormatException e) {
                // 记录警告日志，并使用默认值
                log.info("Warning: Failed to parse TTL value for cache " + name);
            }
        }


        // 保证变量name的合理使用，此处没有变化，根据实际业务逻辑调整
        String finalName = array.length > 0 ? array[0] : name;

        return super.createRedisCache(finalName, cacheConfig);
    }
}
