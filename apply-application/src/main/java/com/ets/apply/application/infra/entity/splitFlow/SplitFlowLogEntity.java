package com.ets.apply.application.infra.entity.splitFlow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("split_flow_log")
public class SplitFlowLogEntity extends BaseEntity<SplitFlowLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 日志流水id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置id
     */
    @TableField("splitFlowId")
    private Integer splitFlowId;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 修改内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
