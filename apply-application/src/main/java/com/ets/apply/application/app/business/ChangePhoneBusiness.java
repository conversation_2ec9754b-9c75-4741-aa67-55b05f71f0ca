package com.ets.apply.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.common.consts.ChangePhoneStatusEnum;
import com.ets.apply.application.common.dto.changePhone.ChangePhoneCreateDTO;
import com.ets.apply.application.common.dto.changePhone.ChangePhoneListDTO;
import com.ets.apply.application.common.dto.intensive.IntensiveChangePhoneListDTO;
import com.ets.apply.application.common.vo.intensive.IntensiveChangePhoneListVO;
import com.ets.apply.application.infra.entity.ChangePhone;
import com.ets.apply.application.infra.service.ChangePhoneService;
import com.ets.common.ToolsHelper;
import com.ets.user.feign.feign.UsersCardsFeign;
import com.ets.user.feign.feign.UsersPhoneFeign;
import com.ets.user.feign.request.UpdateReservedPhoneByPlateNoDTO;
import com.ets.user.feign.response.GetReservedPhoneByPlateNoVO;
import com.ets.user.feign.response.UsersCardsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ChangePhoneBusiness extends BaseBusiness {

    @Autowired
    private ChangePhoneService changePhoneService;

    @Autowired
    private UsersCardsFeign usersCardsFeign;

    @Autowired
    private UsersPhoneFeign usersPhoneFeign;

    public IPage<ChangePhone> getList(ChangePhoneListDTO listDTO) {
        return changePhoneService.getPage(listDTO);
    }

    public IPage<IntensiveChangePhoneListVO> getIntensiveList(IntensiveChangePhoneListDTO listDTO) {
        ChangePhoneListDTO changePhoneListDTO = BeanUtil.copyProperties(listDTO, ChangePhoneListDTO.class);
        return getList(changePhoneListDTO).convert(changePhone -> {
            IntensiveChangePhoneListVO listVO = BeanUtil.copyProperties(changePhone, IntensiveChangePhoneListVO.class);
            DesensitizedUtil.mobilePhone(listVO.getOldPhoneNo());
            DesensitizedUtil.mobilePhone(listVO.getNewPhoneNo());
            return listVO;
        });
    }

    public void create(ChangePhoneCreateDTO createDTO) {
        // 车牌有多张卡，并属于不同用户，则拦截
        List<UsersCardsResponse> usersCardsList = usersCardsFeign.getListByPlateNo(createDTO.getPlateNo()).getDataWithCheckError();
        if (ObjectUtils.isEmpty(usersCardsList)) {
            ToolsHelper.throwException("用户车牌不存在");
        }

        List<Long> uidList = usersCardsList.stream().distinct().map(UsersCardsResponse::getUid).collect(Collectors.toList());
        if (uidList.size() > 1) {
            ToolsHelper.throwException("该车牌关联了多个微信号，可能存在车牌冒用风险，请联系客户核实，移除冒用订单后重新更换手机号");
        }
        UsersCardsResponse usersCards = usersCardsList.get(0);

        GetReservedPhoneByPlateNoVO phoneVO = usersPhoneFeign.getVehicleReservedPhone(usersCards.getUid(), usersCards.getPlateNo()).getDataWithCheckError();
        if (StringUtils.isEmpty(phoneVO.getPhone()) || !phoneVO.getPhone().equals(createDTO.getOldPhoneNo())) {
            ToolsHelper.throwException("原手机号不正确");
        }

        // 新增记录
        ChangePhone changePhone = BeanUtil.copyProperties(createDTO, ChangePhone.class);
        changePhone.setChangeSn(generateSn("change_phone"));
        changePhone.setUid(usersCards.getUid());
        changePhone.setOpUid(0);
        changePhone.setOpTime(LocalDateTime.now());
        changePhone.setStatus(ChangePhoneStatusEnum.STATUS_DEFAULT.getValue());
        changePhoneService.create(changePhone);

        // 更新数据
        try {
            UpdateReservedPhoneByPlateNoDTO updateDTO = new UpdateReservedPhoneByPlateNoDTO();
            updateDTO.setPlateNo(usersCards.getPlateNo());
            updateDTO.setPlateColor(usersCards.getPlateColor());
            updateDTO.setUid(usersCards.getUid());
            updateDTO.setCardId(usersCards.getCardId());
            updateDTO.setCardNo(usersCards.getCardNo());
            updateDTO.setApplyOrderSn(usersCards.getApplyOrderSn());
            updateDTO.setPhone(changePhone.getNewPhoneNo());
            usersPhoneFeign.updateReservedPhoneByPlateNo(updateDTO);

            changePhone.setStatus(ChangePhoneStatusEnum.STATUS_SUCCESS.getValue());
        } catch (Exception e) {
            log.error("更新手机号失败", e);
            changePhone.setStatus(ChangePhoneStatusEnum.STATUS_FAIL.getValue());
        } finally {
            changePhoneService.updateById(changePhone);
        }
    }
}
