package com.ets.apply.application.common.dto.aftersalesreviews;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AdminAfterSalesReviewsGetListDTO {

    private String orderType;

    private Integer issuerId;

    private String plateNo;

    private Long uid;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate applyTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate applyTimeEnd;

    private String orderSn;

    private String reviewSn;

    private Integer reviewStatus;

    private String rejectReason;

    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Min(value = 1, message = "每页条数必须大于0")
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
