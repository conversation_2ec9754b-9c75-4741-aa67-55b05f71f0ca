package com.ets.apply.application.app.business;

import com.ets.apply.application.common.config.AppParamsConfig;
import com.ets.common.ToolsHelper;
import com.ets.starter.alarm.EmailAlarm;
import com.ets.starter.config.AppConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class BaseBusiness {

    @Autowired
    protected AppParamsConfig appParamsConfig;

    @Autowired
    protected AppConfig appConfig;

    @Autowired
    protected EmailAlarm emailAlarm;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    public String generateSn(String key) {
        return ToolsHelper.genNum(redisPermanentTemplate, key, appConfig.getEnv(), 8);
    }

}
