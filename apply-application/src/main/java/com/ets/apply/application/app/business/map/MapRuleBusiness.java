package com.ets.apply.application.app.business.map;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.consts.map.MapSetCacheEnum;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.dto.request.MapSetCacheByKeyDTO;
import com.ets.apply.application.common.vo.map.MapModulePackageListVO;
import com.ets.apply.application.common.vo.map.MapRuleCombineListVO;
import com.ets.apply.application.common.vo.map.MapRuleItemListVO;
import com.ets.apply.application.infra.entity.map.MapPackageSnEntity;
import com.ets.apply.application.infra.entity.map.MapRecommendEntity;
import com.ets.apply.application.infra.entity.mapRule.MapRuleCombineEntity;
import com.ets.apply.application.infra.entity.mapRule.MapRuleItemEntity;
import com.ets.apply.application.infra.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MapRuleBusiness {
    @Autowired
    private MapRuleCombineService mapRuleCombineService;
    @Autowired
    private MapRuleItemService mapRuleItemService;

    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;
    /*
     * 获取全部模块列表
     */
    public IPage<MapRuleCombineListVO> getCombineList(MapRuleCombineDTO dto) {
        // 分页设置
        IPage<MapRuleCombineEntity> oPage = new Page<>(1, 100000, true);

        // 查询条件设置
        LambdaQueryWrapper<MapRuleCombineEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.like(StringUtils.isNotEmpty(dto.getCombineName()),MapRuleCombineEntity::getCombineName, dto.getCombineName())
               .orderByDesc(MapRuleCombineEntity::getSort);
        IPage<MapRuleCombineEntity> pageList = mapRuleCombineService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            MapRuleCombineListVO vo = new MapRuleCombineListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setStatusStr((vo.getStatus() == 1)?"正常":"无效");
            //itemRulesList
            ArrayList itemRulesList = new ArrayList();
            List<MapRuleItemEntity> itemEntities = mapRuleItemService.getListBySns(JSON.parseArray(vo.getCombineValues()),MapRuleItemEntity::getId);
            if(itemEntities != null){
                for (MapRuleItemEntity mapRuleItemEntity: itemEntities) {
                    MapRuleCombineListVO.OptionsVo optionsVo = new MapRuleCombineListVO.OptionsVo();
                    optionsVo.setId(mapRuleItemEntity.getId());
                    optionsVo.setName(mapRuleItemEntity.getItemName());
                    itemRulesList.add(optionsVo);
                }
            }

            vo.setItemRulesList(itemRulesList);


            return vo;
        });
    }
    /*
     * 获取全部模块列表
     */
    public IPage<MapRuleItemListVO> getItemList(MapRuleItemDTO dto) {
        // 分页设置
        IPage<MapRuleItemEntity> oPage = new Page<>(1, 100000,true);

        // 查询条件设置
        LambdaQueryWrapper<MapRuleItemEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.like(StringUtils.isNotEmpty(dto.getItemName()),MapRuleItemEntity::getItemName, dto.getItemName())
            .orderByDesc(MapRuleItemEntity::getSort);
        IPage<MapRuleItemEntity> pageList = mapRuleItemService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            MapRuleItemListVO vo = new MapRuleItemListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setStatusStr((vo.getStatus() == 1)?"正常":"无效");
            return vo;
        });
    }

    /*
     * 新增产品包
     */
    public Boolean addCombine(MapRuleAddCombineDTO dto,String loginCode){
        MapRuleCombineEntity mapRuleCombineEntity = new MapRuleCombineEntity();
        mapRuleCombineEntity.setCombineName(dto.getCombineName());
        mapRuleCombineEntity.setSort(dto.getSort());
        mapRuleCombineEntity.setCombineValues(JSON.toJSONString(dto.getItemRules()));
        mapRuleCombineEntity.setOperator(loginCode);
        mapRuleCombineService.create(mapRuleCombineEntity);
        //todo  记录日志
        //更新ruleCombine缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(MapSetCacheEnum.MAP_SET_CACHE_KEY_RULE_COMBINE.getCode());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);

        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean updateCombine(MapRuleEditCombineDTO dto,String loginCode){
        LambdaUpdateWrapper<MapRuleCombineEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapRuleCombineEntity::getId, dto.getId())
            .set(MapRuleCombineEntity::getCombineValues, JSON.toJSONString(dto.getItemRules()))
            .set(MapRuleCombineEntity::getStatus, dto.getStatus())
            .set(MapRuleCombineEntity::getSort, dto.getSort())
            .set(MapRuleCombineEntity::getOperator,loginCode)
            .set(MapRuleCombineEntity::getUpdatedAt, LocalDateTime.now())
            .set(MapRuleCombineEntity::getCombineName, dto.getCombineName())
           ;
        mapRuleCombineService.updateByWrapper(wrapper);
        //todo  记录日志
        //更新ruleCombine缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(MapSetCacheEnum.MAP_SET_CACHE_KEY_RULE_COMBINE.getCode());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);

        return true;
    }

    /*
     * 新增产品包
     */
    public Boolean addItem(MapRuleAddItemDTO dto,String loginCode){
        MapRuleItemEntity mapRuleItemEntity = new MapRuleItemEntity();
        mapRuleItemEntity.setItemSn(dto.getItemSn());
        mapRuleItemEntity.setItemName(dto.getItemName());
        mapRuleItemEntity.setSort(dto.getSort());
        mapRuleItemEntity.setItemValues(dto.getValues());
        mapRuleItemEntity.setOperator(loginCode);
        mapRuleItemService.create(mapRuleItemEntity);
        //todo  记录日志
        //更新ruleItem缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(MapSetCacheEnum.MAP_SET_CACHE_KEY_RULE_ITEM.getCode());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean updateItem(MapRuleEditItemDTO dto,String loginCode){
        LambdaUpdateWrapper<MapRuleItemEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapRuleItemEntity::getId, dto.getId())
            .set(MapRuleItemEntity::getItemValues, dto.getValues())
            .set(MapRuleItemEntity::getStatus, dto.getStatus())
            .set(MapRuleItemEntity::getSort, dto.getSort())
            .set(MapRuleItemEntity::getItemSn, dto.getItemSn())
            .set(MapRuleItemEntity::getOperator, loginCode)
            .set(MapRuleItemEntity::getUpdatedAt, LocalDateTime.now())
            .set(MapRuleItemEntity::getItemName, dto.getItemName())
        ;
        mapRuleItemService.updateByWrapper(wrapper);
        //todo  记录日志
        //更新ruleItem缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(MapSetCacheEnum.MAP_SET_CACHE_KEY_RULE_ITEM.getCode());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
        return true;
    }
}
