package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.PayGatewayFeign;
import com.ets.apply.application.common.bo.pay.PrepayParamBO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class PayGatewayFallbackFactory implements FallbackFactory<PayGatewayFeign> {

    @Override
    public PayGatewayFeign create(Throwable cause) {
        return new PayGatewayFeign() {
            @Override
            public String prepay(PrepayParamBO params) {
                return JsonResult.error("支付服务调用失败：" + cause.getMessage()).toString();
            }

        };
    }
}