package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.issueService.IssueServiceStatusEnum;
import com.ets.apply.application.infra.entity.IssueServiceEntity;
import com.ets.apply.application.infra.entity.ReviewOrderBizLicenceEntity;
import com.ets.apply.application.infra.entity.ReviewOrderIdCardEntity;
import com.ets.apply.application.infra.mapper.IssueServiceMapper;
import com.ets.apply.application.infra.mapper.ReviewOrderBizLicenceMapper;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @since 2023-12-15
 */
@Service
@DS("db-order")
public class ReviewOrderBizLicenceService extends BaseService<ReviewOrderBizLicenceMapper, ReviewOrderBizLicenceEntity> {
    public ReviewOrderBizLicenceEntity getByReviewOrderSn(String reviewOrderSn) {

        Wrapper<ReviewOrderBizLicenceEntity> wrapper = Wrappers.<ReviewOrderBizLicenceEntity>lambdaQuery()
            .eq(ReviewOrderBizLicenceEntity::getReviewOrderSn, reviewOrderSn)
            .last("LIMIT 1");

        return super.baseMapper.selectOne(wrapper);
    }
}
