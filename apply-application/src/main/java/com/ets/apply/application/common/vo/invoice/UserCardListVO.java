package com.ets.apply.application.common.vo.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserCardListVO {
    private Integer plateColor;
    private String plateNo;
    private String issuedTime;
    private Long issuedDays = 0L;
    private String cardStatus;

    /**
     * 0-不确定 1-小于两年 2-大于两年
     */
    private Integer useYear =0;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bindTime;

    /**
     * 设置使用年限
     */
    public void setUseYear(Long issuedDays) {
        if (issuedDays == null || issuedDays == 0) {
            this.useYear = 0;
        } else {
            if (issuedDays < 365 * 2) {
                this.useYear = 1;
            }else{
                this.useYear = 2;
            }
        }

    }

}
