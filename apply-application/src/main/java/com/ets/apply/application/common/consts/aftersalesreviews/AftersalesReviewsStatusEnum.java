package com.ets.apply.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后审核单状态枚举
 */
@Getter
@AllArgsConstructor
public enum AftersalesReviewsStatusEnum {
    
    /**
     * 待审核
     */
    PENDING(0, "待审核"),
    
    /**
     * 审核通过
     */
    APPROVED(1, "审核通过"),
    
    /**
     * 审核驳回
     */
    REJECTED(2, "审核驳回"),
    
    /**
     * 已取消
     */
    CANCELED(3, "已取消");

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(AftersalesReviewsStatusEnum.values()).collect(Collectors.toMap(AftersalesReviewsStatusEnum::getValue, AftersalesReviewsStatusEnum::getDesc));
    }

}
