package com.ets.apply.application.infra.relation;

import java.util.function.BiConsumer;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.common.base.BaseEntityRelation;

public class AfterSalesReviewBindVehicleRelation extends BaseEntityRelation<AftersalesReviews, AftersalesReviewsVehicles> {

    @Override
    public SFunction<AftersalesReviewsVehicles, Object> getAffiliatedColumn() {
        return AftersalesReviewsVehicles
    }

    @Override
    public BiConsumer<AftersalesReviews, AftersalesReviewsVehicles> getEntityColumn() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getEntityColumn'");
    }

    @Override
    public SFunction<AftersalesReviews, Object> getMasterColumn() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getMasterColumn'");
    }
    
}
