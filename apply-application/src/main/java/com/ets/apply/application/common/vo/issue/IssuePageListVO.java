package com.ets.apply.application.common.vo.issue;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class IssuePageListVO {

    /**
     * 用户id
     */
    private Integer uid;

    /**
     * 发行服务单编号
     */
    private String serviceSn;

    /**
     * 类型[1:比亚迪APP,3:东风本田小程序]
     */
    private Integer type;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 商品订单编号，支付成功后更新
     */
    private String goodsOrderSn;

    /**
     * 协议流水号-sign_serial_num
     */
    private String contractId;

    /**
     * 状态[0:默认,1:已支付,6:已完成,7:已注销,9:已取消]
     */
    private Integer status;

    /**
     * 退款状态: 1 已申请退款，0未退款
     */
    private Integer refundStatus;

    /**
     * 支付金额
     */
    private BigDecimal paidFee;

    /**
     * 激活状态
     */
    private Integer activatedStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String typeStr;

    private String plateColorStr;

    private String statusStr;

    private String refundStatusStr;

    private String activatedStatusStr;
}
