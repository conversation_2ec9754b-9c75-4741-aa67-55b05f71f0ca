package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * etc发行服务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_issue_service_log")
public class IssueServiceLogEntity extends BaseEntity<IssueServiceLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品订单号
     */
    private String serviceSn;

    /**
     * 操作前订单状态
     */
    private Integer preStatus;

    /**
     * 操作后订单状态
     */
    private Integer afterStatus;

    /**
     * 日志内容
     */
    private String content;

    private Integer logType;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
