package com.ets.apply.application.common.vo.productOrder;

import com.ets.apply.application.common.consts.productOrder.ProductOrderLogisticStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductOrderLogListVO {

    private Integer id;

    /**
     * 商品订单号
     */
    private String productOrderSn;

    /**
     * 1:创建,2:取消,3:退款,4:备注,5:发货,6:发起售后,7:取消售后,8:完成售后,9:快递签收
     */
    private Integer logType;

    private Integer preStatus;

    private Integer afterStatus;

    private Integer logisticStatus;

    private String operateUser;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public String getStatusStr() {
        return ProductOrderStatusEnum.getDescByCode(afterStatus);
    }

    public String getLogisticStatusStr() {
        return ProductOrderLogisticStatusEnum.getDescByCode(logisticStatus);
    }

}
