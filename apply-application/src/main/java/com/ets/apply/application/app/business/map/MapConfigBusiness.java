package com.ets.apply.application.app.business.map;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserActivatedEnum;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserIsFirstUseEnum;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserStatusEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.*;
import com.ets.apply.application.common.consts.map.MapRecommendTypeEnum;
import com.ets.apply.application.common.dto.map.MapConfigDTO;
import com.ets.apply.application.common.vo.map.MapConfigVO;
import com.ets.apply.application.infra.entity.map.MapConfigEntity;
import com.ets.apply.application.infra.service.ConfigBizFieldValuesService;
import com.ets.apply.application.infra.service.MapConfigService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
public class MapConfigBusiness {
    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    /*
     * 获取全部模块列表
     */
    public MapConfigVO getInfoByKey(MapConfigDTO dto) {
        // 查询条件设置
        LambdaQueryWrapper<MapConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MapConfigEntity::getConfigKey, dto.getConfigKey());
        MapConfigEntity mapConfigEntity = mapConfigService.getOneByWrapper(wrapper);
        if(mapConfigEntity == null){
            ToolsHelper.throwException("配置数据为空");
        }
        MapConfigVO mapConfigVO = new MapConfigVO();
        BeanUtils.copyProperties(mapConfigEntity, mapConfigVO);
        mapConfigVO.setConfigValuesObject(JSON.parseArray(mapConfigEntity.getConfigValues()));
        mapConfigEntity.getConfigKey();
        MapRecommendTypeEnum.getDescByCode(mapConfigEntity.getConfigKey());

        mapConfigVO.setKeyDesc(MapRecommendTypeEnum.getDescByCode(mapConfigEntity.getConfigKey()));
        return mapConfigVO;
    }


    public HashMap<String, Object> getSelectOptions() {
        HashMap<String, Object> options = new HashMap<>();

        options.put("recommendType", configBizFieldValuesService.getListByBizFieldKey("recommend_type",false));

        options.put("decorationType", configBizFieldValuesService.getListByBizFieldKey("decoration_type",false));


        return options;
    }

}
