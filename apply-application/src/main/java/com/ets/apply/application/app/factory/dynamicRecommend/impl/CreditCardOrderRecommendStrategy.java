package com.ets.apply.application.app.factory.dynamicRecommend.impl;

import com.ets.apply.application.app.business.dynamicRecommend.DynamicRecommendBusiness;
import com.ets.apply.application.app.factory.dynamicRecommend.RecommendStrategy;
import com.ets.apply.application.common.dto.dynamicRecommend.RecommendDTO;
import com.ets.apply.application.common.vo.dynamicRecommend.DynamicRecommendVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CreditCardOrderRecommendStrategy implements RecommendStrategy {
    @Autowired
    DynamicRecommendBusiness dynamicRecommendBusiness;

    @Override
    public DynamicRecommendVO recommend(Long uid) {
        return dynamicRecommendBusiness.recommendCreditCardOrder(uid);
    }

    @Override
    public DynamicRecommendVO recommend(Long uid, RecommendDTO recommendDTO) {
        return dynamicRecommendBusiness.recommendCreditCardOrder(uid);
    }
}
