package com.ets.apply.application.app.job.creditCard;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.bo.creditCard.CreditCardCompensateBO;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


@Component
public class CreditCardJob {

    @Autowired
    private CreditCardBusiness creditCardBusiness;


    /**
     * 已首刷，激活数据缺失数据进行补充操作
     *
     * @param params
     * @return
     */
    @XxlJob("activateFieldsCompensation")
    public ReturnT<String> activateFieldsCompensation(String params) {
        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getFirstUseStartTime()) || ObjectUtil.isNull(compensateBO.getFirstUseEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setFirstUseStartTime(beginDate);
            compensateBO.setFirstUseEndTime(endDate);
        }
        creditCardBusiness.activateFieldsCompensation(compensateBO.getFirstUseStartTime(), compensateBO.getFirstUseEndTime());
        return ReturnT.SUCCESS;

    }

    /**
     * 已激活，审核数据缺失数据进行补偿操作
     *
     * @param params
     * @return
     */
    @XxlJob("auditFieldsCompensation")
    public ReturnT<String> auditFieldsCompensation(String params) {
        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getActivateStartTime()) || ObjectUtil.isNull(compensateBO.getActivateEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setActivateStartTime(beginDate);
            compensateBO.setActivateEndTime(endDate);
        }
        creditCardBusiness.auditFieldsCompensation(compensateBO.getActivateStartTime(), compensateBO.getActivateEndTime());
        return ReturnT.SUCCESS;

    }

    /**
     * 审核通过进件数据补偿
     * @param params
     * @return
     */
    @XxlJob("submitFieldsCompensation")
    public ReturnT<String> submitFieldsCompensation(String params) {
        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getAuditStartTime()) || ObjectUtil.isNull(compensateBO.getAuditEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setAuditStartTime(beginDate);
            compensateBO.setAuditEndTime(endDate);
        }
        creditCardBusiness.submitFieldsCompensation(compensateBO.getAuditStartTime(), compensateBO.getAuditEndTime());
        return ReturnT.SUCCESS;

    }

}
