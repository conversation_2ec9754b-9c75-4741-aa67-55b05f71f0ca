package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.productPackageResource.ProductPackageResourceBusiness;
import com.ets.apply.application.common.dto.request.productPackageResource.ProductPackageResourceDetailDTO;
import com.ets.apply.application.common.vo.productPackageResource.ProductPackageResourceVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/frontend/product-package-resource")
@RefreshScope
@RestController
@Slf4j
public class ProductPackageResourceFrontendController {

    @Autowired
    ProductPackageResourceBusiness productPackageResourceBusiness;

    /**
     *  <a href="https://yapi.etczs.net/project/312/interface/api/34062">...</a>
     */
    @RequestMapping("/detail")
    JsonResult<ProductPackageResourceVO> getDetail(@RequestBody @Valid ProductPackageResourceDetailDTO detailDTO) {
        ProductPackageResourceVO detail = productPackageResourceBusiness.getDetailWithCache(detailDTO);
        return JsonResult.ok(detail);
    }

}
