package com.ets.apply.application.app.job;

import com.ets.apply.application.app.business.ProductOrderImportBusiness;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ImportRecordJob {

    @Autowired
    private ProductOrderImportBusiness productOrderImportBusiness;

    @XxlJob("retryImportFailRecord")
    public ReturnT<String> retryImportFailRecord(String params){

        productOrderImportBusiness.checkImportBatch();

        return ReturnT.SUCCESS;
    }

    @XxlJob("retryImportFailRecordByBatchNo")
    public ReturnT<String> retryImportFailRecordByBatchNo(String params){

        productOrderImportBusiness.retryFailedByBatchNo(params);

        return ReturnT.SUCCESS;
    }
}
