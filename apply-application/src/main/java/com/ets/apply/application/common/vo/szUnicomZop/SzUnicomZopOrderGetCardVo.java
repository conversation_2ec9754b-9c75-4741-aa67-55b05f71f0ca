package com.ets.apply.application.common.vo.szUnicomZop;

import lombok.Data;

import java.util.List;


@Data
public class SzUnicomZopOrderGetCardVo {
    private String code;
    private String message;
    private String success;
    private List<ReturnData> data;

    @Data
    public static class ReturnData{
        private String mobile;
        private String id;
        private String state;
        private String time;
        private String createTime;
        private String note;
        private String openId;
        private String product;
        private String sim;
        private String uin;
        private String cborder;
        private String value1;
        private String value2;
        private String value3;
        private String value4;
        private String value5;
        private String value6;
        private String order;
        private String touchApplyId;
        private String lgtsId;
        private String contact_number;
        private String pay_order_id;
        private String tracking_number;
        private String three_channels;
        private String random_code;
    }



}
