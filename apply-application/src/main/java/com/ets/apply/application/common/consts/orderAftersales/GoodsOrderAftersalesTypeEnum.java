package com.ets.apply.application.common.consts.orderAftersales;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsOrderAftersalesTypeEnum {
    SEND_BACK_REFUND(1, "退货退款", 6),

    EXCHANGE(2, "换货", 2),

    REFUND_ONLY(3, "仅退款", 6),

    CANCEL_GOODS_ORDER(4, "取消商品订单", 6);

    private final Integer type;
    private final String name;
    //售后业务类型
    private final Integer orderAfterSalesType;
    
    public static GoodsOrderAftersalesTypeEnum getByType(int type) {
        for (GoodsOrderAftersalesTypeEnum node : GoodsOrderAftersalesTypeEnum.values()) {
            if (node.getType() == type) {
                return node;
            }
        }
        return null;
    }

    public static Integer getOrderAfterSalesByType(int type) {
        for (GoodsOrderAftersalesTypeEnum node : GoodsOrderAftersalesTypeEnum.values()) {
            if (node.getType() == type) {
                return node.getOrderAfterSalesType();
            }
        }
        return 6;
    }

}
