package com.ets.apply.application.common.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
public class GetManualSnOrderListVO {
        private String orderSn;
        private Integer status;
        private Long uid;
        private String plateNo;
        private Integer plateColor;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createdAt;

        /**
         * 售后状态:0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
         */
        private Integer aftersaleStatus;
        /**
         * 激活状态 0未激活，1已激活，2已注销
         */
        private Integer activatedStatus;

}
