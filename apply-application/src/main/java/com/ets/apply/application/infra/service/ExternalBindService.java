package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.common.consts.external.ExternalBindStatusEnum;
import com.ets.apply.application.infra.entity.ExternalBindEntity;
import com.ets.apply.application.infra.mapper.ExternalBindMapper;
import com.ets.common.ToolsHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 外部渠道订单绑定表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Service
@DS("db-etc")
public class ExternalBindService extends BaseService<ExternalBindMapper, ExternalBindEntity> {

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<ExternalBindEntity> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(ExternalBindEntity::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public ExternalBindEntity getByBindSn(String bindSn) {
        return getOneByColumn(bindSn, ExternalBindEntity::getBindSn);
    }

    public List<ExternalBindEntity> getListByExternalUnionId(Integer thirdType, String externalUnionId) {

        LambdaQueryWrapper<ExternalBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalBindEntity::getExternalUnionId, externalUnionId)
                .eq(ExternalBindEntity::getThirdType, thirdType)
                .eq(ExternalBindEntity::getStatus, ExternalBindStatusEnum.NORMAL.getCode());

        return getListByWrapper(wrapper);
    }

    public ExternalBindEntity getValidByOrderSn(Integer thirdType, String bindOrderSn) {

        LambdaQueryWrapper<ExternalBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalBindEntity::getBindOrderSn, bindOrderSn)
                .eq(ExternalBindEntity::getThirdType, thirdType)
                .eq(ExternalBindEntity::getStatus, ExternalBindStatusEnum.NORMAL.getCode());

        return getOneByWrapper(wrapper);
    }

    public void setDeleted(String bindSn) {
        LambdaUpdateWrapper<ExternalBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ExternalBindEntity::getBindSn, bindSn)
                .set(ExternalBindEntity::getStatus, ExternalBindStatusEnum.DELETED.getCode());

        updateByWrapper(wrapper);
    }

    public void cancelByBindOrderSn(String bindOrderSn) {

        LambdaUpdateWrapper<ExternalBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ExternalBindEntity::getBindOrderSn, bindOrderSn)
                .eq(ExternalBindEntity::getStatus, ExternalBindStatusEnum.NORMAL.getCode())
                .set(ExternalBindEntity::getStatus, ExternalBindStatusEnum.CANCELED.getCode());

        updateByWrapper(wrapper);
    }

}
