package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_package_resource")
public class ProductPackageResourceEntity extends BaseEntity<ProductPackageResourceEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 资源配置sn
     */
    private String resourceSn;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源类型[1-普通资源 2-模板资源]
     */
    private Integer resourceType;


    /**
     * 资源状态[0-初始状态 1-上架 2-下架]
     */
    private Integer resourceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资源配置信息
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
