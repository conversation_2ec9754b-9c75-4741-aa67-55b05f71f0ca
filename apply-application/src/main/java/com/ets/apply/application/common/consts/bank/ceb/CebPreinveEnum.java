package com.ets.apply.application.common.consts.bank.ceb;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum CebPreinveEnum {

    REJECT("0", "拒绝"),
    PASS("1", "通过");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        CebPreinveEnum[] enums = CebPreinveEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
