package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ActivityCreditCardOriginalDataLogEntity;
import com.ets.apply.application.infra.mapper.ActivityCreditCardOriginalDataLogMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 信用卡原始银行数据记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Service
@DS("db-etc")
public class ActivityCreditCardOriginalDataLogService extends BaseService<ActivityCreditCardOriginalDataLogMapper, ActivityCreditCardOriginalDataLogEntity> {

    public void addLog(ActivityCreditCardOriginalDataLogEntity logEntity) {
        logEntity.setCreatedAt(LocalDateTime.now());
        logEntity.setUpdatedAt(LocalDateTime.now());
        this.save(logEntity);
    }
}
