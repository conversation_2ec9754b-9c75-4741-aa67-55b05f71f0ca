package com.ets.apply.feign.fallback;

import com.ets.apply.feign.feign.ApplyOrderFeign;
import com.ets.apply.feign.request.ApplyOrderSnsDTO;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.validation.Valid;
import java.util.List;

@Component
@Slf4j
public class ApplyOrderFallbackFactory implements FallbackFactory<ApplyOrderFeign> {

    @Override
    public ApplyOrderFeign create(Throwable cause) {

        return new ApplyOrderFeign() {

            @Override
            public JsonResult<List<OrderResponse>> getOrderListBySn(@Valid ApplyOrderSnsDTO dto) {
                return JsonResult.error("获取申办订单列表失败，请稍后再试：" + cause.getMessage());
            }
        };
    }
}