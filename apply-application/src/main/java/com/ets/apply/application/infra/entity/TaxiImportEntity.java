package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("etc_taxi_import")
public class TaxiImportEntity extends BaseEntity<TaxiImportEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 出租车公司id
     */
    private Integer companyId;

    /**
     * 邮寄联系人
     */
    private String sendName;

    /**
     * 邮寄联系手机
     */
    private String sendPhone;

    /**
     * 邮寄地区
     */
    private String sendArea;

    /**
     * 邮寄地址
     */
    private String sendAddress;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 身份证正面文件url
     */
    private String userIdcardFrontUrl;

    /**
     * 身份证反面文件url
     */
    private String userIdcardBackUrl;

    /**
     * 营业执照文件url
     */
    private String bizLicenseUrl;

    /**
     * 行驶证正面文件url
     */
    private String userVehicleFrontUrl;

    /**
     * 行驶证反面文件url
     */
    private String userVehicleBackUrl;

    /**
     * 车头照url
     */
    private String userVehicleCar;

    /**
     * 身份证姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String number;

    /**
     * 证件有效期
     */
    private String validDate;

    /**
     * 统一社会信用代码
     */
    private String registerNo;

    /**
     * 营业执照公司名称
     */
    private String licenseCompanyName;

    /**
     * 主体类型
     */
    private String companyType;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 成立时间
     */
    private String buildDate;

    /**
     * 营业期限
     */
    private String operatingPeriod;

    /**
     * 经验范围
     */
    private String businessScope;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 经办人手机号
     */
    private String agentPhoneNum;

    /**
     * 负责人手机号
     */
    private String phoneNum;

    /**
     * 导入状态 0:未处理、1：订单导入成功、2：导入失败 3、审核单提交成功 4、审核单提交失败
     */
    private Integer importStatus;

    /**
     * 导入失败原因
     */
    private String failReason;

    /**
     * 关联订单号
     */
    private String orderSn;

    /**
     * 申办类型 1：广西出租车 2、江苏出租车
     */
    private Integer applyType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime activatedAt;


}
