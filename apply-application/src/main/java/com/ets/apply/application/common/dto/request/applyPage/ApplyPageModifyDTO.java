package com.ets.apply.application.common.dto.request.applyPage;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;

@Data
public class ApplyPageModifyDTO {
    /**
     * 页面名称
     */
    @NotBlank(message = "请输入页面名称")
    @Length(min = 1, max = 20, message = "页面名称请输入长度20以内的字符")
    private String pageName;

    /**
     * 备注
     */
    @Length(min = 0, max = 100, message = "备注请输入长度20以内的字符")
    private String remark;

    /**
     * 页面配置信息
     */
    private Object content;

    @NotBlank(message = "配置sn 号不能为空")
    private String pageSn;

    /**
     * 模板单号
     */
    private String templateSn;
}
