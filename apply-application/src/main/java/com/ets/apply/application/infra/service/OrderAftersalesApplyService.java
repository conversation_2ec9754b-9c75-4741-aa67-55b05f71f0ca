package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.orderAftersales.OrderAftersalesStatusEnum;
import com.ets.apply.application.common.consts.orderAftersales.OrderAftersalesTypeEnum;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesApplyDTO;
import com.ets.apply.application.infra.entity.OrderAftersalesApplyEntity;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.mapper.OrderAftersalesApplyMapper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;



/**
 * <p>
 * 订单售后申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Slf4j
@Service
@Component
@DS("db-order-proxy")
public class OrderAftersalesApplyService extends BaseService<OrderAftersalesApplyMapper, OrderAftersalesApplyEntity>  {
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;
    @Autowired
    private CardsService cardsService;
    /*
     * 通过订单号获取非处理失败的一条记录
     */
    public OrderAftersalesApplyEntity getNormalOneByOrderSnAndType(String orderSn, Integer type){
        Wrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
            .eq(OrderAftersalesApplyEntity::getOrderSn, orderSn)
            .eq(OrderAftersalesApplyEntity::getType, type)
            .notIn(OrderAftersalesApplyEntity::getStatus, Arrays.asList(
                    OrderAftersalesStatusEnum.STATUS_FAIL.getStatus()
            ));
        return super.baseMapper.selectOne(wrapper);
    }
    /*
     * 获取最新的一条申请记录
     */
    public OrderAftersalesApplyEntity getLatestByOrderSnAndType(String orderSn, Integer type,Long uid){
        LambdaQueryWrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
                .eq(OrderAftersalesApplyEntity::getOrderSn, orderSn)
                .eq(OrderAftersalesApplyEntity::getType, type)
                .eq(OrderAftersalesApplyEntity::getUid, uid)
                .orderByDesc(OrderAftersalesApplyEntity::getCreatedAt);
        return getOneByWrapper(wrapper);
    }

    /*
     * 新增申请记录
     */
    public OrderAftersalesApplyEntity addNew(OrderAftersalesApplyDTO dto,OrderOrderEntity orderOrder){
        //创建申请单
        OrderAftersalesApplyEntity  orderAftersalesApply = new OrderAftersalesApplyEntity();
        orderAftersalesApply.setApplySn(ToolsHelper.genNum(redisPermanentTemplate, "OrderAftersalesApplyEntity", "prod", 8));
        orderAftersalesApply.setOrderSn(orderOrder.getOrderSn());
        orderAftersalesApply.setType(dto.getType());
        orderAftersalesApply.setUid(orderOrder.getUid());
        orderAftersalesApply.setPlateNo(orderOrder.getPlateNo());
        orderAftersalesApply.setReason(dto.getReason());
        orderAftersalesApply.setPlateColor(orderOrder.getPlateColor());
        orderAftersalesApply.setStatus(OrderAftersalesStatusEnum.STATUS_APPLY.getStatus());
        orderAftersalesApply.setIssuerCode(cardsService.getIssuerCodeById(orderOrder.getCardId()));
        orderAftersalesApply.setApplyAt(LocalDateTime.now());
        this.create(orderAftersalesApply);
        return orderAftersalesApply;
    }

    /*
     * 变更申请记录
     */
    public Integer modify(String applySn,Integer status,Integer businessStatus,String error){
        LambdaUpdateWrapper<OrderAftersalesApplyEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderAftersalesApplyEntity::getApplySn, applySn)
                .set(OrderAftersalesApplyEntity::getStatus,status)
                .set(OrderAftersalesApplyEntity::getBusinessStatus,businessStatus)
                .set(OrderAftersalesApplyEntity::getError,error);
        //todo 增加日志记录
        return this.updateByWrapper(wrapper);
    }


    /*
     * 获取一条处理中的售后单，非完成和取消状态
     */
    public OrderAftersalesApplyEntity getUnFinishOneByOrderSn(String orderSn, Long uid){
        Wrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
                .eq(OrderAftersalesApplyEntity::getOrderSn, orderSn)
                .eq(OrderAftersalesApplyEntity::getUid, uid)
                .notIn(OrderAftersalesApplyEntity::getStatus, Arrays.asList(
                        OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus()
                ))
                .orderByDesc(OrderAftersalesApplyEntity::getCreatedAt);
        return super.baseMapper.selectOne(wrapper);
    }

    /*
     * 获取最新的一条申请记录
     */
    public OrderAftersalesApplyEntity getLatestByOrderSn(String orderSn){
        LambdaQueryWrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
                .eq(OrderAftersalesApplyEntity::getOrderSn, orderSn)
                .notIn(OrderAftersalesApplyEntity::getStatus, Arrays.asList(
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus()
                ))
                .orderByDesc(OrderAftersalesApplyEntity::getCreatedAt);
        return getOneByWrapper(wrapper);
    }


    /*
     * 变更申请记录
     */
    public Integer modifyBusinessStatus(String applySn,Integer businessStatus,String error){
        LambdaUpdateWrapper<OrderAftersalesApplyEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderAftersalesApplyEntity::getApplySn, applySn)
                .set(OrderAftersalesApplyEntity::getBusinessStatus,businessStatus)
                .set(OrderAftersalesApplyEntity::getError,error);
        //todo 增加日志记录
        return this.updateByWrapper(wrapper);
    }

    /*
     * 变更申请记录
     */
    public Integer modifyStatus(String applySn,Integer status,String error){
        LambdaUpdateWrapper<OrderAftersalesApplyEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderAftersalesApplyEntity::getApplySn, applySn)
                .set(OrderAftersalesApplyEntity::getStatus,status)
                .set(OrderAftersalesApplyEntity::getError,error);
        //todo 增加日志记录
        return this.updateByWrapper(wrapper);
    }



    /*
     * 变更业务状态
     */
    public Integer updateBusinessStatus(String applySn,Integer bussinessStatus){
        LambdaUpdateWrapper<OrderAftersalesApplyEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderAftersalesApplyEntity::getApplySn, applySn)
                .set(OrderAftersalesApplyEntity::getBusinessStatus,bussinessStatus);
        return this.updateByWrapper(wrapper);
    }
    /*
     * 获取最新的一条申请记录
     */
    public OrderAftersalesApplyEntity getLatestByOrderSnAndType(String orderSn, Integer type){
        LambdaQueryWrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
                .eq(OrderAftersalesApplyEntity::getOrderSn, orderSn)
                .eq(OrderAftersalesApplyEntity::getType, type)
                .notIn(OrderAftersalesApplyEntity::getStatus, Arrays.asList(
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus()
                ))
                .orderByDesc(OrderAftersalesApplyEntity::getCreatedAt);
        return getOneByWrapper(wrapper);
    }


    /*
     * 获取最新的一条申请记录
     */
    public OrderAftersalesApplyEntity getByApplySn(String applySn){
        LambdaQueryWrapper<OrderAftersalesApplyEntity> wrapper = Wrappers.<OrderAftersalesApplyEntity>lambdaQuery()
                .eq(OrderAftersalesApplyEntity::getApplySn, applySn);
        return getOneByWrapper(wrapper);
    }
}
