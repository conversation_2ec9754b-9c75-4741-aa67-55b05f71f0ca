package com.ets.apply.application.app.business.plateNo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ets.apply.application.app.thirdservice.feign.CallPhpIssuerFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPhpUserFeign;
import com.ets.apply.application.common.config.PlateNoCheckUniqueConfig;
import com.ets.apply.application.common.consts.apply.ExceptionEnum;
import com.ets.apply.application.common.consts.issuer.IssuerCheckPlateNoCode;
import com.ets.apply.application.common.consts.plateNo.PlateNoCarTypeEnum;
import com.ets.apply.application.common.consts.plateNo.PlateNoOccupyStatusEnum;
import com.ets.apply.application.common.consts.validatePlateNo.ValidatePlateNoEnum;
import com.ets.apply.application.common.consts.validatePlateNo.ValidatePlateNoTermEnum;
import com.ets.apply.application.common.dto.request.issuer.IssuerCheckPlateNoUniqueDTO;
import com.ets.apply.application.common.dto.request.plateNo.PlateNoCheckUniqueDTO;
import com.ets.apply.application.common.dto.request.user.PlateNoCheckDuplicateDTO;
import com.ets.apply.application.common.vo.plateNo.PlateNoCheckUniqueVO;
import com.ets.apply.application.common.vo.response.issuer.CheckIssuerPlateUniqueVO;
import com.ets.apply.application.common.vo.response.user.UserCheckPlateDuplicateVO;
import com.ets.apply.application.infra.entity.ValidatePlateNoEntity;
import com.ets.apply.application.infra.service.ValidatePlateNoService;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PlateNoBusiness {


    @Autowired
    private CallPhpUserFeign phpUserFeign;

    @Autowired
    private CallPhpIssuerFeign issuerFeign;

    @Autowired
    private PlateNoCheckUniqueConfig plateNoCheckUniqueConfig;
    @Autowired
    private ValidatePlateNoService validatePlateNoService;

    public PlateNoCheckUniqueVO plateNoCheck(PlateNoCheckUniqueDTO checkUniqueDTO) {
        // 获取信息
        PlateNoCheckUniqueVO checkUniqueVO = new PlateNoCheckUniqueVO();
        try {
            // 检查我方卡签
            if (checkUserCardPlateDuplicate(checkUniqueDTO).equals(true)) {
                checkUniqueVO.setOccupyStatus(PlateNoOccupyStatusEnum.OCCUPY.getStatus());
                return checkUniqueVO;
            }
            // 检查卡方车牌是否占用
            if (checkIssuerPlateUnique(checkUniqueDTO).equals(false)) {
                checkUniqueVO.setOccupyStatus(PlateNoOccupyStatusEnum.OCCUPY.getStatus());
                return checkUniqueVO;
            }
            // 返回结果
            checkUniqueVO.setOccupyStatus(PlateNoOccupyStatusEnum.NOT_OCCUPY.getStatus());
        } catch (BizException bizException) {
            throw bizException;
        } catch (Exception e) {
            log.info("plateNoCheck 车牌校验异常：" + e.getMessage());
            ToolsHelper.throwException(ExceptionEnum.BUSINESS_FAIL.getMessage(), ExceptionEnum.BUSINESS_FAIL.getCode());
        }

        return checkUniqueVO;

    }

    public Boolean checkUserCardPlateDuplicate(PlateNoCheckUniqueDTO checkUniqueDTO) {
        try {
            PlateNoCheckDuplicateDTO duplicateDTO = new PlateNoCheckDuplicateDTO();
            duplicateDTO.setPlateNo(checkUniqueDTO.getPlateNo());
            duplicateDTO.setPlateColor(checkUniqueDTO.getPlateColor());
            String jsonResult = phpUserFeign.checkPlateDuplicate(duplicateDTO);
            JsonResult<UserCheckPlateDuplicateVO> result = JsonResult.convertFromJsonStr(jsonResult, UserCheckPlateDuplicateVO.class);
            result.checkError();
            return result.getData().getDuplicated();
        } catch (Exception e) {
            log.info("车牌校验checkUserCardPlateDuplicate异常：" + e.getMessage());
            ToolsHelper.throwException(ExceptionEnum.ISSUER_PLATE_NO_CHECK_FAIL.getMessage(), ExceptionEnum.ISSUER_PLATE_NO_CHECK_FAIL.getCode());
        }
        return false;

    }

    /**
     * 车牌占用校验 默认北京速通
     *
     */
    public Boolean checkIssuerPlateUnique(PlateNoCheckUniqueDTO checkUniqueDTO) {

        IssuerCheckPlateNoUniqueDTO issuerCheckPlateNoUniqueDTO = new IssuerCheckPlateNoUniqueDTO();
        issuerCheckPlateNoUniqueDTO.setOwner("高小灯");
        issuerCheckPlateNoUniqueDTO.setPlateColor(checkUniqueDTO.getPlateColor());
        issuerCheckPlateNoUniqueDTO.setPlateNo(checkUniqueDTO.getPlateNo());
        issuerCheckPlateNoUniqueDTO.setCarType(PlateNoCarTypeEnum.car.getCode());
        String issuerCode = plateNoCheckUniqueConfig.getIssuerCode();
        // 检查缓存
        if (plateNoCheckUniqueConfig.getCheckWithCache()) {
            ValidatePlateNoEntity validatePlateNoEntity = validatePlateNoService.getValidatePlateNo(
                    issuerCheckPlateNoUniqueDTO.getPlateNo(),
                    issuerCheckPlateNoUniqueDTO.getPlateColor(),
                    issuerCode,
                    LocalDateTime.now()
            );
            if (ObjectUtil.isNotNull(validatePlateNoEntity)) {
                if (validatePlateNoEntity.getValidateStatus().equals(ValidatePlateNoEnum.VALIDATE_STATUS_SUCCESS.getCode())) {
                    return true;
                } else if (validatePlateNoEntity.getValidateStatus().equals(ValidatePlateNoEnum.VALIDATE_STATUS_FAIL.getCode())) {
                    return false;
                }
            }
        }
        // 请求卡方检查
        String jsonResult = issuerFeign.checkPlateNoUnique(issuerCode, issuerCheckPlateNoUniqueDTO);
        JsonResult<CheckIssuerPlateUniqueVO> result = JsonResult.convertFromJsonStr(jsonResult, CheckIssuerPlateUniqueVO.class);
        if (result.getCode().equals(IssuerCheckPlateNoCode.OCCUPY.getCode())) {
            //新增车牌校验记录
            ValidatePlateNoEntity validatePlateNoEntity = new ValidatePlateNoEntity();
            validatePlateNoEntity.setTerm(ValidatePlateNoTermEnum.NORMAL.getTerm());
            validatePlateNoEntity.setPlateNo(issuerCheckPlateNoUniqueDTO.getPlateNo());
            validatePlateNoEntity.setPlateColor(issuerCheckPlateNoUniqueDTO.getPlateColor());
            validatePlateNoEntity.setValidateStatus(ValidatePlateNoEnum.VALIDATE_STATUS_FAIL.getCode());
            validatePlateNoEntity.setValidateResult(StrUtil.sub(result.getMsg(), 0, 255));
            validatePlateNoEntity.setExpiryDate(DateUtil.offsetSecond(DateUtil.date(), plateNoCheckUniqueConfig.getEffectSeconds()).toTimestamp().toLocalDateTime());
            validatePlateNoEntity.setIssuerCode(issuerCode);
            validatePlateNoService.create(validatePlateNoEntity);
            return false;
        } else if (result.getCode().equals(IssuerCheckPlateNoCode.NOT_OCCUPY.getCode())) {
            //新增车牌校验记录
            ValidatePlateNoEntity validatePlateNoEntity = new ValidatePlateNoEntity();
            validatePlateNoEntity.setTerm(ValidatePlateNoTermEnum.NORMAL.getTerm());
            validatePlateNoEntity.setPlateNo(issuerCheckPlateNoUniqueDTO.getPlateNo());
            validatePlateNoEntity.setPlateColor(issuerCheckPlateNoUniqueDTO.getPlateColor());
            validatePlateNoEntity.setValidateStatus(ValidatePlateNoEnum.VALIDATE_STATUS_SUCCESS.getCode());
            validatePlateNoEntity.setValidateResult("可以办理");
            validatePlateNoEntity.setIssuerCode(issuerCode);
            validatePlateNoEntity.setExpiryDate(DateUtil.offsetSecond(DateUtil.date(), plateNoCheckUniqueConfig.getEffectSeconds()).toTimestamp().toLocalDateTime());
            validatePlateNoService.create(validatePlateNoEntity);
            return true;
        } else {
            log.info("车牌校验checkUserCardPlateDuplicate异常：" + result.getMsg());
            ToolsHelper.throwException(result.getMsg(), ExceptionEnum.ISSUER_PLATE_NO_CHECK_FAIL.getCode());
        }
        return false;
    }
}
