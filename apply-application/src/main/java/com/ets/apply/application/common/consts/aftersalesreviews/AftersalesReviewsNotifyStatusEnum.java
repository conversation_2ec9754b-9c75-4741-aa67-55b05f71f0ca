package com.ets.apply.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * 售后审核通知状态枚举
 */
@Getter
@AllArgsConstructor
public enum AftersalesReviewsNotifyStatusEnum {
    
    DEFAULT(0, "未通知"),
    SUCCESS(1, "通知成功"),
    FAILED(2, "通知失败"),
    CANCELED(3, "已取消");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        AftersalesReviewsNotifyStatusEnum[] enums = AftersalesReviewsNotifyStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
