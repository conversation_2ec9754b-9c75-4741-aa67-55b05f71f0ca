package com.ets.apply.application.common.consts.userCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SettleCycleEnum {

    Day(1, "日结卡"),

    Week(2, "周结卡"),

    Month(3, "月结卡"),

    Webank(4, "微众日结卡"),

    ByPlateNo(5, "按车牌日结卡");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (SettleCycleEnum node : SettleCycleEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
