package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.BaseApplicationFeign;
import com.ets.apply.application.app.thirdservice.response.IpAddressInfoVO;
import com.ets.base.feign.request.sms.SendCodeRequestDTO;
import com.ets.base.feign.request.sms.VerifyCodeRequestDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class BaseApplicationFallbackFactory implements FallbackFactory<BaseApplicationFeign> {
    @Override
    public BaseApplicationFeign create(Throwable throwable) {
        return new BaseApplicationFeign() {
            @Override
            public JsonResult<IpAddressInfoVO> getIpAddressInfo(String ip) {
                return JsonResult.error("ip:" + ip + " 请求获取ip地址信息失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<HashMap<String, String>> sendCode(SendCodeRequestDTO sendCodeDTO) {
                return JsonResult.error("请求获取验证码失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<Object> verifyCode(VerifyCodeRequestDTO requestDTO) {
                return JsonResult.error("请求校验验证码失败：" + throwable.getMessage());
            }
        };
    }
}
