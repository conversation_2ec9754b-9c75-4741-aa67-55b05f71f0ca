package com.ets.apply.application.common.consts.activityCreditCardOperateLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum CreditCardOperateLogTypeEnum {

    TYPE_APPLY("apply", "申请"),
    TYPE_AUDIT("audit", "审核"),
    TYPE_ACTIVATE("activate", "激活"),
    TYPE_FIRST_USE("first_use", "首刷"),
    TYPE_ACTION_RELEASE("release", "解除绑定关联"),


    ;

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        CreditCardOperateLogTypeEnum[] enums = CreditCardOperateLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
