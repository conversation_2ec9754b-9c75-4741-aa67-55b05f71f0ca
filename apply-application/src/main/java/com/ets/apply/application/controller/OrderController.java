package com.ets.apply.application.controller;


import com.ets.apply.application.common.dto.request.order.OrderFindDTO;
import com.ets.apply.application.common.dto.request.order.AuthorizeActivateOrderDetailDTO;
import com.ets.apply.application.common.dto.request.order.AuthorizeActivateOrderListDTO;
import com.ets.apply.application.app.business.orderAftersales.OrderAftersalesBusiness;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesCheckDTO;
import com.ets.apply.application.common.vo.UserSourceInfoVo;
import com.ets.apply.application.common.vo.order.*;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.feign.feign.OrderFeign;
import com.ets.apply.application.app.business.*;
import com.ets.apply.application.common.dto.request.order.EffectOrderListDTO;
import com.ets.apply.feign.request.FindOneOrderReq;
import com.ets.apply.feign.request.OrderListDTO;
import com.ets.apply.application.common.vo.response.order.EffectOrderResponse;
import com.ets.apply.feign.response.OrderAddressResponse;
import com.ets.apply.feign.response.OrderPackageInfoResponse;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import com.ets.common.annotation.RateLimiterAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 订单 微服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@RequestMapping("/order")
@RefreshScope
@RestController
@Slf4j
@Validated
public class OrderController extends BaseController implements OrderFeign {

    @Autowired
    private OrderBusiness orderBusiness;
    @Autowired
    private OrderOrderService orderService;

    @Override
    @RequestMapping("/getUserValidCarOrdersByUid")
    public JsonResult<?> getUserValidCarOrdersByUid(
            @NotNull(message = "用户Uid不能为空") @RequestParam(value = "uid", required = false) Long uid
    ) {
        return JsonResult.ok(orderBusiness.getUserHaveMaterialsValidCarOrders(uid));
    }

    @Override
    @RequestMapping("/findOneByPlate")
    public JsonResult<OrderResponse> findOneByPlate(@Valid @RequestBody FindOneOrderReq req) {
        return JsonResult.ok(orderBusiness.findOneByPlate(req));
    }

    @Override
    @RequestMapping("/getOrderByOrderSn")
    public JsonResult<OrderResponse> getOrderByOrderSn(
            @NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn
    ) {
        return JsonResult.ok(orderBusiness.finOneByOrderSn(orderSn));
    }

    /**
     * 根据订单号查询用户信息
     * @param orderSn
     * @return
     */
    @RequestMapping("/getUserInfoByOrderSn")
    public JsonResult<UserSourceInfoVo> getUserInfoByOrderSn(
            @RequestParam(value = "orderSn") String orderSn
    ) {
        return JsonResult.ok(orderBusiness.getUserInfoByOrderSn(orderSn));
    }

    @Override
    @RequestMapping("/getOrderListByPlate")
    public JsonResult<List<OrderResponse>> getOrderListByPlate(@RequestBody @Valid OrderListDTO orderListDTO) {
        return JsonResult.ok(orderBusiness.getOrderListByPlate(orderListDTO));
    }

    @Override
    @RateLimiterAnnotation(qps = 200,msg = "qps[200]超过限制，请稍后再试")
    @RequestMapping("/getOrderAddressByOrderSn")
    public JsonResult<OrderAddressResponse> getOrderAddressByOrderSn(
            @NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn
    ) {
        return JsonResult.ok(orderBusiness.getOrderAddressByOrderSn(orderSn));
    }

    @Override
    @PostMapping("/getOrderPackageInfoByOrderSn")
    public JsonResult<OrderPackageInfoResponse> getOrderPackageInfoByOrderSn(
            @NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn") String orderSn) {
        return JsonResult.ok(orderBusiness.getOrderPackageInfoByOrderSn(orderSn));
    }


    @RequestMapping("/getOrderInfoByOrderSn")
    public JsonResult<OrderInfoVo> getOrderInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getOrderInfoByOrderSn(order));
    }
    @RequestMapping("/getVehicleInfoByOrderSn")
    public JsonResult<GetVehicleInfoByOrderSnVo> getVehicleInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getVhicleInfoByOrderSn(order));
    }
    @RequestMapping("/getIdCardInfoByOrderSn")
    public JsonResult<GetIdCardInfoByOrderSnVo> getIdCardInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getIdCardInfoByOrderSn(order));
    }

    @RequestMapping("/getBizlicenceInfoByOrderSn")
    public JsonResult<GetBizlicenceInfoByOrderSnVo> getBizlicenceInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getBizlicenceInfoByOrderSn(order));
    }

    @RequestMapping("/getIssueInfoByOrderSn")
    public JsonResult<GetIssueInfoByOrderSnVo> getIssueInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getIssueInfoByOrderSn(order));
    }

    /**
     * 获取有效的订单列表（包含已注销）-活动使用
     *  <a href="https://yapi.etczs.net/project/312/interface/api/29124">yapi</a>
     */

    @RateLimiterAnnotation(qps = 200,msg = "qps[200]超过限制，请稍后再试")
    @RequestMapping("/getEffectOrderListByUid")
    public JsonResult<List<EffectOrderResponse>> getEffectOrderListByUid(@RequestBody @Valid EffectOrderListDTO effectOrderListDTO) {
        return JsonResult.ok(orderBusiness.getEffectOrderListByUid(effectOrderListDTO));
    }

    /**
     * 获取待激活的订单
     * 产品要求已激活的也返回，有业务方判定是否已激活再进行对应的处理
     * wiki:<a href="https://yapi.etczs.net/project/312/interface/api/31497">获取待激活的订单</a>
     *
     */
    @RequestMapping("/getAuthorizeActivateOrderList")
    public JsonResult<List<AuthorizeActivateOrderDetailVO>> getAuthorizeActivateOrderList(@RequestBody @Valid AuthorizeActivateOrderListDTO dto) {
        return JsonResult.ok(orderBusiness.getAuthorizeActivateOrderListVO(dto));
    }

    /**
     * 获取待激活订单详情
     * yapi :<a href="https://yapi.etczs.net/project/312/interface/api/31503">获取待激活订单详情</a>
     */
    @RequestMapping("/getAuthorizeActivateOrderDetail")
    public JsonResult<AuthorizeActivateOrderDetailVO> getAuthorizeActivateOrderDetail(@RequestBody @Valid AuthorizeActivateOrderDetailDTO dto) {
       return JsonResult.ok(orderBusiness.getAuthorizeActivateOrderDetailVO(dto));
    }

    /**
     * 查询订单退款状态
     *
     */
    @RequestMapping("/getRefundInfoByOrderSn")
    public JsonResult<GetRefundInfoByOrderSnVo> getRefundInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        return JsonResult.ok(orderBusiness.getRefundInfoByOrderSn(dto));
    }
}
