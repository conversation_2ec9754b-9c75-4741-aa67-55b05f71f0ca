package com.ets.apply.application.common.dto.request.wecar;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

@Data
public class WecarGetOrderListDTO {
    @NotNull(message = "companyId不能为空")
    private String companyId;
    @NotNull(message = "腾讯出⾏⽤户标识不能为空")
    private String userCode;
    @Min(1)
    private Integer pageNum = 1;

    @Min(100)
    private Integer pageSize = 100;

}
