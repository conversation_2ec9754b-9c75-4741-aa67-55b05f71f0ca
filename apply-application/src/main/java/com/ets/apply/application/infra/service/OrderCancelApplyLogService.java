package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.OrderCancelApplyLog;
import com.ets.apply.application.infra.mapper.OrderCancelApplyLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
@DS("db-order")
public class OrderCancelApplyLogService extends BaseService<OrderCancelApplyLogMapper, OrderCancelApplyLog> {

}
