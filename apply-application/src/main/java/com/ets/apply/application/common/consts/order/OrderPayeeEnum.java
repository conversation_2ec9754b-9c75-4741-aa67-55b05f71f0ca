package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderPayeeEnum {

    // * 售后状态:0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
    ORDER_PAYEE_APPLY("apply", "申办收款"),
    ORDER_PAYEE_PRODUCT_ORDER("product_order", "电商收款"),
    ORDER_PAYEE_GOODS("goods", "商品收款"),
    ORDER_PAYEE_AFTERSALES("aftersales", "售后收款");

    private final String value;
    private final String desc;

    public static OrderPayeeEnum getByCode(String code) {
        for (OrderPayeeEnum node : OrderPayeeEnum.values()) {
            if (node.getValue().equals(code)) {
                return node;
            }
        }
        return null;
    }
}
