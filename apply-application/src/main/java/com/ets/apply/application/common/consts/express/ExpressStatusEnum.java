package com.ets.apply.application.common.consts.express;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpressStatusEnum {

    OnWay(0, "在途"),

    Collect(1, "揽收"),

    Problem(2, "疑难"),

    Signed(3, "签收"),

    BackSign(4, "退签"),

    Delivery(5, "派件"),

    Back(6, "退回"),

    Exchange(7, "转单"),

    WaitAccount(10, "待清关"),

    Accounting(11, "清关中"),

    Accounted(12, "已清关"),

    AccountFail(13, "清关异常"),

    Reject(14, "收件人拒签");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ExpressStatusEnum node : ExpressStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
