package com.ets.apply.application.common.dto.request.productOrder;

import com.ets.common.annotation.PhoneAnnotation;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class ProductOrderModifyDTO {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String productOrderSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 收货手机
     */
    @PhoneAnnotation
    private String sendPhone;

    /**
     * 办理手机号
     */
    @PhoneAnnotation
    private String etcPhone;

    /**
     * 实际销售价格
     */
    @Max(value = 99999999, message = "实际销售价格不能超过99999999")
    @Min(value = 0, message = "实际销售价格不能小于0")
    private BigDecimal paidAmount;

}
