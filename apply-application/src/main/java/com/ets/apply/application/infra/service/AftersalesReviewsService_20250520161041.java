package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.mapper.AftersalesReviewsMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsService extends BaseService<AftersalesReviewsMapper, AftersalesReviews> {

}
