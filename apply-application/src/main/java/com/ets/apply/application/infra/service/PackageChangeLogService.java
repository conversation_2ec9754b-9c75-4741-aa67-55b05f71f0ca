package com.ets.apply.application.infra.service;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.PackageChangeLogEntity;
import com.ets.apply.application.infra.mapper.PackageChangeLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品包相关配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Service
@DS("db-apply")
public class PackageChangeLogService extends BaseService<PackageChangeLogMapper, PackageChangeLogEntity> {

}
