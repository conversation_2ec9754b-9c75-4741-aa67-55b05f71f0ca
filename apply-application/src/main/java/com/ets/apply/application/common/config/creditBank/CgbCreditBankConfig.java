package com.ets.apply.application.common.config.creditBank;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "credit-bank.cgb")
public class CgbCreditBankConfig {

    private String cgbSm2PublicKey;

    private String etcSm2PrivateKey;

    private String etcSm2PublicKey;

    private String channel;

    /** =================== 以下为广发提供给高灯的参数 =================== */
    // 商品信息
    private String productId;

    /**
     * 公路代码
     */
    private String highroadCode;

    /**
     * 第三个通道
     */
    private String thirdChannel;

    
    /**
     * 协议
     */
    private String scheme;

    private Integer port;

    /**
     * 申请url
     */
    private String cgbUrl;

    /**
     * 申请订单url
     */
    private String applyOrderUrl;

    /**
     * 内部版本
     */
    private String innerVersion;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 老用户失败原因
     */
    private String oldUserFailReason;

    /**
     * 广发申请全地址
     */
    private String cgbHostUrl;

    /**
     * 新版本
     */
    private Boolean newVersion;
}
