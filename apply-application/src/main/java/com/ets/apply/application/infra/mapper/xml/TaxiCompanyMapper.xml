<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ets.apply.application.infra.mapper.TaxiCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ets.apply.application.infra.entity.TaxiCompanyEntity">
        <id column="id" property="id" />
        <result column="company_name" property="companyName" />
        <result column="phone" property="phone" />
        <result column="send_area" property="sendArea" />
        <result column="address" property="address" />
        <result column="license" property="license" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="region_id" property="regionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_name, phone, send_area, address, license, created_at, updated_at, region_id
    </sql>

    <select id="getAllCompanyName" resultType="com.ets.apply.application.entity.TaxiCompanyEntity">
        select id, company_name from etc_taxi_company
    </select>
</mapper>
