package com.ets.apply.application.common.dto.productPackagePopup;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PopupSetFirstChooseDTO {
    /**
     *
     */

    @NotBlank(message = "弹窗sn不能为空")
    private String popupSn;
}
