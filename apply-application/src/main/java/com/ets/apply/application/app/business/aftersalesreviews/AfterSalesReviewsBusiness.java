package com.ets.apply.application.app.business.aftersalesreviews;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer;
import com.ets.apply.application.app.thirdservice.feign.AfterSalesReviewsNotifyFeign;
import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewAutoAuditBO;
import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewsNotifyBO;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsNotifyStatusEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.common.dto.aftersalesreviews.*;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsDetailVO;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsListVO;
import com.ets.apply.application.common.vo.aftersalesreivews.AfterSalesReviewsVO;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsLogService;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.starter.queue.QueueDefault;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 售后审核单业务逻辑
 */
@Slf4j
@Component
public class AfterSalesReviewsBusiness {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private QueueDefault queue;

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Autowired
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    @Autowired
    private AfterSalesReviewsNotifyFeign afterSalesReviewsNotifyFeign;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    /**
     * 创建售后审核单
     *
     * @param dto 创建售后审核单请求参数
     * @return 售后审核单
     */
    public AfterSalesReviewsVO createAfterSalesReview(CreateAfterSalesReviewDTO dto) {
        // 检查业务单是否存在待审核记录
        AftersalesReviews reviews = aftersalesReviewsService.getByOrderSn(dto.getOrderSn());
        if (reviews != null && reviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.PENDING.getValue())) {
            ToolsHelper.throwException("该业务单已存在待审核记录，请勿重复提交");
        }

        // 创建审核单
        AftersalesReviews aftersalesReviews = BeanUtil.copyProperties(dto, AftersalesReviews.class);

        // 生成审核单流水号
        String reviewSn = ToolsHelper.genNum(redisPermanentTemplate, "AfterSalesReviews", active, 8);

        // 保存审核行驶证信息
        if (dto.getReviewVehicleInfo() != null) {
            // 校验必传参数
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getFrontImgUrl())) {
                ToolsHelper.throwException("审核行驶证印章页照片不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getBackImgUrl())) {
                ToolsHelper.throwException("审核行驶证条码页照片不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getFrontCarImgUrl())) {
                ToolsHelper.throwException("审核行驶证车头照不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getGearActivateImgUrl())) {
                ToolsHelper.throwException("审核行驶证车内照不能为空");
            }
            saveVehicleInfo(reviewSn, dto.getReviewVehicleInfo(), AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue());
        }

        // 保存订单行驶证信息
        if (dto.getOrderVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getOrderVehicleInfo(), AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue());
        }

        // 设置属性
        aftersalesReviews.setReviewSn(reviewSn);
        aftersalesReviews.setApplyTime(LocalDateTime.now());
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.PENDING.getValue()); // 待审核
        aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.DEFAULT.getValue()); // 未通知

        // 保存审核单
        aftersalesReviewsService.create(aftersalesReviews);

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(reviewSn);
        reviewsLog.setOperateContent("创建售后审核");
        reviewsLog.setOperator("system");
        reviewsLog.setType("create");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("创建售后审核单成功，审核单号：{}", reviewSn);

        // 异步判断是否需要人工审核
        AfterSalesReviewAutoAuditBO bo = new AfterSalesReviewAutoAuditBO();
        bo.setReviewSn(aftersalesReviews.getReviewSn());
        queue.push(new AfterSalesReviewAutoAuditDisposer(bo));

        return BeanUtil.copyProperties(aftersalesReviews, AfterSalesReviewsVO.class);
    }

    /**
     * 保存行驶证信息
     *
     * @param reviewSn 审核单号
     * @param vehicleInfo 行驶证信息
     * @param dataType 数据类型 [1-审核资料 2-申办资料]
     */
    private void saveVehicleInfo(String reviewSn, CreateAfterSalesReviewDTO.VehicleInfoDTO vehicleInfo, Integer dataType) {
        AftersalesReviewsVehicles vehicle = BeanUtil.copyProperties(vehicleInfo, AftersalesReviewsVehicles.class);
        vehicle.setReviewSn(reviewSn);
        vehicle.setDataType(dataType);

        aftersalesReviewsVehiclesService.create(vehicle);
    }

    /**
     * 取消售后审核单
     *
     * @param dto 取消售后审核单请求参数
     */
    public void cancelAfterSalesReview(CancelAfterSalesReviewDTO dto) {
        // 加锁，防止并发操作
        String lockKey = "cancelAfterSalesReview:" + dto.getReviewSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("审核单取消操作进行中，请稍后再试");
        }

        try {
            // 查询审核单
            AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
            if (aftersalesReviews == null) {
                ToolsHelper.throwException("审核单不存在");
            }

        // 检查审核单状态
        if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
            return;
        }

        if (Arrays.asList(
                AftersalesReviewsStatusEnum.APPROVED.getValue(),
                AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
        ) {
            ToolsHelper.throwException("审核单已有审核结果，无法取消");
        }

        // 更新审核单状态
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.CANCELED.getValue()); // 已取消
        aftersalesReviews.setReviewRemark(dto.getCancelReason());
        aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.CANCELED.getValue()); // 已取消
        aftersalesReviews.setUpdatedAt(LocalDateTime.now());

        // 保存审核单
        aftersalesReviewsService.updateById(aftersalesReviews);

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(dto.getReviewSn());
        reviewsLog.setOperateContent("取消售后审核，取消原因：" + dto.getCancelReason());
        reviewsLog.setOperator("system");
        reviewsLog.setType("cancel");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("取消售后审核单成功，审核单号：{}", dto.getReviewSn());
    }

    public IPage<AdminAfterSalesReviewsListVO> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 分页查询
        IPage<AftersalesReviews> reviewsPage = aftersalesReviewsService.getList(dto);

        //todo 获取issuerIdNameMap
        Map<Integer, String> issuerIdNameMap = new HashMap<>();

        // 转换为VO对象
        return reviewsPage.convert(reviews -> {
            AdminAfterSalesReviewsListVO vo = new AdminAfterSalesReviewsListVO();
            BeanUtil.copyProperties(reviews, vo);
            vo.setIssuerName(issuerIdNameMap.getOrDefault(reviews.getIssuerId(), ""));
            return vo;
        });
    }

    public AdminAfterSalesReviewsDetailVO getDetail(AdminAfterSalesReviewsDetailDTO dto) {
        // 查询审核单
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("审核单不存在");
        }

        // 查询审核单行驶证信息
        List<AftersalesReviewsVehicles> vehiclesList = aftersalesReviewsVehiclesService.getByReviewSn(aftersalesReviews.getReviewSn());
        if (ObjectUtils.isEmpty(vehiclesList)) {
            ToolsHelper.throwException("行驶证数据不存在");
        }

        // 获取审核资料和申办资料的数据
        AftersalesReviewsVehicles reviewData = vehiclesList.stream()
                .filter(vehicle -> AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue().equals(vehicle.getDataType()))
                .findFirst()
                .orElse(null);

        AftersalesReviewsVehicles orderData = vehiclesList.stream()
                .filter(vehicle -> AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue().equals(vehicle.getDataType()))
                .findFirst()
                .orElse(null);

        AdminAfterSalesReviewsDetailVO vo = new AdminAfterSalesReviewsDetailVO();
        BeanUtil.copyProperties(aftersalesReviews, vo);
        vo.setReviewVehicleInfo(BeanUtil.copyProperties(reviewData, AdminAfterSalesReviewsDetailVO.VehicleInfoDTO.class));
        vo.setOrderVehicleInfo(BeanUtil.copyProperties(orderData, AdminAfterSalesReviewsDetailVO.VehicleInfoDTO.class));

        return vo;
    }

    public void review(AdminAfReviewsReviewDTO dto) {
        // 获取审核单数据
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("审核单不存在");
        }

        // 检查审核单状态
        if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
            ToolsHelper.throwException("审核单已取消，无法审核");
        }

        if (Arrays.asList(
                AftersalesReviewsStatusEnum.APPROVED.getValue(),
                AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
        ) {
            ToolsHelper.throwException("审核单已有审核结果，无法重复审核");
        }

        if (!aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.PENDING.getValue())) {
            ToolsHelper.throwException("只有待审核状态的审核单才能进行审核");
        }

        // 校验审核状态参数
        if (!Arrays.asList(
                AftersalesReviewsStatusEnum.APPROVED.getValue(),
                AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(dto.getReviewStatus())
        ) {
            ToolsHelper.throwException("审核状态参数错误，只能设置为审核通过或审核驳回");
        }

        // 如果是审核驳回，检查拒绝原因
        if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.REJECTED.getValue())) {
            if (StringUtils.isBlank(dto.getRejectType())) {
                ToolsHelper.throwException("审核驳回时必须填写拒绝类型");
            }
            if (StringUtils.isBlank(dto.getRejectReason())) {
                ToolsHelper.throwException("审核驳回时必须填写拒绝原因");
            }
        }

        // 更新审核单状态
        aftersalesReviews.setReviewStatus(dto.getReviewStatus());
        aftersalesReviews.setReviewTime(LocalDateTime.now());
        aftersalesReviews.setOperator(RequestHelper.getAdminOperator());
        aftersalesReviews.setAutoAudit(0); // 人工审核

        // 设置拒绝相关信息
        if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.REJECTED.getValue())) {
            aftersalesReviews.setRejectType(dto.getRejectType());
            aftersalesReviews.setRejectReason(dto.getRejectReason());
            aftersalesReviews.setReviewRemark(dto.getRejectReason());
        } else {
            // 审核通过时清空拒绝信息
            aftersalesReviews.setRejectType(null);
            aftersalesReviews.setRejectReason(null);
            aftersalesReviews.setReviewRemark("审核通过");
        }

        // 保存审核单
        aftersalesReviews.setUpdatedAt(LocalDateTime.now());
        aftersalesReviewsService.updateById(aftersalesReviews);

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(aftersalesReviews.getReviewSn());
        if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.APPROVED.getValue())) {
            reviewsLog.setOperateContent("人工审核通过");
        } else {
            reviewsLog.setOperateContent("人工审核驳回，拒绝原因：" + dto.getRejectReason());
        }
        reviewsLog.setOperator(RequestHelper.getAdminOperator());
        reviewsLog.setType("review");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("售后审核单审核完成，审核单号：{}，审核结果：{}",
                aftersalesReviews.getReviewSn(), AftersalesReviewsStatusEnum.map.get(dto.getReviewStatus()));

        // 发送回调通知
        sendNotifyCallback(aftersalesReviews);
    }

    /**
     * 发送回调通知
     *
     * @param aftersalesReviews 售后审核单数据
     */
    public void sendNotifyCallback(AftersalesReviews aftersalesReviews) {
        // 检查是否有回调地址
        if (StringUtils.isBlank(aftersalesReviews.getNotifyUrl())) {
            log.debug("审核单号: {} 没有配置回调地址，跳过回调通知", aftersalesReviews.getReviewSn());
            return;
        }

        try {
            // 构建回调数据
            AfterSalesReviewsNotifyBO notifyBO = BeanUtil.copyProperties(aftersalesReviews, AfterSalesReviewsNotifyBO.class);

            // 构建回调地址
            URI uri = URI.create(aftersalesReviews.getNotifyUrl());

            // 发送回调通知
            JsonResult<Object> result = afterSalesReviewsNotifyFeign.reviewStatusNotify(uri, notifyBO);

            if (result.isSuccess()) {
                // 通知成功，更新通知状态
                aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.SUCCESS.getValue());
                aftersalesReviews.setUpdatedAt(LocalDateTime.now());
                aftersalesReviewsService.updateById(aftersalesReviews);

                log.info("售后审核单回调通知成功，审核单号: {}, 回调地址: {}",
                        aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl());
            } else {
                // 通知失败，更新通知状态
                aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.FAILED.getValue());
                aftersalesReviews.setUpdatedAt(LocalDateTime.now());
                aftersalesReviewsService.updateById(aftersalesReviews);

                log.warn("售后审核单回调通知失败，审核单号: {}, 回调地址: {}, 错误信息: {}",
                        aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl(), result.getMsg());
            }
        } catch (Exception e) {
            // 通知异常，更新通知状态为失败
            aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.FAILED.getValue());
            aftersalesReviews.setUpdatedAt(LocalDateTime.now());
            aftersalesReviewsService.updateById(aftersalesReviews);

            log.error("售后审核单回调通知异常，审核单号: {}, 回调地址: {}",
                    aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl(), e);
        }
    }
}
