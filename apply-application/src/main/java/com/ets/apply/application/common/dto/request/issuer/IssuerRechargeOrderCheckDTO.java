package com.ets.apply.application.common.dto.request.issuer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerRechargeOrderCheckDTO {

    @JsonProperty(value = "order_sn")
    private String orderSn;
    private String steps = "rechargeOrderCheck";
    @JsonProperty(value = "card_no")
    private String cardNo;
    @JsonProperty(value = "plate_no")
    private String plateNo;
    @JsonProperty(value = "plate_color")
    private Integer plateColor;
}
