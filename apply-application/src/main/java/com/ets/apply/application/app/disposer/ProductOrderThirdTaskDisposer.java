package com.ets.apply.application.app.disposer;

import com.ets.apply.application.app.business.productOrder.ProductOrderThirdTaskBusiness;
import com.ets.apply.application.common.bo.productOrderThirdTask.ProductOrderThirdTaskNotifyBO;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "ProductOrderThirdTaskJobBean")
public class ProductOrderThirdTaskDisposer extends BaseDisposer {

    @Autowired
    private ProductOrderThirdTaskBusiness productOrderThirdTaskBusiness;

    public ProductOrderThirdTaskDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "ProductOrderThirdTaskJobBean";
    }

    @Override
    public void execute(Object content) {

        ProductOrderThirdTaskNotifyBO bo = getParamsObject(content, ProductOrderThirdTaskNotifyBO.class);

        productOrderThirdTaskBusiness.notifyThirdOrderStatus(bo.getThirdTaskSn());
    }
}
