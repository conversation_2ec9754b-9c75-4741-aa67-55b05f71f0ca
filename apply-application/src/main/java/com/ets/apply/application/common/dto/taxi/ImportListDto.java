package com.ets.apply.application.common.dto.taxi;

import java.util.Date;
import lombok.Data;

@Data
public class ImportListDto {
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 导入状态
     */
    private Integer importStatus;

    /**
     * 导入时间(起)
     */
    private String startImportTime;

    /**
     * 导入时间(止)
     */
    private String endImportTime;

    /**
     * 分页大小
     */
    private Integer pageSize = 20;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 出租车公司id
     */
    private Integer companyId;

    /**
     * 激活时间(起)
     */
    private String activateTimeStart;

    /**
     * 激活时间(止)
     */
    private String activateTimeEnd;
}
