package com.ets.apply.application.app.business.goods;

import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.common.dto.request.applyConfig.ApplyConfigGetDto;
import com.ets.apply.application.common.dto.request.applyConfig.ApplyConfigSetDto;
import com.ets.apply.application.infra.entity.ApplyConfigEntity;
import com.ets.apply.application.infra.service.ApplyConfigService;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class GoodsOrderCenterBusiness extends BaseBusiness {
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ApplyConfigService applyConfigService;

    public boolean setConfig(ApplyConfigSetDto applyConfigSetDto) throws BizException {
        //find
        ApplyConfigEntity applyConfigEntity = applyConfigService.findOneByDomainKey(applyConfigSetDto.getDomainKey());
        //新增
        if(applyConfigEntity == null){
            applyConfigService.createdByDomainKey(
                ToolsHelper.genNum(redisPermanentTemplate, "applyConfigSn", appConfig.getEnv(), 8),
                applyConfigSetDto.getDomainKey(),
                applyConfigSetDto.getParams()
            );
        }else{
            //修改
            applyConfigService.modifyByDomainKey(applyConfigSetDto.getDomainKey(),applyConfigSetDto.getParams());
        }
        return true;
    }

    public ApplyConfigEntity getConfig(ApplyConfigGetDto applyConfigGetDto) throws BizException {

        return applyConfigService.findOneByDomainKey(applyConfigGetDto.getDomainKey());
    }
}
