package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.AfterSalesReviewsNotifyFallbackFactory;
import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewsNotifyBO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;

/**
 * 售后审核单回调通知 Feign 接口
 */
@FeignClient(name = "AfterSalesReviewsNotifyFeign", url = "uri", fallbackFactory = AfterSalesReviewsNotifyFallbackFactory.class)
public interface AfterSalesReviewsNotifyFeign {

    /**
     * 售后审核单状态变更通知
     *
     * @param uri 回调地址
     * @param bo  回调数据
     * @return 回调结果
     */
    @PostMapping
    JsonResult<Object> reviewStatusNotify(URI uri, @RequestBody AfterSalesReviewsNotifyBO bo);
}
