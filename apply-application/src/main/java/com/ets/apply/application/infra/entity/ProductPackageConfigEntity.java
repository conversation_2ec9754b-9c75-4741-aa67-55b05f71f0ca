package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 产品包相关配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_package_config")
public class ProductPackageConfigEntity extends BaseEntity<ProductPackageConfigEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置key
     */
    private String configKey;

    /**
     * 配置对应的值json
     */
    private String configValues;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
