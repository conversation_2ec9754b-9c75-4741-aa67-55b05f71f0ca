package com.ets.apply.application.common.consts.importRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportRecordStatusEnum {

    DEFAULT(0, "待处理"),

    SUCCESS(1, "处理成功"),

    FAILED(2, "处理失败"),

    INVALID(9, "无效数据");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ImportRecordStatusEnum node : ImportRecordStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static ImportRecordStatusEnum getByCode(Integer code) {

        for (ImportRecordStatusEnum node : ImportRecordStatusEnum.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }
}
