package com.ets.apply.application.app.service.bank;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallCiticNewFeign;
import com.ets.apply.application.common.config.creditBank.CiticCreditBankConfig;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.citic.CiticResponseVO;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class CiticService {
    @Autowired
    private CiticCreditBankConfig citicCreditBankConfig;
    @Autowired
    private CallCiticNewFeign callCiticFeign;
    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;

    /**
     * 获取中信银行申请链接
     *
     * @return String
     */
    public String getApplyUrl(ApplyOrderDTO applyOrderDTO) throws BizException {
        if (ObjectUtil.isNotEmpty(applyOrderDTO.getSubReferType()) && ObjectUtil.isNotEmpty(citicCreditBankConfig.getSubReferTypeApplyUrlMap().get(applyOrderDTO.getSubReferType()))) {
            return citicCreditBankConfig.getSubReferTypeApplyUrlMap().get(applyOrderDTO.getSubReferType()) + applyOrderDTO.getBankApplyNumber();
        } else {
            return citicCreditBankConfig.getApplyUrl() + applyOrderDTO.getBankApplyNumber();
        }
    }

    /**
     * 查询中信银行状态
     *
     * @return String
     */
    public CiticResponseVO queryOrder(String orderSn) throws BizException {
        //请求日志
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        try {
            List<String> paIds = new ArrayList<>();
            paIds.add(orderSn);
            Map<String, Object> map = new HashMap<>();
            map.put("paIds", paIds);

            map.put("partnerId", citicCreditBankConfig.getPartnerId());
            //添加请求日志
            thirdInterfaceLogDTO.setLogMethod("citic:queryOrder");
            thirdInterfaceLogDTO.setLogParams(map.toString());
            thirdInterfaceLogDTO.setLogRequest(citicCreditBankConfig.getStatusQueryUrl());
            String jsonResult = callCiticFeign.queryOrder(URI.create(citicCreditBankConfig.getStatusQueryUrl()),map);
            //添加请求日志
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return JSON.parseObject(jsonResult, CiticResponseVO.class);

        } catch (Exception e) {
            //添加请求日志
            thirdInterfaceLogDTO.setLogRespone("CreditCardCitic queryOrder：" + e.getMessage());
            thirdInterfaceLogDTO.setStatus(0);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
            return null;
        }

    }
}
