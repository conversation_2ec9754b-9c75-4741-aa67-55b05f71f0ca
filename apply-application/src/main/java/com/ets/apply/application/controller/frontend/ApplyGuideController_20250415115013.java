package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ApplyGuideBusiness;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionGroupVO;
import com.ets.common.JsonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/frontend/apply-guide")
@Validated
public class ApplyGuideController {

    private static final Logger log = LoggerFactory.getLogger(ApplyGuideController.class);

    @Autowired
    private ApplyGuideBusiness applyGuideBusiness;

    @PostMapping("/get-question-map")
    public JsonResult<List<ApplyGuideMapVO>> getQuestionMap(@RequestBody(required = false) MapTypeRequest request) {
        return JsonResult.ok(applyGuideBusiness.getQuestionMap(request != null ? request.getMapType() : null));
    }

    @PostMapping("/get-result-map")
    public JsonResult<List<ApplyGuideResultVO>> getResultMap(@RequestBody(required = false) MapTypeRequest request) {
        return JsonResult.ok(applyGuideBusiness.getResultMap(request != null ? request.getMapType() : null));
    }

    @PostMapping("/get-question-group-list")
    public JsonResult<List<ApplyGuideQuestionGroupVO>> getQuestionGroupList(@Valid @RequestBody QuestionGroupRequest request) {
        log.debug("获取问题组列表请求 - mapType: {}, questionTypeList: {}", request.getMapType(), request.getQuestionTypeList());
        List<ApplyGuideQuestionGroupVO> result = applyGuideBusiness.getQuestionGroupList(request.getMapType(), request.getQuestionTypeList());
        return JsonResult.ok(result);
    }

    @Data
    public static class MapTypeRequest {
        private Integer mapType;
    }

    @Data
    public static class QuestionGroupRequest {
        @NotNull(message = "地图类型不能为空")
        private Integer mapType;

        @NotNull(message = "问题类型列表不能为空")
        private List<Integer> questionTypeList;
    }
}

