package com.ets.apply.application.app.service.bank;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.common.config.SpdBankConfig;
import com.ets.apply.application.common.utils.bank.spd.EncryptTools;
import com.ets.common.ToolsHelper;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class SpdService {


    @Autowired
    private SpdBankConfig spdBankConfig;

    /**
     * 返回SM4 加密的urlData
     *
     * @return String
     */
    public String getApplyUrl(String orderSn,String redirectUrl) throws Exception {

        Map<String, Object> dataMap = new HashMap<>(3);
        dataMap.put("userId", orderSn);
        dataMap.put("backurl", StringUtils.isEmpty(redirectUrl) ? spdBankConfig.getBackurl() : redirectUrl);
        dataMap.put("linkNum", spdBankConfig.getLinkNum());
        log.info("浦发加密前参数:" + JSON.toJSONString(dataMap));
        // 将要求的参数转成json 后进行SM4 加密
        String encryptData = EncryptTools.encrypt(JSON.toJSONString(dataMap), spdBankConfig.getPrivateKeySM4());
        log.info("浦发SM4 加密后data:" + JSON.toJSONString(encryptData));
        String urlData = URLEncoder.encode(encryptData, "UTF-8");
        // 对urlData 进行sm2 加签
        String encryptSign = EncryptTools.sign(encryptData, spdBankConfig.getPrivateKeySM2());
        log.info("浦发SM2 加密后sign:" + JSON.toJSONString(encryptData));

        String sign = URLEncoder.encode(encryptSign, "UTF-8");
        StringBuilder sb = new StringBuilder();
        sb.append(spdBankConfig.getRequestUrl()).append("?");
        sb.append("channel=").append(spdBankConfig.getChannel()).append("&");
        sb.append("data=").append(urlData).append("&");
        sb.append("sign=").append(sign);
        return sb.toString();
    }


    /**
     * 浦发银行解密
     *
     * @param data
     * @return
     */
    public String decryptAndVerifyData(String data, String sign) throws Exception {

        // 解密  1、sign验签 2、sm4 解密
        Boolean result = EncryptTools.validate(data, sign, spdBankConfig.getSpdPublicKeySM2());
        log.info("浦发SM2 验签结果 :" + result);

        if (result.equals(false)) {
            ToolsHelper.throwException("浦发验签sign 错误");
        }

        String decrypeData = EncryptTools.decrypt(data, spdBankConfig.getPrivateKeySM4());
        log.info("浦发SM2 解密后decryptData :" + decrypeData);

        return decrypeData;
    }

    /**
     * 用于测试模拟的，加密浦发侧回调数据，返回浦发参数
     * @param data
     * @return
     * @throws Exception
     */
    public HashMap<String,String> testSpdSign(String data) throws Exception {
        String encryptData = EncryptTools.encrypt(data, spdBankConfig.getPrivateKeySM4());
        log.info("浦发SM4 加密后data:" + encryptData);

        // encryptData 进行sm2 加签
        String encryptSign = EncryptTools.sign(encryptData, spdBankConfig.getSpdPrivateKeySM2());
        log.info("浦发SM2 加密后sign:" + encryptSign);
        HashMap<String, String> map = new HashMap<>();
        map.put("data", encryptData);
        map.put("sign", encryptSign);
        return map;
    }


    /**
     * 浦发银行的返回格式化
     *
     * @param code
     * @param message
     * @return
     */
    public String formatResponse(String code, String message) {
        Map<String, Object> dataMap = ImmutableMap.of("respCode", code, "respMsg", message);
        return JSON.toJSONString(dataMap);
    }
}
