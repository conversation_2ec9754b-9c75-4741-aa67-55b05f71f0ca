{"sysTime":"2025-05-28 09:57:46.999","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"e78cfc93-dc30-43eb-b852-e1d53fc50944","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"e78cfc93-dc30-43eb-b852-e1d53fc50944\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 09:57:47.132","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"e78cfc93-dc30-43eb-b852-e1d53fc50944","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"113ms\"}","thrown":""}
{"sysTime":"2025-05-28 09:57:47.308","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"e78cfc93-dc30-43eb-b852-e1d53fc50944","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"175ms\"}","thrown":""}
{"sysTime":"2025-05-28 09:57:47.346","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"e78cfc93-dc30-43eb-b852-e1d53fc50944","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748397467309,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"35ms\"}","thrown":""}
{"sysTime":"2025-05-28 09:58:21.779","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"1f6a975f-3c19-4624-9d03-e1ed63cc3847","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"1f6a975f-3c19-4624-9d03-e1ed63cc3847\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 09:58:21.820","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"1f6a975f-3c19-4624-9d03-e1ed63cc3847","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"38ms\"}","thrown":""}
{"sysTime":"2025-05-28 09:58:21.903","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"1f6a975f-3c19-4624-9d03-e1ed63cc3847","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"82ms\"}","thrown":""}
{"sysTime":"2025-05-28 09:58:21.938","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"1f6a975f-3c19-4624-9d03-e1ed63cc3847","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748397501904,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"34ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:00:38.588","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"5dfe35f0-2050-916d-b5b6-56a45a5fd76b","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"5dfe35f0-2050-916d-b5b6-56a45a5fd76b\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:00:38.664","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"5dfe35f0-2050-916d-b5b6-56a45a5fd76b","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"73ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:00:38.791","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"5dfe35f0-2050-916d-b5b6-56a45a5fd76b","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"126ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:00:38.826","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"5dfe35f0-2050-916d-b5b6-56a45a5fd76b","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748397638792,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"34ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:02:55.806","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"ec5ffa78-f943-476d-bde9-c334396976d9","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"ec5ffa78-f943-476d-bde9-c334396976d9\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:02:55.897","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"ec5ffa78-f943-476d-bde9-c334396976d9","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"86ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:02:56.034","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"ec5ffa78-f943-476d-bde9-c334396976d9","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"137ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:02:56.087","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"ec5ffa78-f943-476d-bde9-c334396976d9","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748397776035,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"52ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:06.145","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:06.152","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:06.152","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:06.152","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:06.153","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:07.816","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:07.817","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:07.818","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:07.818","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:07.818","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:14:09.824","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:09.825","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:09.826","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:09.826","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:09.826","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:14:11.159","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:11.162","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:11.163","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:11.164","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:11.164","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:13.830","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:13.831","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:13.832","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:13.832","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:13.832","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:14:16.170","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:16.174","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:16.174","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:16.175","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:16.175","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:21.179","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.181","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.181","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.181","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:21.182","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:21.837","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.838","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.839","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:21.839","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:21.839","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:14:26.190","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:26.193","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:26.193","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:26.193","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:26.194","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:31.198","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:31.202","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:31.203","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:31.203","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:31.203","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:36.210","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:36.213","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:36.213","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:36.214","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:36.214","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:37.842","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:37.843","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:37.844","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:37.844","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:37.845","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:14:41.220","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:41.223","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:41.224","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:41.224","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:41.224","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:46.230","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:46.233","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:46.233","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:46.233","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:46.234","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:51.236","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:51.240","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:51.241","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:51.241","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:51.242","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:56.394","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:56.398","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:56.398","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:14:56.399","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:14:56.399","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:01.402","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:01.406","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:01.407","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:01.407","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:01.408","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:06.413","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:06.417","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:06.417","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:06.418","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:06.418","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:09.849","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:09.850","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:09.851","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:09.851","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:09.851","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@apply-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-05-28 10:15:11.424","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:11.427","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:11.428","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:11.428","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:11.428","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:16.432","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:16.435","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:16.436","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:16.436","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:16.437","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:21.443","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:21.447","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:21.448","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.net.UnknownHostException: nacos.public\n\tat java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:572)\n\tat java.base/java.net.Socket.connect(Socket.java:633)\n\tat java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:533)\n\tat java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:638)\n\tat java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:281)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:386)\n\tat java.base/sun.net.www.http.HttpClient.New(HttpClient.java:408)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1324)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1257)\n"}
{"sysTime":"2025-05-28 10:15:21.448","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:21.448","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20070,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@apply-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.net.UnknownHostException: nacos.public\"}","thrown":""}
{"sysTime":"2025-05-28 10:15:26.633","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@apply-application with instance: Instance{instanceId='null', ip='***********', port=20070, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@apply-application', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-05-28 10:20:33.650","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"3298211b-8a0b-9422-a3f4-4beaef9a291e","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"3298211b-8a0b-9422-a3f4-4beaef9a291e\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:20:33.727","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"3298211b-8a0b-9422-a3f4-4beaef9a291e","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"70ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:20:33.903","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"3298211b-8a0b-9422-a3f4-4beaef9a291e","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"175ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:20:33.937","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"3298211b-8a0b-9422-a3f4-4beaef9a291e","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748398833904,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"33ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:06.803","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"04f3d767-b8ae-4071-808d-48c35538fa06","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":**********,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"04f3d767-b8ae-4071-808d-48c35538fa06\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:21:06.846","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"04f3d767-b8ae-4071-808d-48c35538fa06","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":**********}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":**********},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"34ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:06.929","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"04f3d767-b8ae-4071-808d-48c35538fa06","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"82ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:06.964","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"04f3d767-b8ae-4071-808d-48c35538fa06","spanId":"0.1","parentId":"0","exportable":"","pid":"79605","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748398866929,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":**********}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"34ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.291","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.291","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.293","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.293","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.312","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:95","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-95","message":"De-registering from Nacos Server now...","thrown":""}
{"sysTime":"2025-05-28 10:21:25.313","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:100","methodName":"com.alibaba.nacos.client.naming:removeBeatInfo-100","message":"[BEAT] removing beat: apply@@apply-application:***********:20070 from beat map.","thrown":""}
{"sysTime":"2025-05-28 10:21:25.313","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:268","methodName":"com.alibaba.nacos.client.naming:deregisterService-268","message":"[DEREGISTER-SERVICE] ets-dev deregistering service apply@@apply-application with instance: Instance{instanceId='null', ip='***********', port=20070, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}","thrown":""}
{"sysTime":"2025-05-28 10:21:25.351","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:115","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-115","message":"De-registration finished.","thrown":""}
{"sysTime":"2025-05-28 10:21:25.353","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:21:28.365","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:21:28.366","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:21:31.379","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:21:34.393","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:21:34.393","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:21:34.394","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:21:34.394","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:21:34.394","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:21:34.394","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:34.395","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:21:34.395","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-05-28 10:21:34.395","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-05-28 10:21:34.395","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:21:35.149","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-36","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-05-28 10:21:35.233","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-apply-executor-dev', registryValue='**********:20072'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-05-28 10:21:35.233","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:21:35.234","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-05-28 10:21:35.234","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:21:35.234","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"Thread-35","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:21:35.331","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-05-28 10:21:35.335","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-11} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.335","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-11} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.335","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.335","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-6} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-6} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-10} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-10} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-9} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-9} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-8} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-8} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-5} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-5} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.336","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-7} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:21:35.337","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-7} closed","thrown":""}
{"sysTime":"2025-05-28 10:21:35.337","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"79605","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
