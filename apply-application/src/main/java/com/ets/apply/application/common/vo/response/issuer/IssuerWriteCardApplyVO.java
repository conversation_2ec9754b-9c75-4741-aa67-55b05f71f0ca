package com.ets.apply.application.common.vo.response.issuer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class IssuerWriteCardApplyVO {

    private Integer result;
    private String msg;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processedAt;
    /**
     * 圈存金额(单位：分）
     */
    private Integer writeCardAmount;
    /**
     * 圈存秘钥MAC2
     */
    private String writeCardSecretKey;
    /**
     * 圈存受理号
     */
    private String writeCardAcceptNo;
}
