package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.creditCard.CommBusiness;
import com.ets.apply.application.common.dto.request.bank.comm.ResultNotifyDto;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/third/comm")
@RefreshScope
@RestController
@Slf4j
public class CommController {

    @Autowired
    private CommBusiness commBusiness;

    @PostMapping("/receiveOrder")
    JsonResult<?> receiveOrder(@RequestBody @Valid ResultNotifyDto resultNotifyDto) {
        return JsonResult.ok(commBusiness.receiveOrder(resultNotifyDto));
    }
}
