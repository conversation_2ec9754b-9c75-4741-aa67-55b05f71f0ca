package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RelateBankDataStatusEnum {

    RELATE_STATUS_EMPTY(1, "无匹配数据"),
    RELATE_STATUS_NOT_NEW_USER(2, "非新户"),
    RELATE_STATUS_NOT_USE(3, "新用户未首刷"),
    RELATE_STATUS_HAVE_USE(4, "新用户已首刷"),
    RELATE_STATUS_MUL_USER(5, "多条数据用户");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (RelateBankDataStatusEnum node : RelateBankDataStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
