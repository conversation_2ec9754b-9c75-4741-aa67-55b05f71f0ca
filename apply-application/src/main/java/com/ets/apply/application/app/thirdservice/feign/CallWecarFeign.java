package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallWecarFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.Map;

/**
 * 调用中信银行接口
 */
@FeignClient(url = "EMPTY", name = "CallWecarFeign",   fallbackFactory = CallWecarFallbackFactory.class)
public interface CallWecarFeign {

    @PostMapping("/etcOrder/statusSync")
    String statusSync(URI uri, @RequestBody Map<String, Object> map);

}
