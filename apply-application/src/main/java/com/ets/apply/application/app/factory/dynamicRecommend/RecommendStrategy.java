package com.ets.apply.application.app.factory.dynamicRecommend;

import com.ets.apply.application.app.thirdservice.request.bigData.DynamicRecommendDTO;
import com.ets.apply.application.common.dto.dynamicRecommend.RecommendDTO;
import com.ets.apply.application.common.vo.dynamicRecommend.DynamicRecommendVO;

/**
 * <AUTHOR>
 */
public interface RecommendStrategy {
    DynamicRecommendVO recommend(Long uid);
    DynamicRecommendVO recommend(Long uid, RecommendDTO recommendDTO);

}
