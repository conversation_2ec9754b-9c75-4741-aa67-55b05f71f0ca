package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class CreditCardCoopStatusDTO {



    @NotNull(message = "银行不能为空")
    private Integer whichBank;
    /**
     * TYPE_DEFAULT(1, "申办-内部券"),
     * TYPE_NEW_APPLY(2, "申办-资格"),
     * TYPE_NORMAL_CREDIT_APPLY(3, "活动-消费券"),
     * TYPE_PRODUCT_ORDER_APPLY(4, "商城订单-内部券"),
     * TYPE_INCREASE_APPLY (5, "会员-资格");
     */
    @NotNull(message = "申请类型不能为空")
    private Integer classify;


}
