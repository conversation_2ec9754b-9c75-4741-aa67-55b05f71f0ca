package com.ets.apply.application.common.consts.issuer;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IssuerIdServiceCodeMap {
    JSUTONG_CAR(1, "320101","江苏发行机构"),
    BSUTONG_CAR(2, "110101","北京速通科技有限公司"),
    YUETONG_CAR(3, "440101","广东发行机构"),
    SHANDONG_CAR(6, "370101","山东发行机构"),
    GUIZHOU_CAR(9, "520101","贵州发行机构"),
    TSUTONG_CAR(10, "120101","天津发行机构"),
    JILING_CAR(21, "220101","吉林发行机构"),
    GJETONG_CAR(25, "450101","广西发行机构"),
    QINHAI_CAR(26, "630101","青海发行机构"),
    NEIMENG_CAR(28, "150101","内蒙古发行机构"),
    QINHAI_TWO_CAR(30, "630101","青海发行机构"),
    BSUTONG_TRUCK(31, "110101","北京速通科技有限公司"),
    JSUTONG_TRUCK(32, "320101","江苏发行机构"),
    NEIMENG_TRUCK(33, "150101","内蒙古发行机构"),
    JSUTONG_TAXI(35, "320101","江苏发行机构"),
    TSUTONG_TRUCK(37, "120101","天津发行机构"),
    JSUTONG_9901_CAR(39, "320101","江苏发行机构"),
    JSUTONG_9901_TRUCK(40, "320101","江苏发行机构");


    private final Integer issuerId;
    private final String code;
    private final String name;

    public static String getNameByIssuerId(Integer issuerId) {
        for (IssuerIdServiceCodeMap node : IssuerIdServiceCodeMap.values()) {
            if (node.getIssuerId() == issuerId) {
                return node.getName();
            }
        }
        return "";
    }

}
