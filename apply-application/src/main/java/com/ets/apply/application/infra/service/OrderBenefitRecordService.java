package com.ets.apply.application.infra.service;

import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.consts.orderBenefitRecord.OrderBenefitRecordStatusEnum;
import com.ets.apply.application.infra.entity.OrderBenefitRecord;
import com.ets.apply.application.infra.mapper.OrderBenefitRecordMapper;

/**
 * <p>
 * 订单权益发放记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Service
@DS("db-order")
public class OrderBenefitRecordService extends BaseService<OrderBenefitRecordMapper, OrderBenefitRecord> {

    public List<OrderBenefitRecord> getNeedSendRecordList(String benefitType, int limit) {
        LambdaQueryWrapper<OrderBenefitRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderBenefitRecord::getBenefitType, benefitType)
              .in(OrderBenefitRecord::getStatus, Arrays.asList(OrderBenefitRecordStatusEnum.DEFAULT.getValue(), OrderBenefitRecordStatusEnum.FAIL.getValue()))
              .last("limit " + limit);
        
        return this.list(wrapper);
    }
}
