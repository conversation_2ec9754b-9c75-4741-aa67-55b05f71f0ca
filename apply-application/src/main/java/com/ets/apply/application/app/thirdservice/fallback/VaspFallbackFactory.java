package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.VaspFeign;
import com.ets.apply.application.common.bo.vasp.VaspOrderResultBO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class VaspFallbackFactory implements FallbackFactory<VaspFeign> {

    @Override
    public VaspFeign create(Throwable cause) {
        return new VaspFeign() {
            @Override
            public  String orderResult(@RequestBody VaspOrderResultBO params){
                return JsonResult.error("vasp服务调用失败：" + cause.getMessage()).toString();
            }

        };
    }
}