package com.ets.apply.application;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.monitor.OrderMonitorBusiness;
import com.ets.apply.application.common.bo.monitor.ApplyOrderMonitorBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class OrderStatisticTest {

    @Autowired
    OrderMonitorBusiness orderMonitorBusiness;

    @Test
    public void orderStatistic() {
//        ApplyOrderMonitorBO orderMonitorBO = new ApplyOrderMonitorBO();
//        orderMonitorBO.setStartTime("2023-01-01 00:00:00");
//        orderMonitorBO.setEndTime("2024-01-01 00:00:00");
//        orderMonitorBO.setSendKey("b176930b-ce2c-4bd4-999e-af0e82d83888");
//        String params = "{\"startTime\":\"2023-01-01 00:00:00\",\"endTime\":\"2024-01-01 00:00:00\",\"lastDays\":1}";

        //https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=eb11c3ec-b876-4ad5-98bb-6d16bddd76e5
        String params = "{\"lastDays\":100,\"sendKey\":\"eb11c3ec-b876-4ad5-98bb-6d16bddd76e5\",\"averageDays\":1}";
        ApplyOrderMonitorBO orderMonitorBO = JSONObject.parseObject(params, ApplyOrderMonitorBO.class);

        if (ObjectUtil.isEmpty(orderMonitorBO.getStartTime()) || ObjectUtil.isEmpty(orderMonitorBO.getEndTime())) {
            LocalDateTime startTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(orderMonitorBO.getLastDays()));
            LocalDateTime endTime = LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1));
            orderMonitorBO.setStartTime(startTime);
            orderMonitorBO.setEndTime(endTime);
        }

        if (ObjectUtil.isEmpty(orderMonitorBO.getAverageStartTime()) || ObjectUtil.isEmpty(orderMonitorBO.getAverageEndTime())) {
            LocalDateTime startTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(orderMonitorBO.getAverageDays()));
            LocalDateTime endTime = LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1));
            orderMonitorBO.setAverageStartTime(startTime);
            orderMonitorBO.setAverageEndTime(endTime);
        }


//        orderMonitorBusiness.orderStatistic(orderMonitorBO);

        orderMonitorBusiness.orderBizTypeStatistic(orderMonitorBO);

    }
}
