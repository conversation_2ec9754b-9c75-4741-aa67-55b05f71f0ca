package com.ets.apply.application.common.vo.segmentBenefit;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SegmentBenefitListVO {

    private Integer id;

    private Integer nums;

    private String startSegment;

    private String endSegment;

    private Integer warrantyPeriod;

    private Integer term;

    private String termName;

    private String operator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

}
