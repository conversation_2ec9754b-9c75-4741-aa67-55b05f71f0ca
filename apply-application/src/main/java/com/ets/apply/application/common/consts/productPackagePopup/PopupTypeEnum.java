package com.ets.apply.application.common.consts.productPackagePopup;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum PopupTypeEnum {

    ONE(1, "OBU弹窗", "670"),
    TWO(2, "车主权益", "674"),
 ;

    private final Integer value;
    private final String label;
    private final String msg;



    public static String getDescByCode(Integer code) {
        for (PopupTypeEnum node : PopupTypeEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getLabel();
            }
        }
        return "";
    }

    // 获取所有枚举值及对应信息
    public static List<HashMap<String, Object>> getAll() {
        List<HashMap<String, Object>> list = new ArrayList<>();
        for (PopupTypeEnum node : PopupTypeEnum.values()) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("label", node.getLabel());
            map.put("value", node.getValue());
            map.put("msg", node.getMsg());
            list.add(map);
        }
        return list;
    }

}
