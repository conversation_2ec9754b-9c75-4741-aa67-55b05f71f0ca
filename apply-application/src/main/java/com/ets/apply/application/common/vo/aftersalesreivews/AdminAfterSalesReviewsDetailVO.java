package com.ets.apply.application.common.vo.aftersalesreivews;

import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsRejectTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.common.consts.order.PlateColorEnum;
import lombok.Data;

@Data
public class AdminAfterSalesReviewsDetailVO {

    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 审核状态[0-待审核 1-审核通过 2-审核驳回 3-已取消]
     */
    private Integer reviewStatus;

    private String reviewStatusStr;

    /**
     * 审核驳回类型
     */
    private String rejectType;

    private String rejectTypeStr;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 审核行驶证信息
     */
    private VehicleInfoDTO reviewVehicleInfo;

    /**
     * 订单行驶证信息
     */
    private VehicleInfoDTO orderVehicleInfo;

    public String getReviewStatusStr() {
        return AftersalesReviewsStatusEnum.map.getOrDefault(reviewStatus, "未知");
    }

    public String getRejectTypeStr() {
        return AftersalesReviewsRejectTypeEnum.map.getOrDefault(rejectType, "-");
    }

    /**
     * 行驶证信息数据类型
     */
    @Data
    public static class VehicleInfoDTO {
        /**
         * 车牌号
         */
        private String plateNo;

        /**
         * 车牌颜色
         */
        private Integer plateColor;

        private String plateColorStr;

        /**
         * 所有人
         */
        private String owner;

        /**
         * 车辆类型
         */
        private String type;

        /**
         * 使用性质
         */
        private String useCharacter;

        /**
         * 核定载人数
         */
        private String passengers;

        /**
         * 行驶证正面
         */
        private String frontImgUrl;

        /**
         * 行驶证反面
         */
        private String backImgUrl;

        /**
         * 车头正面照
         */
        private String frontCarImgUrl;

        /**
         * 车内照
         */
        private String gearActivateImgUrl;

        public String getPlateColorStr() {
            return PlateColorEnum.getDescByCode(plateColor);
        }
    }
}
