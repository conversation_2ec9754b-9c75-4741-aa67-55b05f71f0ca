package com.ets.apply.application.infra.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.reviewOrder.ThirdReviewStatus;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.order.CanContinueStatusEnum;
import com.ets.apply.application.infra.entity.ReviewOrderEntity;
import com.ets.apply.application.infra.mapper.ReviewOrderMapper;
import com.ets.common.ToolsHelper;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;



/**
 * <p>
 * 审核单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Service
@DS("db-order")
public class ReviewOrderService extends BaseService<ReviewOrderMapper, ReviewOrderEntity> {

    /**
     * 根据审核单号获取审核单信息
     * @param reviewOrderSn 订单号
     * @return ReviewOrderEntity
     */
    public ReviewOrderEntity getByReviewOrderSn(String reviewOrderSn) {

        return super.baseMapper.selectById(reviewOrderSn);
    }

    /**
     * 获取审核通过的审核单
     */
    public List<ReviewOrderEntity> getThirdReviewPassListByReviewOrderSn(List<String> reviewOrderSn) {
        Wrapper<ReviewOrderEntity> wrapper = Wrappers.<ReviewOrderEntity>lambdaQuery()
                .in(ReviewOrderEntity::getReviewOrderSn, reviewOrderSn);
        return super.baseMapper.selectList(wrapper);
    }
    public ReviewOrderEntity getNeedDealReviewOrderSn(OrderOrderEntity orderOrder) {
        //没有审核单
        if(StringUtil.isEmpty(orderOrder.getReviewOrderSn())){
            return null;
        }

        Wrapper<ReviewOrderEntity> wrapper = Wrappers.<ReviewOrderEntity>lambdaQuery()
                .eq(ReviewOrderEntity::getReviewOrderSn, orderOrder.getReviewOrderSn())
                .in(ReviewOrderEntity::getThirdReviewStatus, Arrays.asList(
                    ThirdReviewStatus.pushing.getCode(),
                    ThirdReviewStatus.wait.getCode(),
                    ThirdReviewStatus.pass.getCode(),
                    ThirdReviewStatus.fail.getCode(),
                    ThirdReviewStatus.reject.getCode()
                ));
        return  super.baseMapper.selectOne(wrapper);
    }
    public List<ReviewOrderEntity> getByReviewOrderSns(List<String> reviewOrderSns) {
        LambdaQueryWrapper<ReviewOrderEntity> wrapper = Wrappers.<ReviewOrderEntity>lambdaQuery()
                .in(ReviewOrderEntity::getReviewOrderSn, reviewOrderSns);
        return super.baseMapper.selectList(wrapper);
    }


    /*
     *  重置审核单的 can_continue 状态为 1
     */
    public void resetCanContinue(String reviewOrderSn) {
        try{
            LambdaUpdateWrapper<ReviewOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(ReviewOrderEntity::getReviewOrderSn, reviewOrderSn)
                               .set(ReviewOrderEntity::getCanContinue, CanContinueStatusEnum.CAN_CONTINUE.getCode());
            updateByWrapper(lambdaUpdateWrapper);
        }catch (Exception e){
            ToolsHelper.throwException("重置审核单的can_continue状态操作失败");
        }
    }

}
