package com.ets.apply.application.common.consts.service;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServerErrorCodeEnum {

    SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED(208008, "已申请过信用卡，请刷新页面查看办理进度"),
    SERVICE_ERROR_CODE_CREDIT_CARD_SUBMITTED(208009, "您的申请已提交，请等待审核结果"),
    SERVICE_ERROR_CODE_CREDIT_CARD_NOT_ALLOW(208010, "该银行目前不可用");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ServerErrorCodeEnum node : ServerErrorCodeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
