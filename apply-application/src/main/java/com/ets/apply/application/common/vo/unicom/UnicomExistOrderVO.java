package com.ets.apply.application.common.vo.unicom;

import lombok.Data;

@Data
public class UnicomExistOrderVO {

    /**
     * 1 显示进度页, 2 走创建流程, 3 可复用选择
     */
    private Integer type;

    /**
     * 进行中的订单
     */
    private String existApplySn;

    /**
     * 可复用的订单号，为空则没有可复用，需重新创建
     */
    private String reuseApplySn;

    /**
     * 可复用的手机号 或 已存在的手机号
     */
    private String phone;

}
