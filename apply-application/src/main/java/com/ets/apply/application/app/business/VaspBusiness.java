package com.ets.apply.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.common.consts.productOrder.*;
import com.ets.apply.application.common.dto.request.vasp.VaspCancelDto;
import com.ets.apply.application.common.dto.request.vasp.VaspOrderSubmitDetailDto;
import com.ets.apply.application.common.dto.request.vasp.VaspSubmitDto;
import com.ets.apply.application.common.vo.vasp.VaspProductOrderSubmitVO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderLogEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.ProductOrderLogService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.base.feign.feign.LbsFeign;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class VaspBusiness extends BaseBusiness {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductPackageService productPackageService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ProductOrderLogService productOrderLogService;

    @Autowired
    private LbsFeign lbsFeign;

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate defaultRedisTemplate;

    /**
     * 人寿订单提交
     */
    public VaspProductOrderSubmitVO vaspSubmit(VaspSubmitDto vaspSubmitDto) {

        if (!ToolsHelper.addLock(defaultRedisTemplate, "vaspSubmit:"+ vaspSubmitDto.getOrderId(), 5)) {
            ToolsHelper.throwException("订单处理中，请勿重复提交");
        }

        // 返回对象
        VaspProductOrderSubmitVO submitVO = new VaspProductOrderSubmitVO();

        // 限制订单商品只有一个的逻辑放参数校验，这里只有一个商品，不用多做校验了
        // 检查订单是否已存在
        ProductOrderEntity productOrderEntity = productOrderService.findByThirdOrderSn(vaspSubmitDto.getOrderId());
        if (ObjectUtil.isNotNull(productOrderEntity)) {
            submitVO.setOrderId(productOrderEntity.getProductOrderSn());
            return submitVO;
        }

        List<VaspOrderSubmitDetailDto> list = JSONArray.parseArray(StringEscapeUtils.unescapeJava(vaspSubmitDto.getOrderDetails()),VaspOrderSubmitDetailDto.class);
        VaspOrderSubmitDetailDto orderDetailDto = list.stream().findFirst().orElse(null);



        if (ObjectUtil.isNull(orderDetailDto)) {
            ToolsHelper.throwException("订单商品信息缺失，请核对后再试");
        }
        // 订单商品校验
        this.validateOrderDetail(orderDetailDto);

        // 获取产品包
        ProductPackageEntity productPackageEntity = this.getProductPackageByPackageSn(orderDetailDto.getProductId());

        // 地址去空格
        vaspSubmitDto.setOrderAddr(vaspSubmitDto.getOrderAddr().replace(" ", "").trim());

        ProductOrderEntity entity = new ProductOrderEntity();
        // 属性设置
        String productOrderSn = ToolsHelper.genNum(redisPermanentTemplate, "productOrderSn", appConfig.getEnv(), 8);
        entity.setProductOrderSn(productOrderSn);
        entity.setThirdOrderSn(vaspSubmitDto.getOrderId().trim());

        // 产品包sn
        entity.setPackageSn(productPackageEntity.getPackageSn());
        entity.setSendPhone(vaspSubmitDto.getVirtualPhone().trim());
        entity.setSendName(vaspSubmitDto.getLinkmanName().trim());
        entity.setSendArea(this.parsingAddress(vaspSubmitDto.getOrderAddr()));
        entity.setSendAddress(vaspSubmitDto.getOrderAddr());
        // 设置价格
        entity.setPaidAmount(BigDecimal.valueOf(vaspSubmitDto.getRealPrice()));
        entity.setTotalAmount(BigDecimal.valueOf(vaspSubmitDto.getOrderPrice()));
        entity.setEtcPhone(vaspSubmitDto.getVirtualPhone().trim());
        entity.setOrderStatus(ProductOrderStatusEnum.PAID.getCode());
        // 设置订单来源
        entity.setSource(productPackageEntity.getSource());
        entity.setReferType(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode());
        entity.setReferValue(ReferValueEnum.PARTNER_VALUE_VASP.getValue());
        productOrderService.create(entity);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CREATED.getCode(),
                entity,
                entity.getOrderStatus(),
                "提交订单",
                "vasp 系统"
        );

        submitVO.setOrderId(entity.getProductOrderSn());
        return submitVO;
    }



    /**
     * 人寿订单取消
     */
    public void vaspCancel(VaspCancelDto vaspCancelDto) {

        // 获取订单信息
        ProductOrderEntity productOrderEntity = productOrderService.findByThirdOrderSn(vaspCancelDto.getOrderId());
        if (ObjectUtil.isNull(productOrderEntity)) {
            ToolsHelper.throwException("未提交该订单，请核实");
        }
        // 已取消订单幂等返回成功
        if (productOrderEntity.getOrderStatus().equals(ProductOrderStatusEnum.CLOSED.getCode())) {
            return;
        }
        // 检查订单是否能够取消
        if (!this.isAllowVaspCancel(productOrderEntity)) {
            ToolsHelper.throwException("订单出库不支持取消操作，请联系客服");
        }
        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderEntity.getProductOrderSn())
                .set(StringUtils.isNotEmpty(vaspCancelDto.getCancelDesc()), ProductOrderEntity::getCancelReason, vaspCancelDto.getCancelDesc())
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode());

        productOrderService.updateByWrapper(wrapper);
        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CANCEL.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "取消订单，取消原因："+vaspCancelDto.getCancelDesc()+"是否退款:"+vaspCancelDto.getIsRefund(),
                "vasp 系统"
        );

    }

    /**
     * 解析地址信息
     *
     * @param sendAddress
     * @return
     */
    private String parsingAddress(String sendAddress) {
        if (StringUtils.isEmpty(sendAddress)) {
            ToolsHelper.throwException("收货地址信息不能为空");
        }
        try {
            return lbsFeign.getAreaByAddress(sendAddress).getDataWithCheckError();
        } catch (Exception e) {
            log.info("vasp 解析收货地址异常" + e.getMessage());
            ToolsHelper.throwException("收货地址解析错误,请核对后再试");
        }
        return "";
    }

    // 创建日志记录
    public void createLog(Integer logType, ProductOrderEntity entity, Integer afterStatus, String content, String operator) {

        try {
            ProductOrderLogEntity logEntity = new ProductOrderLogEntity();
            logEntity.setOperateUser(StringUtils.isEmpty(operator) ? RequestHelper.getAdminOperator() : operator);
            logEntity.setProductOrderSn(entity.getProductOrderSn());
            logEntity.setLogType(logType);
            logEntity.setContent(content);
            logEntity.setAfterStatus(afterStatus);
            logEntity.setPreStatus(entity.getOrderStatus());
            logEntity.setLogisticStatus(entity.getLogisticStatus());

            productOrderLogService.create(logEntity);
        } catch (Exception e) {
            log.info("vasp 记录日志异常 " + e.getMessage());
        }

    }

    /**
     * 获取可用产品包，校验下单条件
     *
     * @param packageSn
     * @return
     */
    private ProductPackageEntity getProductPackageByPackageSn(String packageSn) {
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(packageSn);

        if (ObjectUtil.isNull(productPackageEntity)) {
            ToolsHelper.throwException("该产品未配置，请联系客服核实");
        }
        // 校验产品库存
        if (productPackageEntity.getPackageStock().equals(0)) {
            ToolsHelper.throwException("该产品库存不足，请联系客服核实");
        }
        return productPackageEntity;
    }

    /**
     * 是否允许合作商取消订单
     * @param productOrder
     * @return
     */
    private boolean isAllowVaspCancel(ProductOrderEntity productOrder) {
        // 订单未支付、未发货允许取消
        if (!Arrays.asList(
                ProductOrderStatusEnum.PAID.getCode(),
                ProductOrderStatusEnum.DEFAULT.getCode()
        ).contains(productOrder.getOrderStatus())) {
            return false;
        }

        // 发货单未推送可以取消
        return Objects.equals(ProductOrderLogisticStatusEnum.UN_PUSH.getCode(), productOrder.getLogisticStatus());
    }


    /**
     * 商品
     */
    public void validateOrderDetail(@Valid VaspOrderSubmitDetailDto orderDetailDto) {
        if(orderDetailDto.getProductCnt() >1){
            ToolsHelper.throwException("订单购买数量超过限制");
        }
    }

}
