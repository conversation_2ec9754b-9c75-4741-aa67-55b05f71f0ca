package com.ets.apply.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.factory.productPartner.ProductPartnerFactory;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import org.springframework.stereotype.Component;

@Component
public class ThirdPartnerShipNotify extends TaskBase {

    @Override
    public void childExec(TaskRecordEntity taskRecord) {
        try {
            ProductOrderShipBO contentBO = JSON.parseObject(taskRecord.getNotifyContent(), ProductOrderShipBO.class);
            // 组装退款参数

            ProductPartnerFactory.create(contentBO.getReferValue()).ship(contentBO);
        } catch (BizException e) {
            //原路输出
            ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
        } catch (Exception e1) {
            ToolsHelper.throwException("系统错误" + e1.getMessage());
        }
    }
}
