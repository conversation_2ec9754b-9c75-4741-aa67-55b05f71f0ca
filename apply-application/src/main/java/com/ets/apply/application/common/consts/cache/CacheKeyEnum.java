package com.ets.apply.application.common.consts.cache;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CacheKeyEnum {

    APPLY_PAGE_CACHE_KEY_PREFIX("apply:applyPage:pageCache", "测试页面缓存"),
    PRODUCT_PACKAGE_RESOURCE_CACHE_KEY_PREFIX("apply:productPackageResource:resourceCache", "产品包资源配置缓存");
    private final String code;
    private final String description;

}
