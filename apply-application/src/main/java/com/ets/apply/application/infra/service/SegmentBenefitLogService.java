package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.SegmentBenefitLog;
import com.ets.apply.application.infra.mapper.SegmentBenefitLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 号段权益日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
@DS("db-apply")
public class SegmentBenefitLogService extends BaseService<SegmentBenefitLogMapper, SegmentBenefitLog> {

}
