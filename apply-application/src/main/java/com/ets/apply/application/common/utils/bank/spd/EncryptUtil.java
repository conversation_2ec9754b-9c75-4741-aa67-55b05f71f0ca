package com.ets.apply.application.common.utils.bank.spd;

import lombok.Data;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.asn1.sec.ECPrivateKey;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.*;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.math.ec.ECConstants;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

public class EncryptUtil {

    public static class SM2Util {

        private static final X9ECParameters sm2p256v1 = GMNamedCurves.getByName("sm2p256v1");
        private static final byte[] defaultUserID = "****************".getBytes();

        /**
         * SM2私钥签名请求报文
         */
        public static String signData(String reqStr, String pvkStr) throws Exception {
            byte[] pvkBytes = Base64.getDecoder().decode(pvkStr);
            if (pvkBytes.length > 32) {
                pvkBytes = SM2Util.getPrivateKey(pvkBytes);
            }
            byte[] signedBytes = SM2Util.sign(pvkBytes, reqStr.getBytes());
            return Base64.getEncoder().encodeToString(signedBytes);
        }

        public static byte[] sign(byte[] privateKey, byte[] sourceData) throws CryptoException {
            return sign(defaultUserID, privateKey, sourceData);
        }

        public static byte[] sign(byte[] userId, byte[] privateKey, byte[] sourceData) throws CryptoException {

            ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
            ECPrivateKeyParameters priKeyParameters = new ECPrivateKeyParameters(new BigInteger(1, privateKey), parameters);
            SM2Signer signer = new SM2Signer();
            CipherParameters param;
            ParametersWithRandom pwr = new ParametersWithRandom(priKeyParameters, new SecureRandom());
            if (userId != null) {
                param = new ParametersWithID(pwr, userId);
            } else {
                param = pwr;
            }
            signer.init(true, param);
            signer.update(sourceData, 0, sourceData.length);
            return signer.generateSignature();
        }

        /**
         * SM2公钥验签报文
         */
        public static boolean verifySign(String srcString, String respSignature, String pukStr) throws Exception {
            byte[] pukBytes = Base64.getDecoder().decode(pukStr);
            if (pukBytes.length > 64) {
                pukBytes = SM2Util.getPublicKey(pukBytes);
            }
            return SM2Util.verifySign(pukBytes, srcString.getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(respSignature));
        }

        public static boolean verifySign(byte[] publicKey, byte[] sourceData, byte[] signData) {
            return verifySign(defaultUserID, publicKey, sourceData, signData);
        }

        public static boolean verifySign(byte[] userId, byte[] publicKey, byte[] sourceData, byte[] signData) {

            if (publicKey.length == 64) {
                byte[] tmp = new byte[65];
                System.arraycopy(publicKey, 0, tmp, 1, publicKey.length);
                tmp[0] = 0x04;
                publicKey = tmp;
            }

            ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
            ECPublicKeyParameters pubKeyParameters = new ECPublicKeyParameters(sm2p256v1.getCurve().decodePoint(publicKey), parameters);
            SM2Signer signer = new SM2Signer();
            CipherParameters param;
            if (userId != null) {
                param = new ParametersWithID(pubKeyParameters, userId);
            } else {
                param = pubKeyParameters;
            }
            signer.init(false, param);
            signer.update(sourceData, 0, sourceData.length);
            return signer.verifySignature(signData);
        }

        /**
         * 获取der编码下的公钥
         *
         * @param derData der编码的公钥，二进制数据
         * @return 返回公钥值
         */
        public static byte[] getPublicKey(byte[] derData) {

            SubjectPublicKeyInfo info = SubjectPublicKeyInfo.getInstance(derData);
            return info.getPublicKeyData().getBytes();
        }

        /**
         * 获取der编码下的私钥
         *
         * @param derData der编码的私钥，二进制数据
         * @return 返回私钥值
         */
        public static byte[] getPrivateKey(byte[] derData) throws IOException {

            PrivateKeyInfo info = PrivateKeyInfo.getInstance(derData);
            ECPrivateKey cpk = ECPrivateKey.getInstance(info.parsePrivateKey());

            int length = 32;
            byte[] bytes = cpk.getKey().toByteArray();
            if (bytes.length == length) {
                return bytes;
            }

            int start = bytes[0] == 0 ? 1 : 0;
            int count = bytes.length - start;

            if (count > length) {
                return null;
            }

            byte[] tmp = new byte[length];
            System.arraycopy(bytes, start, tmp, tmp.length - count, count);
            return tmp;
        }

        /**
         * 生成sm2公私钥对
         */
        public static SM2KeyPair generateKeyPair() {
            ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
            KeyGenerationParameters kgp = new ECKeyGenerationParameters(parameters, new SecureRandom());
            ECKeyPairGenerator ecKeyPairGenerator = new ECKeyPairGenerator();
            ecKeyPairGenerator.init(kgp);

            ECPrivateKeyParameters ecpriv = null;
            ECPublicKeyParameters ecpub = null;
            do {
                AsymmetricCipherKeyPair keypair = ecKeyPairGenerator.generateKeyPair();
                ecpriv = (ECPrivateKeyParameters) keypair.getPrivate();
                ecpub = (ECPublicKeyParameters) keypair.getPublic();
            } while (ecpriv == null || ecpriv.getD().equals(ECConstants.ZERO)
                    || ecpriv.getD().compareTo(sm2p256v1.getN()) >= 0 || ecpriv.getD().signum() <= 0);

            byte[] privKey = formatBigNum(ecpriv.getD());
            byte[] pubxKey = formatBigNum(ecpub.getQ().getAffineXCoord().toBigInteger());
            byte[] pubyKey = formatBigNum(ecpub.getQ().getAffineYCoord().toBigInteger());
            byte[] pubKey = new byte[64];
            System.arraycopy(pubxKey, 0, pubKey, 0, pubxKey.length);
            System.arraycopy(pubyKey, 0, pubKey, pubxKey.length, pubyKey.length)
            ;
            return new SM2KeyPair(privKey, pubKey);
        }

        /**
         * 格式化BigInteger，bg.toByteArray()获取到的字节数据长度不固定，因此需要格式化为固定长度
         */
        private static byte[] formatBigNum(BigInteger bg) {
            int needLength = 32;
            byte[] tmp = new byte[needLength];
            byte[] bgByte = bg.toByteArray();

            if (bgByte.length > needLength) {
                System.arraycopy(bgByte, bgByte.length - needLength, tmp, 0, needLength);
            } else if (bgByte.length == needLength) {
                tmp = bgByte;
            } else {
                System.arraycopy(bgByte, 0, tmp, needLength - bgByte.length, bgByte.length);
            }

            return tmp;
        }
    }

    @Data
    public static class SM2KeyPair {

        private String base64PriKey;
        private String base64PubKey;
        private byte[] priByte;
        private byte[] pubByte;

        public SM2KeyPair(byte[] priByte, byte[] pubByte) {
            this.priByte = priByte;
            this.pubByte = pubByte;
        }

        public String getBase64PriKey() {
            if (base64PriKey == null) {
                base64PriKey = Base64.getEncoder().encodeToString(priByte);
            }
            return base64PriKey;
        }

        public String getBase64PubKey() {
            if (base64PubKey == null) {
                base64PubKey = Base64.getEncoder().encodeToString(pubByte);
            }
            return base64PubKey;
        }
    }

    public static class SM4Util {

        private static final int SM4_ENCRYPT = 1, SM4_DECRYPT = 0;
        public static final int SM4_PKCS8_PADDING = 1;


        public static byte[] encryptCBC(byte[] data, byte[] key, byte[] iv) {
            return encryptCBC(data, key, iv, SM4_PKCS8_PADDING);
        }

        public static byte[] decryptCBC(byte[] cipher, byte[] key, byte[] iv) {
            return decryptCBC(cipher, key, iv, SM4_PKCS8_PADDING);
        }

        public static byte[] encryptCBC(byte[] data, byte[] key, byte[] iv, int paddingMode) {
            BlockCipher engine = new SM4Engine();
            engine.init(true, new KeyParameter(key));
            if (paddingMode == SM4_PKCS8_PADDING) {
                data = padding(data, SM4_ENCRYPT);
            } else {
                data = data.clone();
            }
            int length = data.length;
            iv = iv.clone();
            for (int i = 0; length > 0; length -= 16, i += 16) {
                for (int j = 0; j < 16; j++) {
                    data[i + j] = ((byte) (data[i + j] ^ iv[j]));
                }
                engine.processBlock(data, i, data, i);
                System.arraycopy(data, i, iv, 0, 16);
            }
            return data;
        }

        public static byte[] decryptCBC(byte[] cipher, byte[] key, byte[] iv, int paddingMode) {
            BlockCipher engine = new SM4Engine();
            engine.init(false, new KeyParameter(key));
            int length = cipher.length;
            byte[] plain = new byte[cipher.length];
            iv = iv.clone();
            for (int i = 0; length > 0; length -= 16, i += 16) {
                engine.processBlock(cipher, i, plain, i);
                for (int j = 0; j < 16; j++) {
                    plain[j + i] = ((byte) (plain[i + j] ^ iv[j]));
                }
                System.arraycopy(cipher, i, iv, 0, 16);
            }

            byte[] res;
            if (paddingMode == SM4_PKCS8_PADDING) {
                res = padding(plain, SM4_DECRYPT);
            } else {
                res = plain;
            }
            return res;
        }

        private static byte[] padding(byte[] input, int mode) {
            if (input == null) {
                return null;
            }

            byte[] ret;
            if (mode == SM4_ENCRYPT) {
                int p = 16 - input.length % 16;
                ret = new byte[input.length + p];
                System.arraycopy(input, 0, ret, 0, input.length);
                for (int i = 0; i < p; i++) {
                    ret[input.length + i] = (byte) p;
                }
            } else {
                int p = input[input.length - 1];
                ret = new byte[input.length - p];
                System.arraycopy(input, 0, ret, 0, input.length - p);
            }
            return ret;
        }
    }
}
