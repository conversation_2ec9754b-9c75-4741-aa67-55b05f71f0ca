package com.ets.apply.application.infra.relation;

import java.util.function.BiConsumer;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.common.base.BaseEntityRelation;

public class AfterSalesReviewBindVehicleRelation extends BaseEntityRelation<AftersalesReviews, AftersalesReviewsVehicles> {

    @Override
    public SFunction<AftersalesReviewsVehicles, Object> getAffiliatedColumn() {
        return AftersalesReviewsVehicles::getReviewSn;
    }

    @Override
    public BiConsumer<AftersalesReviews, AftersalesReviewsVehicles> getEntityColumn() {
        return Af
    }

    @Override
    public SFunction<AftersalesReviews, Object> getMasterColumn() {
        return AftersalesReviews::getReviewSn;
    }
    
}
