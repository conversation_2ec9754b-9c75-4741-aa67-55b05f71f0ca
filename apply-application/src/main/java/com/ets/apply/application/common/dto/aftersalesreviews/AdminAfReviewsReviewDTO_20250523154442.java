package com.ets.apply.application.common.dto.aftersalesreviews;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AdminAfReviewsReviewDTO {

    @NotBlank(message = "审核单流水号不能为空")
    private String reviewSn;

    @NotNull(message = "审核状态不能为空")
    private Integer reviewStatus;

    
    private String rejectReason = "";
}
