package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.CallPhpBaseFeign;
import com.ets.apply.application.common.dto.request.notice.MinaNoticeSendDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class CallPhpBaseFallbackFactory implements FallbackFactory<CallPhpBaseFeign> {

    @Override
    public CallPhpBaseFeign create(Throwable cause) {
        return new CallPhpBaseFeign() {

            @Override
            public String sendMinaNotice(MinaNoticeSendDTO minaNoticeSendDTO){
                return JsonResult.error("请求PHP base 服务发送消息失败: " + cause.getMessage()).toString();
            }
        };
    }
}