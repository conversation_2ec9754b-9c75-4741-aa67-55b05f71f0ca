package com.ets.apply.application.common.consts.order;

import lombok.Getter;

import java.util.*;
import java.util.stream.IntStream;

@Getter
public enum FlowTypeEnum {
    FLOW_NORMAL(1, "普通"),
    FLOW_GATE_PROMOTION_V2(2, "活动模式"),
    FLOW_CMB(3, "招商银行办理"),
    FLOW_CATE_NINGXIA(4, "宁夏银行"),
    FLOW_SIGN_BANK(5, "银行签约"),
    FLOW_PRE_PUSH(6, "普通快发"),
    FLOW_TAXI_COMPANY(7, "出租车"),
    FLOW_VEHICLE_SERVICE(8, "微信支付（无保证金）"),
    FLOW_WE_BANK(9, "微众银行"),
    FLOW_PINGAN_GOOD_CAR_OWNER(10, "平安好车主"),
    FLOW_MINA_PRE_PUSH_BIND_BANK(11, "小程序指定卡快发"),
    FLOW_MINA_PRE_PUSH_DEPOSIT(12, "小程序保证金快发"),
    FLOW_MINA_V5_NORMAL(13, "V5快发"),
    FLOW_SIGN_BANK_NEED_PAY(14, "建行直连（线上）"),
    FLOW_CUSTOMER_TRANSLATE(15, "广发苏通联名卡"),
    FLOW_H5_BIND_BANK(16, "H5绑定银行卡"),
    FLOW_CREDIT_CARD(17, "信用卡"),
    FLOW_BAI_DU(18, "百度快发"),
    FLOW_PRE_PUSH_DEPOSIT(19, "保证金普通快发"),
    FLOW_TRUCK(20, "货车申办"),
    FLOW_TRUCK_WX_PAY(21, "江苏货车微信支付"),
    FLOW_CAR_OWNER_SERVICE(22, "车主卡收费模式"),
    FLOW_STD(23, "STD快发"),
    FLOW_CAR_OWNER_SERVICE_CREDIT(24, "车主卡收费模式(信用卡办理）"),
    FLOW_TRUCK_WX_PAY_MENGTONG(25, "内蒙货车"),
    FLOW_OFFLINE_DELIVERY(26, "客车自提"),
    FLOW_QUICK_SHIP_V2(27, "申办2.0 快发"),
    FLOW_OFFLINE_PREPOSE(28, "ETC前装"),
    FLOW_CANCEL_REAPPLY(30, "注销重办流程"),
    FLOW_PRODUCT_ORDER(31, "商品订单"),
    FLOW_TRUCK_WX_PAY_YUETONG(32, "货车微信支付申办模式【粤通】"),
    FLOW_TRUCK_WX_PAY_YUNTONG(33, "货车微信支付申办模式【云南】"),
    FLOW_TRUCK_WX_PAY_JINGTONG(34, "货车微信支付申办模式【天津】"),
    FLOW_REAPPLY_QUICK_SHIP_V3(35, "快发重办模式"),
    FLOW_QUICK_SHIP_V3(36, "新快发模式3.0"),
    FLOW_QUICK_SHIP_CREDIT_CARD(37, "信用卡快发模式"),
    FLOW_QUICK_MACHINE_SALES(38, "自助机售卖模式"),
    FLOW_REAPPLY_CREDIT_CARD(40, "信用卡重办"),
    FLOW_QUICK_SHIP_CREDIT_CARD_V3(41, "信用卡快发模式-注销重办"),
    FLOW_TRUCK_QUICK_SHIP(42, "货车快发"),
    FLOW_CAR_SUPER_QUICK_SHIP(43, "超快发"),
    FLOW_CAR_CREDIT_CARD_SWITCH(44, "信用卡0元办理，可切换银行模式"),
    FLOW_JSUTONG_WL_PREPOSE(45, "江苏网路9901 前装流程"),
    FLOW_COUPON_QUALIFY(46, "用券资格流程"),
    FLOW_CANCEL_REAPPLY_SUPER_SHIP_GUIDE(47, "注销重办流程-超快发"),
    FLOW_CAR_SUPER_QUICK_SHIP_JSH5(48, "江苏H5超快发"),
    FLOW_DEPOSIT_BIND_BANK_JSH5(49, "江苏H5保证金指定卡"),
    FLOW_TRUCK_QUICK_SHIP_JSH5(50, "江苏H5货车快发"),
    FLOW_PRODUCT_ORDER_JSH5(51, "商品订单-京东商品申办-江苏H5"),
    FLOW_REAPPLY_QUICK_SHIP_V3_JSH5(52, "快发重办模式-江苏H5"),
    FLOW_CANCEL_REAPPLY_JSH5(53, "注销重办流程-江苏H5"),
    FLOW_CAR_SUPER_QUICK_SHIP_JSH5_ACTIVATE(54, "江苏H5激活超快发"),
    FLOW_PRODUCT_ORDER_JSH5_ACTIVATE(55, "商品订单-京东商品申办-江苏H5激活"),
    FLOW_DEPOSIT_BIND_BANK_JSH5_ACTIVATE(56, "江苏H5激活保证金指定卡"),
    FLOW_TRUCK_QUICK_SHIP_JSH5_ACTIVATE(57, "江苏H5激活货车快发"),
    FLOW_CANCEL_REAPPLY_SUPER_SHIP(60, "售后注销重办超快发"),
    FLOW_CAR_SUPER_QUICK_SHIP_ADDRESS_BEHIND(61, "客车超快发地址后置"),
    FLOW_CAR_ADDRESS_BEHIND_H5_ACTIVATE(62, "客车H5激活地址后置"),
    FLOW_CAR_CREDIT_CARD_SWITCH_NEW(63, "信用卡切换模式-新流程"),
    FLOW_CAR_SUPER_QUICK_SHIP_NEW(64, "超快发-新流程"),
    FLOW_CAR_SELF_SUPPORT(65, "业务自营流程"),
    FLOW_TRUCK_PRODUCT_ORDER(66, "货车电商模式"),
    FLOW_PRODUCT_ORDER_CREDIT_CARD(67, "商城订单信用卡"),
    ;


    private final Integer key;

    private final String desc;

    public static final Map<Integer, String> map;

    FlowTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    static {
        FlowTypeEnum[] enums = FlowTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(
                LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getKey(), enums[index].getDesc()),
                Map::putAll
        );
    }
    public static String getDescByCode(int code) {
        for (FlowTypeEnum node : FlowTypeEnum.values()) {
            if (node.getKey() == code) {
                return node.getDesc();
            }
        }

        return "FlowType:"+code;
    }
    public static Integer getIssueServiceType(Integer code) {
        HashMap<Integer,Integer> hashMap = new HashMap<>();
        hashMap.put(FlowTypeEnum.FLOW_JSUTONG_WL_PREPOSE.getKey(),2);
        if(hashMap.containsKey(code)){
            return hashMap.get(code);
        }
        return 0;
    }
    /*
     * 检查是否注销重办类型
     */

    public static Boolean checkIsReapply(Integer flowType) {
        if(Arrays.asList(
                FlowTypeEnum.FLOW_CANCEL_REAPPLY.getKey(),
                FlowTypeEnum.FLOW_CANCEL_REAPPLY_SUPER_SHIP_GUIDE.getKey(),
                FlowTypeEnum.FLOW_CANCEL_REAPPLY_SUPER_SHIP.getKey(),
                FlowTypeEnum.FLOW_CANCEL_REAPPLY_JSH5.getKey()
        ).contains(flowType)){
            return true;
        }
        return false;
    }
    public static FlowTypeEnum getNodeByFLowType(Integer flowType) {
        for (FlowTypeEnum node : FlowTypeEnum.values()) {
            if (Objects.equals(node.getKey(), flowType)) {
                return node;
            }
        }
        return null;
    }
}
