package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

@Data
public class CreditCardLogDTO {
    @NotEmpty(message = "流水号不能为空")
    private String orderSn;

    @Min(value = 1, message = "页码最少为1")
    private Integer pageNum = 1;

    @Min(value = 1, message = "每页条数最少为1")
    @Max(value = 100, message = "每页条数最多为100")
    private Integer pageSize = 20;
}
