package com.ets.apply.application.common.utils;


import com.ets.apply.application.common.consts.system.EtcConstants;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

public class UserUtil {

    /**
     * 获取网关通过header传递过来的uid，仅限http请求
     *
     * @return
     */
    public static Long getUid() {
        HttpServletRequest request = RequestHelper.getHttpServletRequest();
        String uidString = RequestHelper.getAllHeaders(request).get(EtcConstants.AUTHORIZE_USER_ID);
        if (StringUtils.isEmpty(uidString)) {
            ToolsHelper.throwException("请先登录");
        }
        long uid = Long.parseLong(uidString);

        if (uid <= 0) {
            ToolsHelper.throwException("请先登录");
        }

        return uid;
    }
    /*
     * 获取后端登录的loginCode
     */
    public static String getLoginCode() {
        return RequestHelper.getHttpServletRequest().getHeader(EtcConstants.AUTHORIZE_LOGIN_CODE);
    }


}
