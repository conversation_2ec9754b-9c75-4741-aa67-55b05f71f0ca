package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallMqProducerFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.mqProducer.MqEventTriggerDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用PHP队列
 */
@FeignClient(
        url = "http://golden-mq-producer.dev:11000/",
        name = "CallMqProducerFeign",
        fallbackFactory = CallMqProducerFallbackFactory.class
)
public interface CallMqProducerFeign {

    @PostMapping(value = "mq/producer/sendMsg", headers = {"content-Type=application/json"})
    String eventTrigger(@RequestBody MqEventTriggerDTO params);

}
