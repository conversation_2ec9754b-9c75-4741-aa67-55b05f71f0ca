package com.ets.apply.application.app.event.bean;

import com.ets.apply.application.app.event.*;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class EventBeans {

    public final static String ORDER_ACTIVATED_BEAN = "orderActivatedEventBean";

    @Bean(value = ORDER_ACTIVATED_BEAN)
    public OrderActivatedEvent orderActivatedEvent() {
        return new OrderActivatedEvent("");
    }

}
