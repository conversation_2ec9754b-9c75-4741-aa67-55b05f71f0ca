package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.plate-no-check-unique")
public class PlateNoCheckUniqueConfig {

    /**
     * 是否使用缓存
     */
    private Boolean checkWithCache;

    /**
     * 有效时间- 秒
     */
    private Integer effectSeconds;

    /**
     * 卡方code
     */
    private String issuerCode;
}
