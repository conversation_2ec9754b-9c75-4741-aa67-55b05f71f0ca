package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CallSzUnicomZopFeign;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Map;

@Component
public class CallSzUnicomZopFallbackFactory implements FallbackFactory<CallSzUnicomZopFeign> {

    @Override
    public CallSzUnicomZopFeign create(Throwable cause) {
        return new CallSzUnicomZopFeign() {
            @Override
            public String checkUser(URI uri, Map<String, String> map){
                return JsonResult.error("checkUser 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String selectNum(URI uri, Map<String, String> map){
                return JsonResult.error("selectNum 请求第三方服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String safeCode(URI uri, Map<String, String> map){
                return JsonResult.error("safeCode 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String checkCode(URI uri, Map<String, String> map){
                return JsonResult.error("checkCode 请求第三方服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String checkRisk(URI uri, Map<String, String> map){
                return JsonResult.error("checkRisk 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String createOrderIntention(URI uri, Map<String, String> map){
                return JsonResult.error("createOrderIntention 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String createOrderIntentionFormal(URI uri, Map<String, String> map){
                return JsonResult.error("createOrderIntentionFormal 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String orderGetCard(URI uri, Map<String, String> map){
                return JsonResult.error("orderGetCard 请求第三方服务失败: " + cause.getMessage()).toString();
            }


        };
    }
}