package com.ets.apply.application.infra.entity.aftersalesreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@TableName("etc_aftersales_reviews_vehicles")
public class AftersalesReviewsVehicles extends BaseEntity<AftersalesReviewsVehicles> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 数据类型[1-审核资料 2-申办资料]
     */
    private Integer dataType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 车辆类型
     */
    private String type;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 核定载人数
     */
    private String passengers;

    /**
     * 行驶证正面
     */
    private String frontImgUrl;

    /**
     * 行驶证反面
     */
    private String backImgUrl;

    /**
     * 车头正面照
     */
    private String frontCarImgUrl;

    /**
     * 车内照
     */
    private String gearActivateImgUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
