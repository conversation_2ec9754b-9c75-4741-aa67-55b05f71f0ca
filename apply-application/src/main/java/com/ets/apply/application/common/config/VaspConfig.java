package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.vasp")
public class VaspConfig {

    /**
     * 商户id
     */
    private String agentId;

    /**
     * 秘钥
     */
    private String secret;

    /**
     * 调用域名
     */
    private String url;
}
