package com.ets.apply.application.common.consts.cmbc;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 最终审核
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum CmbcFinalApproveStatusEnum {
    // 新用户
    FINAL_REJECTED_ENUM("Z0","终审拒绝"),
    FINAL_AUDIT_ENUM("Z1", "终审通过"),
    FINAL_CANCEL_ENUM("Z2", "终审取消");

    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (CmbcFinalApproveStatusEnum node : CmbcFinalApproveStatusEnum.values()) {
            if (Objects.equals(node.getCode(), code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}
