package com.ets.apply.application.common.dto.aftersalesreviews;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 取消售后审核单请求参数
 */
@Data
public class CancelAfterSalesReviewDTO {
    
    /**
     * 审核单流水号
     */
    @NotBlank(message = "审核单流水号不能为空")
    private String reviewSn;

    /**
     * 取消原因
     */
    @NotBlank(message = "取消原因不能为空")
    private String cancelReason = "";
}
