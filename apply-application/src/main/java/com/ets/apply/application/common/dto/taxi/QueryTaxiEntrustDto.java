package com.ets.apply.application.common.dto.taxi;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

@Data
public class QueryTaxiEntrustDto {
    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 支付模式
     */
    @Alias("payment_mode")
    private Integer paymentMode;

    /**
     * 车牌号
     */
    @Alias("plate_no")
    private String plateNo;

    /**
     * 卡ID
     */
    @Alias("card_id")
    private Integer cardId;
}
