package com.ets.apply.feign.feign;

import com.ets.apply.feign.fallback.ApplyOrderFallbackFactory;
import com.ets.apply.feign.request.ApplyOrderSnsDTO;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;
import java.util.List;

@FeignClient(
        url = "${microUrls.apply:http://apply-application:20070}",
        name = "ApplyGetOrderFeign",
        contextId = "apply-order",
        path = "/apply-order",
        fallbackFactory = ApplyOrderFallbackFactory.class
)
public interface ApplyOrderFeign {

    @RequestMapping("/getOrderListBySn")
    JsonResult<List<OrderResponse>> getOrderListBySn(@Valid ApplyOrderSnsDTO dto);

}

