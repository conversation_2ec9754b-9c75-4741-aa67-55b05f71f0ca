package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 导入批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_import_batch")
public class ImportBatchEntity extends BaseEntity<ImportBatchEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批次号
     */
    private String bathNo;

    /**
     * 订单类型: 1商城订单
     */
    private Integer orderType;

    /**
     * 导入文件名称
     */
    private String fileName;

    /**
     * 数据记录数量
     */
    private Integer recordCount;

    /**
     * 数据内容，json格式
     */
    private String content;

    /**
     * 数据内容md5
     */
    private String contentSign;

    /**
     * 状态:0未处理,1处理中,2全部处理成功,3部分失败
     */
    private Integer status;

    private String loginCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
