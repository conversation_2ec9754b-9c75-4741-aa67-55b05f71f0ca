package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.Min;

@Data
public class CreditCardListDTO {

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;

    /**
     * 车牌号
     */
    private String plateNo;

    private Long uid;

    private String orderSn;

    private Integer whichBank;

    private Integer status;

    private String auditTimeStart;
    private String auditTimeEnd;

    private String activateTimeStart;
    private String activateTimeEnd;

    private String firstUseDateStart;
    private String firstUseDateEnd;

    private String createTimeStart;
    private String createTimeEnd;

    private String classify;

    private Integer auditStatus;

    private Integer firstUseStatus;

    private Integer activatedStatus;


    private Integer referType;

    private Integer subReferType;


}
