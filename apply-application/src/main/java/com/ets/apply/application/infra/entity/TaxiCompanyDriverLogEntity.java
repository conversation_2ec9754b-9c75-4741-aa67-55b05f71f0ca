package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 出租车司机操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_taxi_company_driver_log")
public class TaxiCompanyDriverLogEntity extends BaseEntity<TaxiCompanyDriverLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志类型
     */
    private Integer logType;

    /**
     * 出租车司机id
     */
    private Integer driverId;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
