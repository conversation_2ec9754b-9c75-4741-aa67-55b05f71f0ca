package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "activity")
public class ActivityConfig {

    private OrderCondition mengNiu;

    @Data
    public static class OrderCondition {
        private String payStartTime;
        private String payEndTime;
        private List<Integer> orderStatus;
        private String expiredTime;
    }
}
