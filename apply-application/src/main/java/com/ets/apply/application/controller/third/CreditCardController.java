package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.creditCard.CgbCreditBusiness;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.dto.request.bank.cgb.CgbReceiveDataDto;
import com.ets.apply.application.common.dto.request.bank.common.CheckCanApply;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardApplyStatusDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardBankInfoDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCallBackDTO;
import com.ets.apply.application.common.dto.request.creditCard.*;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.creditCard.*;
import com.ets.apply.application.common.vo.creditCard.pab.CheckNewUserByPhoneVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/third/creditCard")
@RefreshScope
@RestController
@Slf4j
public class CreditCardController {

    @Autowired
    private CreditCardBusiness creditCardBusiness;

    @Autowired
    private CgbCreditBusiness cgbCreditBusiness;

    /**
     * 申请信用卡
     */
    @PostMapping("/applyOrder")
    public JsonResult<CreditCardApplyOrderVO> applyOrder(@RequestBody @Valid ApplyOrderDTO applyOrderDTO) {
        return JsonResult.ok(creditCardBusiness.applyOrder(applyOrderDTO));
    }


    @PostMapping("/callback")

    public JsonResult<CreditCardCallBackVO> callBack(@RequestBody CreditCardCallBackDTO dto){
        return JsonResult.ok(creditCardBusiness.callback(dto));
    }

    /**
     * 查询信用卡状态
     * @param dto
     * @return
     */
    @PostMapping("/bankStatus")
    public JsonResult<CreditCardBankInfoVO> bankStatus(@RequestBody(required = false) @Valid CreditCardBankInfoDTO dto) {
        return JsonResult.ok(creditCardBusiness.bankStatus(dto));
    }

    /**
     * 查询信用卡状态
     * @param dto
     * @return
     */
    @PostMapping("/checkNewUserByPhone")
    public JsonResult<CheckNewUserByPhoneVO> checkNewUserByPhone(@RequestBody(required = false) @Valid CheckByPhoneDTO dto) {
        return JsonResult.ok(creditCardBusiness.checkNewUserByPhone(dto));
    }

    @PostMapping("/receiveCgbOrder")
    public Map<String, Object> receiveOrder(@RequestBody @Valid CgbReceiveDataDto data) throws Exception {
        return cgbCreditBusiness.receiveData(data);
    }


}
