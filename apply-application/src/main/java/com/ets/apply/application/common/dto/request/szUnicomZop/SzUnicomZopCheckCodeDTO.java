package com.ets.apply.application.common.dto.request.szUnicomZop;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class SzUnicomZopCheckCodeDTO {
    @NotNull(message = "身份证不能为空")
    private String certNo;
    @NotNull(message = "渠道编码不能为空")
    private String channel;
    @NotNull(message = "手机号码不能为空")
    private String contactNum;
    @NotNull(message = "验证码不能为空")
    private String safeCode;
}
