package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ThirdTypeEnum {

    De<PERSON>ult(0, "ETC助手"),
    <PERSON><PERSON><PERSON>(1, "滴滴"),
    <PERSON><PERSON><PERSON>(2, "微车"),
    <PERSON><PERSON><PERSON><PERSON>(3, "快狗"),
    <PERSON><PERSON>Car(4, "腾讯汽车"),
    <PERSON><PERSON><PERSON>(5, "御途"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(6, "货拉拉"),
    <PERSON>tcMember(7, "ETC会员"),
    <PERSON><PERSON><PERSON>(8, "途虎"),
    <PERSON><PERSON><PERSON><PERSON>(9, "众安"),
    <PERSON><PERSON><PERSON><PERSON>(10, "壁虎"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(11, "广点通"),
    TxMap(12, "腾讯地图"),
    <PERSON><PERSON>(16, "招行"),
    <PERSON><PERSON><PERSON>(17, "百度"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(30, "百度小程序");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ThirdTypeEnum node : ThirdTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
