package com.ets.apply.application.common.consts.issueService;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum IssueServiceStatusEnum {

    STATUS_CREATED(0, 1,"已创建"),

    STATUS_PAID(1, 3,"已支付"),

    STATUS_FINISHED(6, 5,"已签约"),

    STATUS_VOID(7, 4,"已注销"),

    STATUS_CANCELED(9, 4,"已取消");

    private final int code;
    private final int orderCenterStatus;
    private final String description;

    public static String getDescByCode(int code) {
        for (IssueServiceStatusEnum node : IssueServiceStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static int getOrderCenterStatus(int code) {
        for (IssueServiceStatusEnum node : IssueServiceStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getOrderCenterStatus();
            }
        }

        return 0;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(IssueServiceStatusEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
