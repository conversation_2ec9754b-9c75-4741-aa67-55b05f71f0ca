package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.SearchResult;
import com.ets.apply.application.infra.mapper.SearchResultMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 全局搜索结果映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
@DS("db-etc")
public class SearchResultService extends BaseService<SearchResultMapper, SearchResult> {

}
