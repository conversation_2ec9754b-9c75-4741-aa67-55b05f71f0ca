package com.ets.apply.application.common.dto.request.orderAftersales;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class OrderAftersalesOrderChangeDTO {
    @JsonProperty(value = "app_id")
    private String appId = "10000";

    @JsonProperty(value = "issuer_code")
    private String issuerCode;

    private dataParams params;


    @Data
    public static class dataParams {

        @JsonProperty(value = "order_sn")
        private String orderSn;

        private String operate;

        @Length(min = 0, max = 200, message = "申请原因请输入长度300以内的字符")
        private String msg;
    }


    public OrderAftersalesOrderChangeDTO(String orderSn,String operate,String msg){
        this.params = new dataParams();
        this.params.setOrderSn(orderSn);
        this.params.setOperate(operate);
        this.params.setMsg(msg);
    }
}
