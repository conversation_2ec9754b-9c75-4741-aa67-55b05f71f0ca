package com.ets.apply.application.app.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.business.productOrder.ProductOrderThirdTaskBusiness;
import com.ets.apply.application.app.factory.productOrder.ProductOrderFactory;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.service.thirdPartner.WecarService;
import com.ets.apply.application.app.thirdservice.feign.*;
import com.ets.apply.application.app.thirdservice.request.LogisticsFindByOrderSnDTO;
import com.ets.apply.application.app.thirdservice.response.LogisticsVO;
import com.ets.apply.application.common.bo.pay.PayGateWayRefundBO;
import com.ets.apply.application.common.bo.pay.PrepayParamBO;
import com.ets.apply.application.common.bo.productOrder.*;
import com.ets.apply.application.common.bo.source.SourceCategoryBO;
import com.ets.apply.application.common.config.WecarConfig;
import com.ets.apply.application.common.config.queue.PayGatewayConfig;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.common.consts.card.CardIssuerIdEnum;
import com.ets.apply.application.common.consts.order.FlowTypeEnum;
import com.ets.apply.application.common.consts.payment.PayGatewayConst;
import com.ets.apply.application.common.consts.productOrder.*;
import com.ets.apply.application.common.consts.productPackage.ProductPackageIsShowAddEnum;
import com.ets.apply.application.common.consts.redisCache.RedisCacheKeyConstants;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.consts.wecar.WecarStatusEnum;
import com.ets.apply.application.common.consts.wecar.WecarSwitchLogisticsStatusEnum;
import com.ets.apply.application.common.consts.wecar.WecarSwitchStatusEnum;
import com.ets.apply.application.common.dto.productOrder.NotifyActivateDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderChooseBankDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductRefundDTO;
import com.ets.apply.application.common.dto.request.creditCard.UnbindByParamsDTO;
import com.ets.apply.application.common.dto.request.wecar.*;
import com.ets.apply.application.common.utils.DesensitizeUtil;
import com.ets.apply.application.common.utils.TokenUtils;
import com.ets.apply.application.common.vo.wecar.*;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerCacheEntity;
import com.ets.apply.application.infra.service.*;
import com.ets.apply.feign.request.ProductOrderExternalCreateDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderCancelDTO;
import com.ets.apply.application.common.dto.request.productOrder.*;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.common.utils.PlateUtils;
import com.ets.apply.application.common.vo.productOrder.*;
import com.ets.apply.feign.request.GetFinishOrdersDTO;
import com.ets.apply.feign.response.PaymentRefundResponse;
import com.ets.apply.feign.response.ProductOrderExternalCreateVO;
import com.ets.apply.feign.response.ProductOrderResponse;
import com.ets.base.feign.feign.LbsFeign;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ProductOrderBusiness extends BaseBusiness {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductOrderLogService productOrderLogService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;

    @Autowired
    private LbsFeign lbsFeign;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private CallPhpPayFeign callPhpPayFeign;

    @Autowired
    private ProductPackageService productPackageService;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisDefaultTemplate;

    @Autowired
    private DeliveryFeign deliveryFeign;

    @Autowired
    private WecarService wecarService;

    @Autowired
    private ProductPackageBusiness productPackageBusiness;

    @Autowired
    private WecarConfig wecarConfig;

    @Autowired
    private SourceBusiness sourceBusiness;

    @Autowired
    private ProductOrderThirdTaskBusiness productOrderThirdTaskBusiness;

    @Autowired
    private ApplyConfigService applyConfigService;

    @Autowired
    private PayGatewayFeign payGatewayFeign;
    @Autowired
    protected PayGatewayConfig payGatewayConfig;

    @Autowired
    private CallProductOrderThirdNotifyFeign callProductOrderThirdNotifyFeign;

    @Autowired
    private CreditCardBusiness creditCardBusiness;
    @Autowired
    private MapThirdPartnerCacheService mapThirdPartnerCacheService;

    public void createOrder(ProductOrderCreateDTO dto, String loginCode) {

        // 检查ThirdOrderSn
        if (productOrderService.isExistThirdOrderSn(dto.getThirdOrderSn())) {
            ToolsHelper.throwException("第三方订单号已存在");
        }

        List<String> packageSnList =  productPackageBusiness.getAdminPackageSnList(loginCode);
        if (packageSnList == null || packageSnList.isEmpty()) {
            ToolsHelper.throwException("产品包权限为空：" + loginCode);
        }
        if (! packageSnList.contains(dto.getPackageSn())) {
            ToolsHelper.throwException("不支持创建该产品包");
        }

        BigDecimal packagePrice = getPackagePrice(dto.getPackageSn());
        if (packagePrice == null) {
            ToolsHelper.throwException("产品包不正确");
        }
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if(ProductPackageIsShowAddEnum.SHOW_ADD_NO.getValue().equals(productPackageEntity.getIsShowAdd())){
            ToolsHelper.throwException("产品包不支持购买");
        }

        // 地址去空格
        dto.setSendAddress(dto.getSendAddress().replace(" ", "").trim());

        String sendArea = "";
        try {
            sendArea = getAreaByAddress(dto.getSendAddress());
        } catch (Exception e) {
            ToolsHelper.throwException("收货地址解析错误：" + e.getMessage());
        }

        // 地址解析核对
        this.addressAndAreaValidation(dto.getSendAddress(), sendArea);
        ProductOrderEntity entity = new ProductOrderEntity();
        // 属性设置
        String productOrderSn = ToolsHelper.genNum(redisPermanentTemplate, "productOrderSn", appConfig.getEnv(), 8);
        entity.setProductOrderSn(productOrderSn);
        entity.setThirdOrderSn(dto.getThirdOrderSn().trim());
        // 有传车牌还是要记录车牌的，没传车牌就不记录车牌
        if (StringUtils.isNotEmpty(dto.getPlateNo())) {
            String plateNo = dto.getPlateNo().trim();
            entity.setPlateNo(plateNo);
            entity.setPlateColor(PlateUtils.getPlateColorByPlateNo(plateNo));
        }
        // 产品包sn
        entity.setPackageSn(dto.getPackageSn());
        entity.setSendPhone(dto.getSendPhone().trim());
        entity.setSendName(dto.getSendName().trim());
        entity.setSendArea(sendArea);
        entity.setSendAddress(dto.getSendAddress());
        // 设置价格
        entity.setPaidAmount(dto.getPaidAmount());
        entity.setTotalAmount(packagePrice);
        entity.setEtcPhone(dto.getEtcPhone().trim());
        entity.setOrderStatus(ProductOrderStatusEnum.PAID.getCode());
        // 设置订单来源
        entity.setSource(productPackageEntity.getSource());
        productOrderService.create(entity);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CREATED.getCode(),
                entity,
                entity.getOrderStatus(),
                "提交订单",
                RequestHelper.getAdminOperator()
        );
    }

    /**
     * 外部电商已发货订单同步
     * @param dto
     */
    public ProductOrderExternalCreateVO createOrderFromExternal(ProductOrderExternalCreateDTO dto) {

        ProductOrderExternalCreateVO vo = new ProductOrderExternalCreateVO();
        vo.setPackageSn(dto.getPackageSn());

        BigDecimal packagePrice = getPackagePrice(dto.getPackageSn());
        if (packagePrice == null) {
            ToolsHelper.throwException("产品包不正确");
        }
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if(ProductPackageIsShowAddEnum.SHOW_ADD_NO.getValue().equals(productPackageEntity.getIsShowAdd())){
            ToolsHelper.throwException("产品包不支持购买");
        }

        vo.setSkuSn(productPackageEntity.getPackageInfoObj().getString("goods_sku"));

        // 检查ThirdOrderSn
        ProductOrderEntity exist = productOrderService.findByThirdOrderSn(dto.getThirdOrderSn());
        if (exist != null) {
            //ToolsHelper.throwException("第三方订单号已存在");
            // 按已成功处理
            vo.setProductOrderSn(exist.getProductOrderSn());

            return vo;
        }

        ProductOrderEntity entity = new ProductOrderEntity();
        // 属性设置
        String productOrderSn = ToolsHelper.genNum(redisPermanentTemplate, "productOrderSn", appConfig.getEnv(), 8);
        entity.setProductOrderSn(productOrderSn);
        entity.setThirdOrderSn(dto.getThirdOrderSn().trim());

        // 产品包sn
        entity.setPackageSn(dto.getPackageSn());
        entity.setSendPhone(dto.getSendPhone().trim());
        entity.setSendName(dto.getSendName().trim());
        entity.setSendArea(dto.getSendArea());
        entity.setSendAddress(dto.getSendAddress());
        // 设置价格
        entity.setPaidAmount(dto.getPaidAmount());
        entity.setTotalAmount(packagePrice);
        entity.setEtcPhone(dto.getEtcPhone().trim());
        entity.setOrderStatus(ProductOrderStatusEnum.SHIPPED.getCode());
        entity.setLogisticStatus(ProductOrderLogisticStatusEnum.SHIPPED.getCode());
        entity.setSendTime(dto.getSendTime());
        entity.setLogisticCompany(dto.getLogisticCompany());
        entity.setLogisticNumber(dto.getLogisticNumber());

        // 设置订单来源
        entity.setSource(productPackageEntity.getSource());
        productOrderService.create(entity);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.SHIP.getCode(),
                entity,
                entity.getOrderStatus(),
                "提交订单，标记已发货",
                "系统"
        );

        vo.setProductOrderSn(productOrderSn);

        return vo;
    }


    /**
     * 修改订单
     * @param dto
     */
    public void modify(ProductOrderModifyDTO dto) {
        ProductOrderEntity entity = getProductOrderBySn(dto.getProductOrderSn());

        if (StringUtils.isNotEmpty(entity.getApplyOrderSn())) {
            ToolsHelper.throwException("已绑定申办订单，不能修改数据");
        }

//        String plateNo = dto.getPlateNo().trim();

        String modifiedInfo = "";
        if (StringUtils.isNotEmpty(dto.getPlateNo()) && !dto.getPlateNo().trim().equals(entity.getPlateNo())) {
            // 检查车牌是否占用
            String plateNo = dto.getPlateNo().trim();
            checkPlateNo(plateNo);
            modifiedInfo = modifiedInfo + "将办理车牌号码" + entity.getPlateNo() + "改为" + plateNo + "；";
        }
        if (StringUtils.isNotEmpty(dto.getSendPhone()) && !dto.getSendPhone().equals(entity.getSendPhone())) {
            modifiedInfo = modifiedInfo + "将收货人手机号码" + entity.getSendPhone() + "改为" + dto.getSendPhone() + "；";
        }

        if (StringUtils.isNotEmpty(dto.getEtcPhone()) && !dto.getEtcPhone().equals(entity.getEtcPhone())) {
            modifiedInfo = modifiedInfo + "办理手机号" + entity.getEtcPhone() + "改为" + dto.getEtcPhone() + "；";
        }
        if (dto.getPaidAmount().compareTo(entity.getPaidAmount()) != 0) {
            modifiedInfo = modifiedInfo + "将实际销售价" + entity.getPaidAmount() + "元改为" + dto.getPaidAmount() + "元；";
        }

        if (StringUtils.isEmpty(modifiedInfo)) {
            return;
        }

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, dto.getProductOrderSn())
                .set(StringUtils.isNotEmpty(dto.getSendPhone()), ProductOrderEntity::getSendPhone, dto.getSendPhone())
                .set(StringUtils.isNotEmpty(dto.getEtcPhone()), ProductOrderEntity::getEtcPhone, dto.getEtcPhone())
                .set(ProductOrderEntity::getPaidAmount, dto.getPaidAmount());

        if (StringUtils.isNotEmpty(dto.getPlateNo())) {
            String plateNo = dto.getPlateNo().trim();
            wrapper.set(ProductOrderEntity::getPlateColor, PlateUtils.getPlateColorByPlateNo(plateNo))
                    .set(StringUtils.isNotEmpty(dto.getPlateNo()), ProductOrderEntity::getPlateNo, plateNo)

            ;
        }

        productOrderService.updateByWrapper(wrapper);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.MODIFIED.getCode(),
                entity,
                entity.getOrderStatus(),
                "修改订单（修改内容：" + modifiedInfo + "）",
                RequestHelper.getAdminOperator()

        );
    }

    public ProductOrderSelectOptionVO getSelectOptions(List<String> sourceCodeList) {
        if (ObjectUtils.isNotEmpty(sourceCodeList)) {
            sourceCodeList.add("0");
        }
        List<SourceCategoryBO> sourceCategory = sourceBusiness.getSourceCategory(sourceCodeList);

        ProductOrderSelectOptionVO optionVO = new ProductOrderSelectOptionVO();
        optionVO.setSource(sourceCategory);
        optionVO.setProductOrderStatus(ProductOrderStatusEnum.getLabelList());

        return optionVO;
    }

    public IPage<ProductOrderListVO> getList(ProductOrderListDTO dto) {

        // 分页设置
        IPage<ProductOrderEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPlateNo()), ProductOrderEntity::getPlateNo, dto.getPlateNo())
                .eq(StringUtils.isNotEmpty(dto.getSendPhone()), ProductOrderEntity::getSendPhone, dto.getSendPhone())
                .eq(StringUtils.isNotEmpty(dto.getEtcPhone()), ProductOrderEntity::getEtcPhone, dto.getEtcPhone())
                .eq(StringUtils.isNotEmpty(dto.getSource()), ProductOrderEntity::getSource, dto.getSource())
                .in(ObjectUtil.isNotEmpty(dto.getSourceList()), ProductOrderEntity::getSource, dto.getSourceList())
                .eq(NumberUtil.isPositive(dto.getOrderStatus()), ProductOrderEntity::getOrderStatus, dto.getOrderStatus())
                .eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductOrderEntity::getPackageSn, dto.getPackageSn())
                // 大于等于
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), ProductOrderEntity::getCreatedAt, dto.getCreateTimeBegin())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), ProductOrderEntity::getCreatedAt, dto.getCreateTimeEnd() + " 23:59:59")
                .eq(ProductOrderEntity::getChannelId, ChannelIdEnum.DEFAULT.getCode())
                .eq(StringUtils.isNotEmpty(dto.getProductOrderSn()), ProductOrderEntity::getProductOrderSn, dto.getProductOrderSn())
                .likeRight(StringUtils.isNotEmpty(dto.getThirdOrderSn()), ProductOrderEntity::getThirdOrderSn,
                        dto.getThirdOrderSn())
                .orderByDesc(ProductOrderEntity::getCreatedAt);

        // 普通用户 不显示排除渠道数据
        if (ObjectUtil.isEmpty(dto.getSourceList())) {
            List<String> sourceList = sourceBusiness.getChildSourceList(1);
            wrapper.notIn(ObjectUtil.isNotEmpty(dto.getSourceList()),ProductOrderEntity::getSource, sourceList);
        }

        IPage<ProductOrderEntity> pageList = productOrderService.getPageListByWrapper(oPage, wrapper);
        HashMap<String,String> packageNameMap = new HashMap<>();
        if(CollUtil.isNotEmpty(pageList.getRecords())){
            // 获取pageList 订单中对应产品包packageSn,组合成packageNameMap
            packageNameMap = productPackageBusiness.getPackageNameBySn(
                    pageList.getRecords().stream().map(ProductOrderEntity::getPackageSn).collect(Collectors.toList())
            );
        }


        HashMap<String, String> finalPackageNameMap = packageNameMap;
        return pageList.convert(record -> {
            ProductOrderListVO vo = new ProductOrderListVO();
            BeanUtils.copyProperties(record, vo);
            // finalPackageNameMap 不是空并且包含record.getPackageSn()
            vo.setPackageStr(finalPackageNameMap.get(record.getPackageSn()));

            vo.setSendPhone(DesensitizeUtil.phone(record.getSendPhone()));
            vo.setSendName(DesensitizeUtil.name(record.getSendName()));

            return vo;
        });
    }

    public List<ProductOrderDownloadData> getExportData(ProductOrderListDTO dto) {

        // 时间范围限制在一个月内
        if (StringUtils.isEmpty(dto.getCreateTimeBegin()) || StringUtils.isEmpty(dto.getCreateTimeEnd())) {
            ToolsHelper.throwException("请选择开始和结束时间");
        }
        LocalDateTime beginTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeBegin() + " 00:00:00");
        LocalDateTime endTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeEnd() + " 23:59:59");
        if (beginTime.getYear() != endTime.getYear() || ! beginTime.getMonth().equals(endTime.getMonth())) {
            ToolsHelper.throwException("开始和结束时间需在同一个月份范围");
        }

        // 查询条件设置
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPlateNo()), ProductOrderEntity::getPlateNo, dto.getPlateNo())
                .eq(StringUtils.isNotEmpty(dto.getSendPhone()), ProductOrderEntity::getSendPhone, dto.getSendPhone())
                .eq(StringUtils.isNotEmpty(dto.getEtcPhone()), ProductOrderEntity::getEtcPhone, dto.getEtcPhone())
                .eq(StringUtils.isNotEmpty(dto.getSource()), ProductOrderEntity::getSource, dto.getSource())
                .eq(NumberUtil.isPositive(dto.getOrderStatus()), ProductOrderEntity::getOrderStatus, dto.getOrderStatus())
                .eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductOrderEntity::getPackageSn, dto.getPackageSn())
                // 大于等于
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), ProductOrderEntity::getCreatedAt, beginTime)
                // 小于
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), ProductOrderEntity::getCreatedAt, endTime)
                .eq(ProductOrderEntity::getChannelId, ChannelIdEnum.DEFAULT.getCode())
                .eq(StringUtils.isNotEmpty(dto.getProductOrderSn()), ProductOrderEntity::getProductOrderSn, dto.getProductOrderSn())
                .like(StringUtils.isNotEmpty(dto.getThirdOrderSn()), ProductOrderEntity::getThirdOrderSn, dto.getThirdOrderSn());

        List<ProductOrderEntity> list = productOrderService.getListByWrapper(wrapper);
        List<ProductOrderDownloadData> result = new ArrayList<>();

        HashMap<String, String> packageMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            packageMap = productPackageBusiness.getPackageNameBySn(
                    list.stream().map(ProductOrderEntity::getPackageSn).collect(Collectors.toList())
            );
        }

        HashMap<String, String> finalPackageMap = packageMap;
        list.forEach(record -> {
            ProductOrderDownloadData vo = new ProductOrderDownloadData();
            BeanUtils.copyProperties(record, vo);
            vo.setPackageStr(finalPackageMap.get(record.getPackageSn()));
            vo.setStatusStr(ProductOrderStatusEnum.getDescByCode(record.getOrderStatus()));

            // 物流状态
            vo.setLogisticStatusStr(ProductOrderLogisticStatusEnum.getDescByCode(record.getLogisticStatus()));

            result.add(vo);
        });

        return result;
    }


    public IPage<ProductOrderLogListVO> getLogList(ProductOrderLogListDTO dto) {

        // 分页设置
        IPage<ProductOrderLogEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ProductOrderLogEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ProductOrderLogEntity::getProductOrderSn, dto.getProductOrderSn())
                .orderByAsc(ProductOrderLogEntity::getCreatedAt);

        IPage<ProductOrderLogEntity> pageList = productOrderLogService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            ProductOrderLogListVO vo = new ProductOrderLogListVO();
            BeanUtils.copyProperties(record, vo);

            return vo;
        });
    }

    public ProductOrderEntity getProductOrderBySn(String productOrderSn) {
        ProductOrderEntity entity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (entity == null) {
            ToolsHelper.throwException("记录不存在");
        }

        return entity;
    }

    public ProductOrderDetailVO getDetail(String productOrderSn) {

        ProductOrderEntity entity = getProductOrderBySn(productOrderSn);

        ProductOrderDetailVO vo = new ProductOrderDetailVO();

        BeanUtils.copyProperties(entity, vo);
        vo.setPackageStr(getPackageName(entity.getPackageSn()));
        vo.setIssuerLogisticSn(this.getIssuerLogisticSn(entity.getProductOrderSn(), entity.getLogisticStatus()));

        //vo.setSendPhone(ToolsHelper.desensitize(entity.getSendPhone(), 4, 4));
        //vo.setEtcPhone(ToolsHelper.desensitize(entity.getEtcPhone(), 4, 4));
        //vo.setSendName(ToolsHelper.desensitize(entity.getSendName(), 2 , 1));

        return vo;
    }

    @Transactional
    public void remark(String productOrderSn, String remark) {
        ProductOrderEntity entity = getProductOrderBySn(productOrderSn);

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getRemark, remark);

        productOrderService.updateByWrapper(wrapper);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.REMARK.getCode(),
                entity,
                entity.getOrderStatus(),
                "备注订单（备注内容：" + remark + "）",
                RequestHelper.getAdminOperator()

        );
    }

    public void cancel(String productOrderSn, String reason,String operator) {
        // 状态判断
        ProductOrderEntity entity = getProductOrderBySn(productOrderSn);
        if (!Arrays.asList(
                ProductOrderStatusEnum.PAID.getCode(), ProductOrderStatusEnum.DEFAULT.getCode(),
                ProductOrderStatusEnum.SHIPPED.getCode(), ProductOrderStatusEnum.RECEIVED.getCode()
        ).contains(entity.getOrderStatus())
        ) {
            ToolsHelper.throwException("不能进行该操作");
        }

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getCancelReason, reason)
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode());

        // 统一退款操作方法
        String refundSn = this.productOrderRefund(entity, "订单取消退款",operator);
        if (StringUtils.isNotEmpty(refundSn)) {
            wrapper.set(ProductOrderEntity::getRefundSn, refundSn)
                .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());
            reason = reason + "订单退款（退款单号：" + refundSn + "）";
        }

        productOrderService.updateByWrapper(wrapper);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CANCEL.getCode(),
                entity,
                ProductOrderStatusEnum.CLOSED.getCode(),
                "订单取消(取消原因：" + reason + ")",
                operator
        );
        thirdOrderStatusNotify(entity,ProductOrderStatusEnum.CLOSED.getCode());
    }

    private String productOrderRefund(ProductOrderEntity entity, String refundReason,String operator) {
        ProductOrderRefundBO refundBO = new ProductOrderRefundBO();
        refundBO.setProductOrderSn(entity.getProductOrderSn());
        if (StringUtils.isNotEmpty(entity.getThirdOrderSn())) {
            refundBO.setThirdOrderSn(entity.getThirdOrderSn());
        }
        refundBO.setPaymentSn(entity.getPaymentSn());
        refundBO.setRefundAt(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
        refundBO.setRefundReason(refundReason);
        refundBO.setPaidAmount(entity.getPaidAmount());
        refundBO.setTotalAmount(entity.getTotalAmount());
        refundBO.setHasShip(ObjectUtil.isNotNull(entity.getSendTime()));
        refundBO.setReferValue(entity.getReferValue());
        refundBO.setOperator(operator);
        return ProductOrderFactory.create(entity.getReferType()).refund(refundBO);

    }

    public void refund(String productOrderSn){
        ProductOrderEntity entity = getProductOrderBySn(productOrderSn);
        if (!ProductOrderStatusEnum.REFUNDING.getCode().equals(entity.getOrderStatus())) {
            ToolsHelper.throwException("不能进行该操作");
        }
        //如果是腾讯出行的订单
        if(
            entity.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode()) &&
            entity.getReferValue().equals(ReferValueEnum.PARTNER_VALUE_WECAR.getValue())
        ) {
            //更新为退款中，通知出行状态32，发起退款
            WecarNotifyStatusDTO wecarNotifyStatusDTO = new WecarNotifyStatusDTO();
            wecarNotifyStatusDTO.setStatus(WecarStatusEnum.TO_REFUND.getStatus());
            wecarNotifyStatusDTO.setSpOrderStatus(WecarStatusEnum.getSpOrderStatus(WecarStatusEnum.TO_REFUND.getStatus()));
            wecarNotifyStatusDTO.setLogisticsStatus(WecarSwitchLogisticsStatusEnum.getChangeOutByCode(entity.getLogisticStatus()));
            wecarNotifyStatusDTO.setActionTime(System.currentTimeMillis() / 1000);
            wecarNotifyStatusDTO.setUserCode(entity.getReferUserCode());
            wecarNotifyStatusDTO.setVehicleNo(entity.getPlateNo());
            wecarNotifyStatusDTO.setOutOrderId(entity.getProductOrderSn());
            wecarService.notifyStatus(wecarNotifyStatusDTO);

            // 记录日志
            createLog(
                ProductOrderLogTypeEnum.REFUND.getCode(),
                entity,
                ProductOrderStatusEnum.REFUNDING.getCode(),
                "同步腾讯出行发起退款",
                RequestHelper.getAdminOperator()

            );

            ToolsHelper.throwException("已向腾讯出行发起退款，请等待退款通知");
            return;
        }


        String reason = "订单退款";
        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode())
                .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());

//        if (!entity.getPaymentSn().isEmpty()) {
//            // 调用支付域进行退款
//            PaymentRefundDTO refundDTO = new PaymentRefundDTO();
//            refundDTO.setPaymentSn(entity.getPaymentSn());
//            refundDTO.setReason("订单退款");
//            String refundSn = paymentRefund(refundDTO);
//            wrapper.set(ProductOrderEntity::getRefundSn, refundSn)
//                .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());
//            reason = reason + "订单退款（退款单号：" + refundSn + "）";
//
//        } else {
            // 统一退款操作方法
            String refundSn = this.productOrderRefund(entity, reason,RequestHelper.getAdminOperator());
            wrapper.set(ProductOrderEntity::getRefundSn, refundSn)
                    .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());
            if (StringUtils.isNotEmpty(refundSn)) {
                reason = reason + "订单退款（退款单号：" + refundSn + "）";
            }
//        }
        productOrderService.updateByWrapper(wrapper);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.REFUND.getCode(),
                entity,
                ProductOrderStatusEnum.CLOSED.getCode(),
                reason,
                RequestHelper.getAdminOperator()

        );

        thirdOrderStatusNotify(entity,ProductOrderStatusEnum.CLOSED.getCode());
    }


    // 创建日志记录
    public void createLog(Integer logType, ProductOrderEntity entity, Integer afterStatus, String content, String operator) {

        try {
            ProductOrderLogEntity logEntity = new ProductOrderLogEntity();
            logEntity.setOperateUser(operator);
            logEntity.setProductOrderSn(entity.getProductOrderSn());
            logEntity.setLogType(logType);
            logEntity.setContent(content);
            logEntity.setAfterStatus(afterStatus);
            logEntity.setPreStatus(entity.getOrderStatus());
            logEntity.setLogisticStatus(entity.getLogisticStatus());

            productOrderLogService.create(logEntity);
        } catch (Exception e) {
            log.info("createLog 异常" + e.getMessage());
        }

    }

    public void checkPlateNo(String plateNo) {

        CheckPlateNoDTO dto = new CheckPlateNoDTO();
        dto.setPlateNo(plateNo);
        dto.setPlateColor(PlateUtils.getPlateColorByPlateNo(plateNo));

        String jsonResult = callPhpApplyFeign.checkPlateNo(dto);
        JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
        result.checkError();
    }

    public String getAreaByAddress(String address) {

        return lbsFeign.getAreaByAddress(address).getDataWithCheckError();
    }

    public JSONArray getLogisticInfo(String productOrderSn) {
        ProductOrderEntity entity = getProductOrderBySn(productOrderSn);
        if (StringUtils.isEmpty(entity.getLogisticNumber())) {
            return null;
        }

        ExpressEntity expressEntity = expressService.getByExpressNumber(entity.getLogisticNumber());
        if (expressEntity == null) {
            return null;
        }

        String data = expressEntity.getData();

        return JSON.parseArray(data);
    }

    public long getGoodsInventoryLeft() {
        return 10000 - productOrderService.getWarrantyReceivedCount();
    }

    public String paymentRefund(PaymentRefundDTO paymentRefundDTO) {
        if (paymentRefundDTO.getPaymentSn() == null) {
            return "";
        }
        String jsonResult = callPhpPayFeign.refund(paymentRefundDTO);
//        String jsonResult = "{\"code\":0,\"msg\":\"success\",\"data\":{\"refund_order\":{\"refund_sn\":\"2203281811000000901RF\",\"uid\":\"1991826796\",\"amount\":0.01,\"pay_amount\":\"0.01\",\"wx_amount\":\"0.00\",\"payment_sn\":\"2203281106000001981MP\",\"product_id\":\"2203281106000000581\",\"product_type\":\"47\",\"transaction_id\":\"4200001362202203285931699458\",\"status\":0,\"reason\":\"订单退款\",\"third_notify_url\":\"\",\"refund_type\":0,\"created_at\":\"2022-03-28 18:11:19\",\"updated_at\":\"2022-03-28 18:11:19\",\"id\":709}}}";
        JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
        result.checkError();
        PaymentRefundResponse data = JSON.parseObject(
                result.getData().toString(), new TypeReference<PaymentRefundResponse>() {
                });
        return data.getRefundOrder().getRefundSn();
    }

    /**
     * 获取产品包名称
     *
     * @param productOrderSn
     * @return
     */
    public String getPackageName(String productOrderSn) {
        Boolean hasKey = redisDefaultTemplate.hasKey(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME + productOrderSn);
        if (Boolean.TRUE.equals(hasKey)) {
            return redisDefaultTemplate.opsForValue().get(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME + productOrderSn);
        }
        String packageName = ProductPackageEnum.getDescByCode(productOrderSn);
        if (StringUtils.isEmpty(packageName)) {
            ProductPackageEntity productPackageEntity = productPackageService.getBySn(productOrderSn);
            if (productPackageEntity != null) {
                packageName = productPackageEntity.getPackageName();
            }
        }
        redisDefaultTemplate.opsForValue().set(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME + productOrderSn, packageName);
        redisDefaultTemplate.expire(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME + productOrderSn, 2, TimeUnit.HOURS);
        return packageName;
    }

    /**
     * 获取产品价格
     *
     * @param productOrderSn
     * @return
     */
    private BigDecimal getPackagePrice(String productOrderSn) {
        BigDecimal price = ProductPackageEnum.getPriceByCode(productOrderSn);
        if (price == null) {
            ProductPackageEntity productPackageEntity = productPackageService.getBySn(productOrderSn);
            if (productPackageEntity != null) {
                price = productPackageEntity.getPackageFee();
            }
        }
        return price;
    }


    public HashMap<String, List<Map<String, String>>> getChannelSelectOptions() {
        HashMap<String, List<Map<String, String>>> options = new HashMap<>();
        options.put("productOrderStatus", ChannelProductOrderStatusEnum.getLabelList());
        return options;
    }

    /**
     * 获取审核发货平台发货流水
     *
     * @param productOrderSn
     * @param logisticStatus
     * @return
     */
    public String getIssuerLogisticSn(String productOrderSn, Integer logisticStatus) {

        if (!Arrays.asList(
                ProductOrderLogisticStatusEnum.PUSHED.getCode(),
                ProductOrderLogisticStatusEnum.SHIPPED.getCode(),
                ProductOrderLogisticStatusEnum.RECEIVED.getCode()
        ).contains(logisticStatus)) {
            return "";
        }
        try {
            LogisticsFindByOrderSnDTO findByOrderSnDTO = new LogisticsFindByOrderSnDTO();
            findByOrderSnDTO.setOrderSn(productOrderSn);
            JsonResult<LogisticsVO> logisticsVO = deliveryFeign.findByOrderSn(findByOrderSnDTO);
            return logisticsVO.getData().getLogisticsSn();
        } catch (Exception e) {
            log.info("获取发货单号异常" + e.getMessage());
        }
        return "";

    }

    public void ship(ProductOrderShipDTO shipDTO) {
        try {

            // 先找到对应订单
            ProductOrderEntity orderEntity = productOrderService.getOneByColumn(shipDTO.getOrderSn(), ProductOrderEntity::getProductOrderSn);

            if (ObjectUtil.isNull(orderEntity)) {
                return;
            }

            if (!orderEntity.getOrderStatus().equals(ProductOrderStatusEnum.PAID.getCode())) {
                log.info("ship 京东申办单状态异常，不标记订单已发货：" + shipDTO.getOrderSn() + "订单状态:" + orderEntity.getOrderStatus());
                return;
            }


            // 入库发货信息
            orderEntity.setOrderStatus(ProductOrderStatusEnum.SHIPPED.getCode());
            orderEntity.setLogisticStatus(ProductOrderLogisticStatusEnum.SHIPPED.getCode());
            orderEntity.setLogisticCompany(shipDTO.getExpressCorp());
            orderEntity.setLogisticNumber(shipDTO.getExpressNumber());
            orderEntity.setMsg("");
            orderEntity.setSendTime(shipDTO.getExpressTime());
            productOrderService.updateById(orderEntity);

            // 记录日志
            createLog(
                    ProductOrderLogTypeEnum.SHIP.getCode(),
                    orderEntity,
                    orderEntity.getOrderStatus(),
                    "审核平台发货，修改订单状态到已发货",
                    "系统"

            );
            // 目前只有第三方合作方才需要进行操作
            if (orderEntity.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())) {
                ProductOrderShipBO shipBO = new ProductOrderShipBO();
                shipBO.setProductOrderSn(orderEntity.getProductOrderSn());
                shipBO.setThirdOrderSn(orderEntity.getThirdOrderSn());
                shipBO.setLogisticCompany(shipDTO.getExpressCorp());
                shipBO.setLogisticNumber(shipDTO.getExpressNumber());
                shipBO.setSendAddress(orderEntity.getSendAddress());
                shipBO.setReferValue(orderEntity.getReferValue());

                ReferTypeEnum referTypeEnum = ReferTypeEnum.getReferTypeEnumByType(orderEntity.getReferType());
                if(ObjectUtil.isNull(referTypeEnum)){
                    log.info("productOrder ship 获取枚举信息失败,类型值"+ orderEntity.getReferType());
                    return ;
                }
                TaskRecordReferTypeEnum taskReferType = referTypeEnum.taskTypeMapEnum(referTypeEnum);
                if(ObjectUtil.isNull(taskReferType)){
                    log.info("productOrder ship 获取枚举信息:taskReferType 失败,类型值"+ orderEntity.getReferType());
                    return ;
                }
                //塞队列进行发货操作
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(orderEntity.getProductOrderSn());
                taskRecordDTO.setReferType(taskReferType.getType());
                taskRecordDTO.setNextExecTime(LocalDateTime.now());
                taskRecordDTO.setNotifyContent(JSON.toJSONString(shipBO));
                TaskFactory.create(taskReferType.getType()).addAndPush(taskRecordDTO);
            }


        } catch (Exception e) {
            log.info(" product ship 处理异常" + e.getMessage());
            ToolsHelper.throwException("请求异常，请稍后再试");
        }
    }


    /**
     * 售后退款
     *
     * @param productOrderSaleAfterDTO
     */
    public void productOrderSalesRefund(ProductOrderSaleAfterDTO productOrderSaleAfterDTO) {
        // 先找到对应订单
        ProductOrderEntity orderEntity = productOrderService.getOneByColumn(productOrderSaleAfterDTO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);

        if (ObjectUtil.isNull(orderEntity)) {
            return;
        }

        if (!Arrays.asList(
                ProductOrderStatusEnum.PAID.getCode(),
                ProductOrderStatusEnum.SHIPPED.getCode(),
                ProductOrderStatusEnum.RECEIVED.getCode(),
                ProductOrderStatusEnum.AFTER_SALE.getCode(),
                ProductOrderStatusEnum.REFUNDING.getCode(),
                ProductOrderStatusEnum.AFTER_SALE.getCode(),
                ProductOrderStatusEnum.AFTER_SALE.getCode()
        ).contains(orderEntity.getOrderStatus())) {
            log.info("ship 京东申办单状态异常，不进行售后处理：" + productOrderSaleAfterDTO.getProductOrderSn() + "订单状态:" + orderEntity.getOrderStatus());
            return;
        }

        ProductOrderSaleAfterBO saleAfterBO = new ProductOrderSaleAfterBO();
        saleAfterBO.setProductOrderSn(orderEntity.getProductOrderSn());
        saleAfterBO.setThirdOrderSn(orderEntity.getThirdOrderSn());
        saleAfterBO.setIsRefund(productOrderSaleAfterDTO.getIsRefund());
        saleAfterBO.setPaidAmount(orderEntity.getPaidAmount());
        saleAfterBO.setTotalAmount(orderEntity.getTotalAmount());

        // 处理售后
        ProductOrderFactory.create(orderEntity.getReferType()).saleAfter(saleAfterBO);

    }

    public void addressAndAreaValidation(String address, String area) {
        if (StringUtils.isEmpty(address)) {
            throw new BizException("地址不能为空");
        }
        if (StringUtils.isEmpty(area)) {
            throw new BizException("区域不能为空");
        }
        String[] splitArea = area.split(" ");
        String areaLast = splitArea[splitArea.length - 1];

        String[] splitAddress = address.split(areaLast);
        String addressLast = splitAddress[splitAddress.length - 1];

        if (!address.endsWith(addressLast)) {
            throw new BizException("地址填写有误:" + addressLast + "不匹配，请检查核对");
        }

    }


    public List<ProductOrderResponse> getFinishedOrders(GetFinishOrdersDTO dto){

        List<ProductOrderEntity> productOrderEntityList = productOrderService.getFinishOrders(dto,wecarConfig.getNeedRefundPackageSns());
        return ListUtils.convertByClass(productOrderEntityList,ProductOrderResponse.class);

    }

    public ProductOrderResponse getByProductOrderSnAndUid(String productOrderSn,Long uid) {
        ProductOrderEntity entity = productOrderService.findByProductOrderSnAndUid(productOrderSn, uid);
        if (entity == null) {
            ToolsHelper.throwException("记录不存在");
        }
        ProductOrderResponse productOrderResponse = new ProductOrderResponse();
        BeanUtils.copyProperties(entity, productOrderResponse);
        return productOrderResponse;
    }

    /**
     * TOD 处理取消逻辑
     * @param cancelDTO
     */
    public void cancelBeforeShipped(ProductOrderCancelDTO cancelDTO, Long uid) {
        // 状态判断
        ProductOrderEntity entity = getProductOrderBySn(cancelDTO.getProductOrderSn());
        if (!Arrays.asList(ProductOrderStatusEnum.PAID.getCode(),
                ProductOrderStatusEnum.DEFAULT.getCode()).contains(entity.getOrderStatus())) {
            ToolsHelper.throwException("取消失败，订单当前状态不允许取消");
        }
        if(SourceEnum.WECAR.getCode().equals(entity.getSource())){
            ToolsHelper.throwException("取消失败，订单来源为腾讯出行，不允许取消");
        }

        //待发货中状态可以取消
        if (!entity.getLogisticStatus().equals(ProductOrderLogisticStatusEnum.UN_PUSH.getCode())) {
            ToolsHelper.throwException("取消失败，订单已发货，请联系客服");
        }
        if (!uid.equals(entity.getUid())) {
            ToolsHelper.throwException("订单不属于当前用户，不能进行该操作");
        }

        String reason = Optional.ofNullable(cancelDTO.getReason()).orElse("申请取消");

        // TODO 推送发货要去尝试取消发货单，下一期再做

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, cancelDTO.getProductOrderSn())
                .set(ProductOrderEntity::getCancelReason, reason)
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode());

        if (ObjectUtil.isNotEmpty(entity.getPaymentSn())) {
            // 统一退款操作方法
            String refundSn = this.productOrderRefund(entity, "订单取消退款","用户"+uid);
            if (StringUtils.isNotEmpty(refundSn)) {
                wrapper.set(ProductOrderEntity::getRefundSn, refundSn)
                        .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());
                reason = reason + "订单退款（退款单号：" + refundSn + "）";
            }
        }

        productOrderService.updateByWrapper(wrapper);
        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CANCEL.getCode(),
                entity,
                ProductOrderStatusEnum.CLOSED.getCode(),
                "订单取消(取消原因：" + reason + ")",
                "用户"
        );
    }

    /**
     * 商城订单激活通知
     */
    public void activateNotify(NotifyActivateDTO dto){
        // 第三方任务处理
        productOrderThirdTaskBusiness.syncTaskByProductOrderSn(dto.getProductOrderSn());
    }

    /*
     * 处理有关售后状态
     */
    public Boolean aftersalesMarkByOrderSn(OrderOrderEntity order, Integer aftersaleStatus, String aftersalesSn) {
        if(!Arrays.asList(
                FlowTypeEnum.FLOW_PRODUCT_ORDER.getKey(),
                FlowTypeEnum.FLOW_PRODUCT_ORDER_JSH5.getKey(),
                FlowTypeEnum.FLOW_PRODUCT_ORDER_JSH5_ACTIVATE.getKey(),
                FlowTypeEnum.FLOW_TRUCK_PRODUCT_ORDER.getKey()
        ).contains(order.getFlowType())){
            return true;
        }
        ProductOrderEntity productOrder = productOrderService.findByApplyOrderSn(order.getOrderSn());
        if(productOrder == null){
            return false;
        }
        switch (aftersaleStatus){
            //创建售后
            case 0:
                if(!Arrays.asList(
                        ProductOrderStatusEnum.REFUNDING.getCode(),
                        ProductOrderStatusEnum.ACTIVATED.getCode(),
                        ProductOrderStatusEnum.CLOSED.getCode(),
                        ProductOrderStatusEnum.AFTER_SALE.getCode()
                ).contains(productOrder.getOrderStatus())){
                    productOrderService.markOrderOnAftersale(productOrder.getProductOrderSn(),ProductOrderStatusEnum.AFTER_SALE.getCode());
                    // 记录日志
                    createLog(
                            ProductOrderLogTypeEnum.CREATED.getCode(),
                           productOrder,
                            productOrder.getOrderStatus(),
                            "售后创建："+aftersalesSn,
                            "system"
                    );
                }
                break;
            //售后完成
            case 1:
                if(!Arrays.asList(
                        ProductOrderStatusEnum.REFUNDING.getCode(),
                        ProductOrderStatusEnum.ACTIVATED.getCode(),
                        ProductOrderStatusEnum.CLOSED.getCode()
                ).contains(productOrder.getOrderStatus())){
                    productOrderService.markOrderOnAftersale(productOrder.getProductOrderSn(),ProductOrderStatusEnum.REFUNDING.getCode());
                    // 记录日志
                    createLog(
                            ProductOrderLogTypeEnum.FINISH_AFTER_SALE.getCode(),
                            productOrder,
                            productOrder.getOrderStatus(),
                            "售后完成："+aftersalesSn,
                            "system"
                    );
                }
                break;
            //售后取消
            case 2:
                if(!Arrays.asList(
                        ProductOrderStatusEnum.AFTER_SALE.getCode()
                ).contains(productOrder.getOrderStatus())){
                    Integer newOrderStatus = ProductOrderStatusEnum.PAID.getCode();
                    //恢复订单状态
                    if(productOrder.getLogisticStatus().equals(ProductOrderLogisticStatusEnum.SHIPPED.getCode())){
                        newOrderStatus = ProductOrderStatusEnum.SHIPPED.getCode();
                    }else if(productOrder.getLogisticStatus().equals(ProductOrderLogisticStatusEnum.RECEIVED.getCode())){
                        newOrderStatus = ProductOrderStatusEnum.RECEIVED.getCode();
                    }
                    productOrderService.markOrderOnAftersale(productOrder.getProductOrderSn(),newOrderStatus);
                    // 记录日志
                    createLog(
                            ProductOrderLogTypeEnum.CANCEL_AFTER_SALE.getCode(),
                            productOrder,
                            productOrder.getOrderStatus(),
                            "售后取消："+aftersalesSn,
                            "system"
                    );
                }
                break;
        }
        return true;
    }


    /*
     * 获取产品列表
     */
    public List<WecarProductVo> getProductListByCompanyId(String companyId) throws BizException {
        String type = "prod";
        String cacheMainKey = "MapCache:ThirdPartner:Prod";
        MapThirdPartnerCacheEntity mapCacheDatas = mapThirdPartnerCacheService.getOneByTypeAndMainKeyAndKey(type,cacheMainKey,companyId);
        if(mapCacheDatas == null){
            ToolsHelper.throwException("配置信息不存在："+companyId);
        }
        return JSONObject.parseArray(mapCacheDatas.getCacheValues(),WecarProductVo.class);
    }

    /*
     * 获取产品列表
     */
    public ProductOrderThirdParamBO getThirdParamsByCompanyId(String companyId) throws BizException {
        ApplyConfigEntity applyConfigEntity = applyConfigService.findOneByDomainKey("apply:"+companyId+":product");
        if(applyConfigEntity == null){
            ToolsHelper.throwException("配置信息不存在："+companyId);
        }
        JSONObject paramsObject = JSON.parseObject(applyConfigEntity.getParams());
        return JSONObject.parseObject(paramsObject.getString("orderParams"), ProductOrderThirdParamBO.class);
    }
    /*
     * 创建电商 订单
     */
    public WecarAddOrderVo addOrder(ThirdAddOrderDTO dto) throws BizException {

        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getApplyProductId());
        if(productPackageEntity == null || productPackageEntity.getStatus() == 2){
            ToolsHelper.throwException("产品包"+dto.getApplyProductId()+"不存在或者已下架");
        }
        // 地址去空格
        dto.setReceiverAddress(dto.getReceiverAddress().replace(" ", "").trim());
        String sendArea = "";
        try {
            sendArea = getAreaByAddress(dto.getReceiverAddress());
        } catch (Exception e) {
            log.info("收货地址解析错误：" + e.getMessage());
            ToolsHelper.throwException("收货地址解析错误：" + e.getMessage());
            //sendArea = "广东省 广州市 番禺区";
        }
        //获取支付参数
        ProductOrderThirdParamBO thirdParams = getThirdParamsByCompanyId(dto.getCompanyId());

        ProductOrderEntity entity = new ProductOrderEntity();
        // 属性设置
        String productOrderSn = ToolsHelper.genNum(redisPermanentTemplate, "productOrderSn", appConfig.getEnv(), 8);
        entity.setProductOrderSn(productOrderSn);
        // 有传车牌还是要记录车牌的，没传车牌就不记录车牌
        if (StringUtils.isNotEmpty(dto.getVehicleNo())) {
            String plateNo = dto.getVehicleNo().trim();
            entity.setPlateNo(plateNo);
            entity.setPlateColor(PlateUtils.getPlateColorByPlateNo(plateNo));
        }
        // 产品包sn
        entity.setPackageSn(dto.getApplyProductId());
        entity.setSendPhone(dto.getMobile().trim());
        entity.setSendName(dto.getReceiver().trim());
        entity.setSendArea(sendArea);
        entity.setSendAddress(dto.getReceiverAddress());
        // 设置价格
        JSONObject jsonObject = JSONObject.parseObject(productPackageEntity.getPackageInfo());
        BigDecimal originalPrice = jsonObject.getBigDecimal("original_price");

        entity.setPaidAmount(productPackageEntity.getPackageFee());
        entity.setTotalAmount(originalPrice);
        entity.setEtcPhone(dto.getMobile().trim());
        entity.setOrderStatus(ProductOrderStatusEnum.DEFAULT.getCode());
        // 设置订单来源
        entity.setSource(productPackageEntity.getSource());
        entity.setReferType(thirdParams.getReferType());
        entity.setReferValue(thirdParams.getReferValue());
        entity.setReferUserCode(dto.getUserCode());
        entity.setThirdOrderSn(dto.getApplyOrderNo());
        productOrderService.create(entity);

        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.CREATED.getCode(),
                entity,
                entity.getOrderStatus(),
                "提交订单",
                dto.getCompanyId()
        );

        return new WecarAddOrderVo(
                entity.getProductOrderSn(),
                WecarStatusEnum.WAIT_PAY.getStatus(),
                entity.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue(),
                entity.getTotalAmount().multiply(BigDecimal.valueOf(100)).longValue()
        );
    }


    public JSONObject prepay(ThirdOrderPrePayDTO dto) {
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getSpOrderId(), ProductOrderEntity::getProductOrderSn);
        if (productOrderEntity == null) {
            ToolsHelper.throwException("商品订单不存在：" + dto.getSpOrderId());
        }
        if (!ProductOrderStatusEnum.DEFAULT.getCode().equals(productOrderEntity.getOrderStatus())) {
            ToolsHelper.throwException("订单状态不正确");
        }
        //获取支付参数
        ProductOrderThirdParamBO payParams = getThirdParamsByCompanyId(dto.getCompanyId());
        // 组装支付参数
        PrepayParamBO bo = new PrepayParamBO();
        bo.setOrderNo(productOrderEntity.getProductOrderSn());
        bo.setOrderType(payParams.getOrderType());
        bo.setUserId(productOrderEntity.getUid());
        bo.setOpenId(dto.getOpenId());
        bo.setPayAmount(productOrderEntity.getPaidAmount());
        bo.setPayWay(payParams.getPayWay());
        bo.setMchType(payParams.getMchType());
        bo.setProductName("商品订单支付: " + productOrderEntity.getProductOrderSn());
        bo.setTimeExpire(15);

        // 调支付服务 获取支付参数，创建支付单
        String ret = payGatewayFeign.prepay(bo);
        JsonResult<String> result = JsonResult.convertFromJsonStr(ret, String.class);
        result.checkError();
        // 返回支付参数
        return JSON.parseObject(result.getData());
    }

    /*
     * 获取订单列表
     */
    public List<ProductOrderThirdListVO> getOrderList(WecarGetOrderListDTO dto) throws BizException {
        List<ProductOrderThirdListVO> productOrderThirdList = new ArrayList<>();
        List<ProductOrderEntity> list = productOrderService.getListByColumn(dto.getUserCode(), ProductOrderEntity::getReferUserCode);
        if(!list.isEmpty()){
            Map<String,WecarProductVo> productPackageMap = new HashMap<>();
            list.forEach(record -> {
                ProductOrderThirdListVO vo = new ProductOrderThirdListVO();
                if(!productPackageMap.containsKey(record.getPackageSn())){
                    productPackageMap.put(record.getPackageSn(),productPackageBusiness.getWecarProductVoByPackageSn(record.getPackageSn()));
                }

                vo.setCardProviderId(productPackageMap.get(record.getPackageSn()).getCardProviderId());
                vo.setCardProviderName(productPackageMap.get(record.getPackageSn()).getCardProviderName());
                vo.setApplyProductId(productPackageMap.get(record.getPackageSn()).getId());
                vo.setApplyProductName(productPackageMap.get(record.getPackageSn()).getAliasName());
                vo.setProductImg(productPackageMap.get(record.getPackageSn()).getProductImg());
                vo.setCreateTime(record.getCreatedAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
                vo.setSpOrderId(record.getProductOrderSn());
                vo.setStatus(record.getOrderStatus());
                vo.setVehicleNo(record.getPlateNo());
                vo.setActualAmount(record.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue());
                productOrderThirdList.add(vo);
            });
        }
        return productOrderThirdList;
    }

    /*
     * 获取产品列表
     */
    public ProductOrderThirdInfoVO getOrderInfo(WecarGetOrderInfoDTO dto) throws BizException {
        //检查订单
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getSpOrderId(), ProductOrderEntity::getProductOrderSn);
        if(productOrderEntity == null){
            ToolsHelper.throwException("该订单号不存在");
        }
        ProductOrderThirdInfoVO productOrderThirdInfoVO = new ProductOrderThirdInfoVO();

        WecarProductVo wecarProductVo = productPackageBusiness.getWecarProductVoByPackageSn(productOrderEntity.getPackageSn());
        if(wecarProductVo != null){
            productOrderThirdInfoVO.setCardProviderId(wecarProductVo.getCardProviderId());
            productOrderThirdInfoVO.setCardProviderName(wecarProductVo.getCardProviderName());
            productOrderThirdInfoVO.setApplyProductId(wecarProductVo.getId());
            productOrderThirdInfoVO.setApplyProductName(wecarProductVo.getAliasName());
            productOrderThirdInfoVO.setProductImg(wecarProductVo.getProductImg());
        }
        productOrderThirdInfoVO.setSpOrderId(productOrderEntity.getProductOrderSn());
        productOrderThirdInfoVO.setVehicleNo(productOrderEntity.getPlateNo());
        productOrderThirdInfoVO.setStatus(productOrderEntity.getOrderStatus());
        productOrderThirdInfoVO.setTransOrderNo(productOrderEntity.getLogisticNumber());
        productOrderThirdInfoVO.setTransCompany(productOrderEntity.getLogisticCompany());
        productOrderThirdInfoVO.setLogisticsStatus(productOrderEntity.getLogisticStatus());
        productOrderThirdInfoVO.setReceiver(productOrderEntity.getSendName());
        productOrderThirdInfoVO.setMobile(productOrderEntity.getSendPhone());
        productOrderThirdInfoVO.setReceiverAddress(productOrderEntity.getSendAddress());
        productOrderThirdInfoVO.setCreateTime(productOrderEntity.getCreatedAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        productOrderThirdInfoVO.setPayTime((productOrderEntity.getPaidTime() != null)?productOrderEntity.getPaidTime().toInstant(ZoneOffset.of("+8")).toEpochMilli():null);
        productOrderThirdInfoVO.setActualAmount(productOrderEntity.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue());

        return productOrderThirdInfoVO;
    }

    /*
     * 取消订单
     */
    public Boolean thridOrderCancelOrder(WecarCancelOrderDTO dto){
        //检查订单
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getSpOrderId(), ProductOrderEntity::getProductOrderSn);
        if(productOrderEntity == null){
            ToolsHelper.throwException("该订单号不存在");
        }
        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        //处理订单状态
        switch (Objects.requireNonNull(ProductOrderStatusEnum.getByCode(productOrderEntity.getOrderStatus()))) {
            //未支付
            case DEFAULT -> {
                //可以直接取消，更新订单数据
                wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderEntity.getProductOrderSn())
                        .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode());
                thirdOrderStatusNotify(productOrderEntity, ProductOrderStatusEnum.CLOSED.getCode());
            }
            //已支付
            case PAID -> {
                //待发货中状态可以取消
                if (!productOrderEntity.getLogisticStatus().equals(ProductOrderLogisticStatusEnum.UN_PUSH.getCode())) {
                    ToolsHelper.throwException("订单不可取消");
                }

                // 统一退款操作方法
                String refundSn = this.productOrderRefund(productOrderEntity, dto.getReason(),dto.getUserCode());
                if (StringUtils.isNotEmpty(refundSn)) {
                    wrapper.set(ProductOrderEntity::getRefundSn, refundSn)
                            .set(ProductOrderEntity::getRefundAt, LocalDateTime.now());
                    dto.setReason(dto.getReason() + "订单退款（退款单号：" + refundSn + "）");
                }
                //更新为退款中
                wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderEntity.getProductOrderSn())
                        .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.REFUNDING.getCode());
                thirdOrderStatusNotify(productOrderEntity, ProductOrderStatusEnum.REFUNDING.getCode());
            }
            //其他状态下，不可取消
            default -> ToolsHelper.throwException("订单当前状态不可取消");
        }

        productOrderService.updateByWrapper(wrapper);
        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.BEGIN_AFTER_SALE.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "订单取消（修改内容：" + dto + "）",
                dto.getCompanyId()
        );
        return true;
    }

    /*
     * 修改订单
     */
    public Boolean modifyOrder(WecarModifyOrderDTO dto) throws BizException {
        //检查订单
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getSpOrderId(), ProductOrderEntity::getProductOrderSn);
        if(productOrderEntity == null){
            ToolsHelper.throwException("该订单号不存在");
        }
        //待发货中状态可以取消
        if(!productOrderEntity.getLogisticStatus().equals(ProductOrderLogisticStatusEnum.UN_PUSH.getCode())){
            ToolsHelper.throwException("订单不可修改");
        }
        // 地址去空格
        dto.setReceiverAddress(dto.getReceiverAddress().replace(" ", "").trim());
        String sendArea = "";
        try {
            sendArea = getAreaByAddress(dto.getReceiverAddress());
        } catch (Exception e) {
            ToolsHelper.throwException("收货地址解析错误：" + e.getMessage());
        }
        //更新订单数据
        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderEntity.getProductOrderSn())
                .set(ProductOrderEntity::getSendPhone, dto.getMobile())
                .set(ProductOrderEntity::getSendName, dto.getReceiver())
                .set(ProductOrderEntity::getSendArea, sendArea)
                .set(ProductOrderEntity::getSendAddress, dto.getReceiverAddress());
        productOrderService.updateByWrapper(wrapper);
        // 记录日志
        createLog(
                ProductOrderLogTypeEnum.MODIFIED.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "订单修改（修改内容：" + dto.toString() + "）",
                dto.getCompanyId()
        );

        return true;
    }

    /*
     * 取消订单
     */
    public boolean thirdOrderStatusNotify(ProductOrderEntity productOrder,Integer orderStatus){
        //检查订单
        if(productOrder == null){
           return false;
        }
        if(!productOrder.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())){
            return false;
        }
        //符合通知的类型才进行操作，这一期只有geely
        if(!checkIsGeelyType(productOrder.getReferValue())){
            return false;
        }

        //根据类型找companyId
        String companyId = ReferValueEnum.getCompanyIdByReferValue(productOrder.getReferValue());
        String timestamp = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        ProductOrderThirdParamBO thirdParamsBo = getThirdParamsByCompanyId(companyId);
        ProductOrderThirdNotifyBO bo = new ProductOrderThirdNotifyBO();
        bo.setStatus(orderStatus);
        bo.setCompanyId(companyId);
        bo.setOutOrderId(productOrder.getProductOrderSn());
        bo.setActionTime(System.currentTimeMillis() / 1000);
        bo.setUserCode(productOrder.getReferUserCode());
        bo.setTimestamp(timestamp);
        bo.setNonce(ToolsHelper.createRandomStr(30));
        bo.setLogisticStatus(productOrder.getLogisticStatus());
        WecarProductVo wecarProductVo = productPackageBusiness.getWecarProductVoByPackageSn(productOrder.getPackageSn());
        if(wecarProductVo != null){
            bo.setApplyProductId(wecarProductVo.getId());
            bo.setApplyProductName(wecarProductVo.getAliasName());
        }

        bo.setSeqId(ToolsHelper.createRandomStr(30));
        bo.setToken(TokenUtils.getTokenByObject(bo, thirdParamsBo.getSecretKey()));
        callProductOrderThirdNotifyFeign.statusNotify(thirdParamsBo.getNotifyUrl(), bo);
        return true;
    }


    public void thridOrderRefund(ChannelProductRefundDTO dto) {
        // 校验订单
        ProductOrderEntity productOrderEntity = getProductOrderBySn(dto.getProductOrderSn());
        if (!Arrays.asList(ChannelProductOrderStatusEnum.PAID.getCode(),
                ChannelProductOrderStatusEnum.REFUNDING.getCode(),
                ChannelProductOrderStatusEnum.REFUND_FAILED.getCode()
        ).contains(productOrderEntity.getOrderStatus())
        ) {
            ToolsHelper.throwException("订单状态当前不能进行该操作不能进行该操作");
        }
        // 组装mq 消息
        PayGateWayRefundBO payGateWayRefundBO = new PayGateWayRefundBO();
        payGateWayRefundBO.setPaymentNo(productOrderEntity.getPaymentSn());
        payGateWayRefundBO.setOrderNo(productOrderEntity.getProductOrderSn());
        payGateWayRefundBO.setAmount(productOrderEntity.getPaidAmount());
        payGateWayRefundBO.setUserId(productOrderEntity.getUid().intValue());
        payGateWayRefundBO.setOrderType(PayGatewayConst.ORDER_TYPE);
        payGateWayRefundBO.setReason(dto.getReason());
        // 发起mq
        try {
            Message sendMsg = new Message(payGatewayConfig.getRefundTopic(), payGatewayConfig.getChannelProductOrderRefundAutoTag(), JSON.toJSONString(payGateWayRefundBO).getBytes());
            payGatewayConfig.mqProducer().send(sendMsg);
        } catch (MQClientException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getErrorMessage());
        } catch (MQBrokerException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getErrorMessage());
        } catch (RemotingException | InterruptedException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getMessage());
        }
        productOrderService.refundingChannelProductOrder(productOrderEntity.getProductOrderSn());

        // 记录发起消息
        createLog(ProductOrderLogTypeEnum.REFUND.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "发起退款操作：" + dto.getReason(),
                dto.getOperator());
    }

    /*
     * 同步订单状态
     */
    public Boolean notify(ThirdOrderNotifyDTO dto) throws BizException {
        //检查订单
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getSpOrderId(), ProductOrderEntity::getProductOrderSn);
        if(productOrderEntity == null){
            ToolsHelper.throwException("该订单号不存在");
        }
        thirdOrderStatusNotify(productOrderEntity, dto.getOrderStatus());
        return true;
    }


    /*
     * 判断是否类似geely类型的商城订单,通用的渠道订单，ETC助手收钱，退款，同步状态处理
     */
    public boolean checkIsGeelyType(Integer referValue) {
        //符合通知的类型才进行操作，这一期只有geely
        if (Arrays.asList(
                ReferValueEnum.PARTNER_VALUE_GEELY.getValue()
        ).contains(referValue)) {
            return true;
        }
        return false;
    }
    /**
     * 选择银行
     *
     **/
    public void chooseBank(Long uid,ProductOrderChooseBankDTO chooseBankDTO) {
        // 先获取到要修改的订单，订单状态需要是未支付状态
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(chooseBankDTO.getProductOrderSn(),
                ProductOrderEntity::getProductOrderSn);
        if (productOrderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }
        if (!Objects.equals(productOrderEntity.getUid(), uid)) {
            ToolsHelper.throwException("订单不属于当前用户");
        }
        if (!Objects.equals(ProductOrderStatusEnum.DEFAULT.getCode(), productOrderEntity.getOrderStatus())) {
            ToolsHelper.throwException("订单状态不允许修改");
        }

        // 商城单需要是信用卡类型的单才允许切换 referType=1
        if (!Objects.equals(productOrderEntity.getReferType(), ReferTypeConstant.CREDIT_CARD_APPLY_TYPE)) {
            ToolsHelper.throwException("订单类型不属于信用卡不允许修改");
        }
        // 当前银行类型跟所选的一致，直接返回，不继续处理
        if (Objects.equals(productOrderEntity.getReferValue(), chooseBankDTO.getWhichBank())) {
            return;
        }

        // 清除掉信用卡的该类型关联信用卡单信息
        UnbindByParamsDTO unbindByParamsDTO = new UnbindByParamsDTO();
        unbindByParamsDTO.setUid(productOrderEntity.getUid());
        unbindByParamsDTO.setReferSn(productOrderEntity.getProductOrderSn());
        creditCardBusiness.cleanReferSn(unbindByParamsDTO);

        // 把商城单的状态修改为对应的银行卡类型

        productOrderService.setReferValue(productOrderEntity.getProductOrderSn(), chooseBankDTO.getWhichBank());
        // 记录日志
        createLog(ProductOrderLogTypeEnum.CHOOSE_BANK.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "选择银行：" + ActivityCreditCardUserInfoWhichBankEnum.getDescByCode(chooseBankDTO.getWhichBank()),
                "用户");
    }
}
