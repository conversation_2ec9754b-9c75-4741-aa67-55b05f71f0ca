package com.ets.apply.application.infra.entity.map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 默认映射相关
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("map_third_partner")
public class MapThirdPartnerEntity extends BaseEntity<MapThirdPartnerEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 合作方id
     */
    private String companyId;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态（有效1，无效2）
     */
    private Integer status;

    /**
     * 排序，序号越大排前面
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
