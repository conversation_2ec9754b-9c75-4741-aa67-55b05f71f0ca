package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.AfterSalesReviewsNotifyFeign;
import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewsNotifyBO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * 售后审核单回调通知 Feign 降级处理
 */
@Component
public class AfterSalesReviewsNotifyFallbackFactory implements FallbackFactory<AfterSalesReviewsNotifyFeign> {

    @Override
    public AfterSalesReviewsNotifyFeign create(Throwable throwable) {
        return new AfterSalesReviewsNotifyFeign() {
            @Override
            public JsonResult<Object> reviewStatusNotify(URI uri, AfterSalesReviewsNotifyBO bo) {
                return JsonResult.error("售后审核单回调通知失败: " + throwable.getMessage());
            }
        };
    }
}
