package com.ets.apply.application.app.disposer;

import com.ets.apply.application.app.business.BlacklistBusiness;
import com.ets.apply.application.common.bo.blacklist.BlacklistBO;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "BlacklistNotifyJobBean")
public class BlacklistNotifyDisposer extends BaseDisposer {


    @Autowired
    BlacklistBusiness blacklistBusiness;

    public BlacklistNotifyDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "BlacklistNotifyJobBean";
    }

    @Override
    public void execute(Object content) {
        BlacklistBO notifyBO = getParamsObject(content, BlacklistBO.class);
        blacklistBusiness.notify(notifyBO);
    }
}
