package com.ets.apply.application.controller.admin;
import com.alibaba.nacos.common.utils.StringUtils;
import com.ets.apply.application.app.business.StatisticDataBusiness;
import com.ets.apply.application.common.dto.adminStatistic.StatisticDataCompareDTO;
import com.ets.apply.application.common.dto.adminStatistic.StatisticDataDTO;
import com.ets.apply.application.common.vo.adminStatistic.StatisticDataCompareVO;
import com.ets.apply.application.common.vo.adminStatistic.StatisticDataListVO;
import com.ets.apply.application.common.vo.adminStatistic.StatisticDataPanelVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/admin/statistic")
public class StatisticDataController extends BaseController {

    @Autowired
    StatisticDataBusiness statisticDataBusiness;
    @RequestMapping("/getConfig")
    public JsonResult<Object> getConfig() {
        return JsonResult.ok(statisticDataBusiness.getConfig("statistic:data:config"));
    }
    @RequestMapping("/getList")
    public JsonResult<List<StatisticDataListVO>> getList(@RequestBody StatisticDataDTO dto) {
        if(StringUtils.isEmpty(dto.getStartDate())){
            dto.setStartDate((new SimpleDateFormat("yyyy-MM-dd")).format(new Date()));
        }
        if(StringUtils.isEmpty(dto.getEndDate())){
            dto.setEndDate((new SimpleDateFormat("yyyy-MM-dd")).format(new Date()));
        }
        return JsonResult.ok(statisticDataBusiness.getList(dto));
    }

    @RequestMapping("/getCompare")
    public JsonResult<List<StatisticDataCompareVO>> getCompare(@RequestBody StatisticDataCompareDTO dto) {
        return JsonResult.ok(statisticDataBusiness.getCompare(dto));
    }
    @RequestMapping("/getPanel")
    public JsonResult< List<StatisticDataPanelVO>> getPanel() {

        return JsonResult.ok(statisticDataBusiness.getPanelList());
    }
}
