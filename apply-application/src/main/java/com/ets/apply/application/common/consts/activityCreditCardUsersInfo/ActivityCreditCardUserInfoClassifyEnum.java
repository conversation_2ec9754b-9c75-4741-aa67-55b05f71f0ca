package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoClassifyEnum {

    TYPE_DEFAULT(1, "申办-内部券"),
    TYPE_NEW_APPLY(2, "申办-资格"),
    TYPE_NORMAL_CREDIT_APPLY(3, "活动-消费券"),
    TYPE_PRODUCT_ORDER_APPLY(4, "商城订单-内部券"),
    TYPE_INCREASE_APPLY (5, "会员-资格"),
    TYPE_INCREASE_ACTIVITY (6, "增值-活动");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardUserInfoClassifyEnum node : ActivityCreditCardUserInfoClassifyEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardUserInfoClassifyEnum node : ActivityCreditCardUserInfoClassifyEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));
            row.put("sub", String.valueOf(node.getCode()));
            list.add(row);
        }

        return list;
    }
}
