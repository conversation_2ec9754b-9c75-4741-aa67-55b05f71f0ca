package com.ets.apply.application.common.vo.channelProductOrder;

import com.ets.apply.application.common.consts.productOrder.ChannelProductOrderStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.DecimalMax;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

@Data
public class ChannelProductOrderListVO {

    private Integer id;

    /**
     * 商品订单号
     */
    private String productOrderSn;

    /**
     * 产品包sn
     */
    private String packageSn;


    /**
     * 订单状态 10:初始状态 20:待发货 30:已发货 40:已签收  50:售后中 60：售后完成 90
     */
    private Integer orderStatus;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidTime;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 交易单号
     */
    private String transactionId;

    /**
     * 申办单号
     */
    private String applyOrderSn;

    /**
     * 异常信息
     */

    /**
     * 产品包名称
     */
    public String packageStr;
//    public String getPackageStr() {
//        return ProductPackageEnum.getDescByCode(packageSn);
//    }

    public String getOrderStatusStr() {
        return ChannelProductOrderStatusEnum.getDescByCode(orderStatus);
    }

    /**
     * 是否允许退款
     *
     * @return
     */
    public Boolean isAllowRefund() {
        return Arrays.asList(
                ChannelProductOrderStatusEnum.PAID.getCode(), ChannelProductOrderStatusEnum.REFUND_FAILED.getCode(),
                ChannelProductOrderStatusEnum.REFUNDING.getCode()
        ).contains(orderStatus);

    }

    public Integer getTotalAmountInt(){
        return totalAmount.intValue();
    }

    public Integer getPaidAmountInt(){
        return paidAmount.intValue();
    }
}
