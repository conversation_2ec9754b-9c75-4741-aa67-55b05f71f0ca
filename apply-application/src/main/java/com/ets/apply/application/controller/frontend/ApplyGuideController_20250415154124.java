package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ApplyGuideBusiness;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionGroupVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultWithItemVO;
import com.ets.apply.application.common.dto.QuestionGroupRequestDTO;
import com.ets.common.JsonResult;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/frontend/apply-guide")
public class ApplyGuideController {

    @Autowired
    private ApplyGuideBusiness applyGuideBusiness;

    @PostMapping("/get-question-map")
    public JsonResult<List<ApplyGuideMapVO>> getQuestionMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getQuestionMap(mapType));
    }

    @PostMapping("/get-result-map")
    public JsonResult<List<ApplyGuideResultVO>> getResultMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getResultMap(mapType));
    }

    @PostMapping("/get-question-group-list")
    public JsonResult<List<ApplyGuideQuestionGroupVO>> getQuestionGroupList(@Valid @RequestBody QuestionGroupRequestDTO request) {
        List<ApplyGuideQuestionGroupVO> result = applyGuideBusiness.getQuestionGroupList(request.getMapType(), request.getQuestionTypeList());
        return JsonResult.ok(result);
    }

    @PostMapping("/get-result-with-item-map")
    public JsonResult<List<ApplyGuideResultWithItemVO>> getResultWithItemMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getResultWithItemMap(mapType));
    }
}

