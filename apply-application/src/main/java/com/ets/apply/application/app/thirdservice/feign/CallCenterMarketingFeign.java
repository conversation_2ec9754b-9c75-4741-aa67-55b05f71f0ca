package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallCenterMarketingFallBackFactory;
import com.ets.apply.application.app.thirdservice.request.SendTermCouponDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCoopDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardStreamDTO;
import com.ets.apply.application.common.dto.request.notice.SelectNoticeInfoDTO;
import com.ets.apply.application.common.dto.request.machine.MachineCommandDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 调用center-marketing接口
 */
@FeignClient(
        url = "${microUrls.marketing:http://etc-center-marketingboot:8501}",
        name = "etc-center-marketing", fallbackFactory = CallCenterMarketingFallBackFactory.class)
public interface CallCenterMarketingFeign {

    /**
     * 中信银行
     * 领取券资格
     */
    @PostMapping("/credit/stream/receive")
    String receive(@RequestBody CreditCardStreamDTO param);

    @PostMapping("/wx/notice/select/notice/info")
    String selectNoticeInfo(@RequestBody SelectNoticeInfoDTO infoDTO);

    @PostMapping("/marketing/self-help-machine/command")
    String command(@RequestBody MachineCommandDTO commandDTO);

    /**
     * 领取券资格
     * https://www.tapd.cn/31207138/prong/stories/view/1131207138001089114
     */
    @PostMapping("/creditcard/coop/receive")
    String coopReceive(@RequestBody CreditCardCoopDTO param);

    /**
     * 校验申办单是否参与活动
     * https://yapi.etczs.net/project/51/interface/api/33684
     */
    @PostMapping("/etcAct/check-join")
    String etcActCheckJoin(@RequestBody List<String> param);

    @PostMapping("/term/purchase/sendTermCoupon")
    String sendTermCoupon(@RequestBody SendTermCouponDTO couponDTO);

}
