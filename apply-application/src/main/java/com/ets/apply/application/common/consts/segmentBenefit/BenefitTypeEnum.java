package com.ets.apply.application.common.consts.segmentBenefit;

import com.ets.apply.application.app.factory.benefit.impl.BenefitBase;
import com.ets.apply.application.app.factory.benefit.impl.TermCouponBenefit;
import com.ets.apply.application.app.factory.benefit.impl.TruckTermCouponBenefit;
import com.ets.apply.application.app.factory.benefit.impl.WarrantyBenefit;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BenefitTypeEnum {

    TERM_COUPON("term_coupon", TermCouponBenefit.class, "券包"),
    TRUCK_TERM_COUPON("truck_term_coupon", TruckTermCouponBenefit.class, "货车券包"),
    WARRANTY("warranty", WarrantyBenefit.class, "质保"),
    ;

    private final String type;
    private final Class<? extends BenefitBase> benefit;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(BenefitTypeEnum.values()).collect(Collectors.toMap(BenefitTypeEnum::getType, BenefitTypeEnum::getDesc));
    }

    public static BenefitTypeEnum getBenefitTypeEnumByValue(String value) {
        return Arrays.stream(BenefitTypeEnum.values()).filter(e -> e.getType().equals(value)).findFirst().orElse(null);
    }
}
