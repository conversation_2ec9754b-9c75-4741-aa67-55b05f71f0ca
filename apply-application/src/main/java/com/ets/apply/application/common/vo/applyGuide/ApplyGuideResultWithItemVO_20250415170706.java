package com.ets.apply.application.common.vo.applyGuide;

import lombok.Data;
import java.util.List;

@Data
public class ApplyGuideResultWithItemVO {
    /**
     * 结果ID
     */
    private Integer id;

    /**
     * 问题组合
     */
    private String questionMapGroup;

    /**
     * 产品编号
     */
    private String packageSn;

    /**
     * 产品标题
     */
    private String productTitle;

    /**
     * 产品图片URL
     */
    private String productImgUrl;

    private BigDecimal packageFee;
    //原始价格
    private BigDecimal originalPrice;

    /**
     * 问题项列表
     */
    private List<ApplyGuideMapVO> questionItems;
} 