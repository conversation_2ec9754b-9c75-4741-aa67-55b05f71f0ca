package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSONArray;
import com.ets.apply.application.app.thirdservice.feign.CouponFeign;
import com.ets.apply.application.common.bo.coupon.SendCouponBO;
import com.ets.apply.application.common.bo.missionReward.MissionRewardContentBO;
import com.ets.apply.application.common.bo.missionReward.MissionRewardResultBO;
import com.ets.apply.application.infra.entity.MissionConfigEntity;
import com.ets.apply.application.infra.service.MissionConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class MissionRewardBusiness {

    @Autowired
    private CouponFeign couponFeign;

    @Autowired
    private MissionConfigService missionConfigService;

    public Boolean isMissionConfigValid(MissionConfigEntity configEntity) {

        if (configEntity.getStatus() != 1) {
            return false;
        }

        if (configEntity.getStartDate() != null && configEntity.getStartDate().isAfter(LocalDateTime.now())) {
            return false;
        }

        if (configEntity.getEndDate() != null && configEntity.getEndDate().isBefore(LocalDateTime.now())) {
            return false;
        }

        return true;
    }

    public MissionRewardResultBO reward(String missionConfigSn, Long uid, String businessOrderSn) {

        MissionRewardResultBO resultBO = new MissionRewardResultBO();

        MissionConfigEntity configEntity = missionConfigService.getByConfigSn(missionConfigSn);

        if (configEntity == null) {
            resultBO.setError("没有找到奖励配置");
            return resultBO;
        }

        if (! isMissionConfigValid(configEntity)) {
            resultBO.setError("奖励配置无效");
            return resultBO;
        }

        List<MissionRewardContentBO.rewardCouponInfo> couponInfos = configEntity.getRewardContentBO().getCouponInfoList();
        if (couponInfos != null && ! couponInfos.isEmpty()) {

            resultBO.setCouponInfo(new HashMap<>());

            couponInfos.forEach(rewardCouponInfo -> {

                SendCouponBO sendCouponBO = new SendCouponBO();
                sendCouponBO.setActivityId(133);
                sendCouponBO.setUid(uid);
                sendCouponBO.setCouponBatchNo(rewardCouponInfo.getBatchNo());
                sendCouponBO.setCount(rewardCouponInfo.getCount());
                sendCouponBO.setMsgId(businessOrderSn);
                sendCouponBO.setBusinessOrderNo(businessOrderSn);
                sendCouponBO.setChannel("奖励发放");

                String couponNos = sendCoupon(sendCouponBO);

                resultBO.getCouponInfo().put(rewardCouponInfo.getBatchNo(), couponNos);
            });

        }

        return resultBO;
    }

    /**
     * 申请发券
     * @return
     */
    protected String sendCoupon(SendCouponBO sendCouponBO) {

        JSONArray result = couponFeign.sendCoupon(sendCouponBO).getDataWithCheckError();

        List<String> couponNos = new ArrayList<>();
        for (Object row : result) {
            if (row instanceof HashMap) {
                String couponNo = String.valueOf(((HashMap) row).get("couponNo"));
                couponNos.add(couponNo);
            }
        }

        return StringUtils.join(couponNos, ",");
    }

}
