package com.ets.apply.application.infra.entity.map;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
/**
 * <p>
 * 映射发布生产数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("map_third_partner_cache")
public class MapThirdPartnerCacheEntity extends BaseEntity<MapThirdPartnerCacheEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型：preview预览,prod发布
     */
    private String type;

    /**
     * 缓存主key
     */
    private String cacheMainKey;

    /**
     * 缓存key
     */
    private String cacheKey;

    /**
     * 缓存值，json
     */
    private String cacheValues;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
