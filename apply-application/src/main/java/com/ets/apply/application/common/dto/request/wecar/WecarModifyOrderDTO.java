package com.ets.apply.application.common.dto.request.wecar;

import com.ets.common.annotation.PhoneAnnotation;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class WecarModifyOrderDTO {
    @NotNull(message = "companyId不能为空")
    private String companyId;
    @NotNull(message = "服务商订单号不能为空")
    private String spOrderId;
    @NotNull(message = "收货地址不能为空")
    private String receiverAddress;
    @NotNull(message = "收货人不能为空")
    private String receiver;
    @NotBlank(message = "手机号码不能为空")
    @PhoneAnnotation(message = "手机号格式错误")
    private String mobile;
}
