package com.ets.apply.application.common.dto.request.productOrder;

import com.ets.common.annotation.PhoneAnnotation;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ThirdAddOrderDTO {
    @NotNull(message = "companyId不能为空")
    private String companyId;
    private String applyOrderNo;//分销方订单编号，创建成功后不可重复使用
    private String vehicleNo;
    @NotNull(message = "收货地址不能为空")
    private String receiverAddress;
    @NotNull(message = "收货人不能为空")
    private String receiver;
    private Integer plateColor = 0;

    @NotBlank(message = "手机号码不能为空")
    @PhoneAnnotation(message = "手机号格式错误")
    private String mobile;
    @NotNull(message = "⽤户标识不能为空")
    private String userCode;
    @NotNull(message = "办理产品id不能为空")
    private String applyProductId;
}
