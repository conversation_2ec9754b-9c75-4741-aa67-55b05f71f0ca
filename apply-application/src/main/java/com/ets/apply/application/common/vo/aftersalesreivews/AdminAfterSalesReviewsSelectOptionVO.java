package com.ets.apply.application.common.vo.aftersalesreivews;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AdminAfterSalesReviewsSelectOptionVO {
    // 业务类型
    private List<Map<String, String>> orderTypeList;
    // 发卡方
    private List<Map<Integer, String>> issuerIdList;
    // 审核状态
    private List<Map<Integer, String>> reviewStatusList;
    // 驳回原因（二级联动）
}
