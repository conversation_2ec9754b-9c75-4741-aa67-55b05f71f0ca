{"sysTime":"2025-05-28 10:43:28.690","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-05-28 10:43:28.682","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:28.724","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:28.858","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:28.859","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-05-28 10:43:28.860","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-05-28 10:43:28.860","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-05-28 10:43:28.876","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-05-28 10:43:28.879","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-05-28 10:43:28.926","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:28.934","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-05-28 10:43:28.947","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-05-28 10:43:29.229","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-05-28 10:43:29.581","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[apply-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:29.775","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[apply-application-dev.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:29.776","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-apply-application-dev.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-credit-card.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-template-msg-ids.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-big-data-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-05-28 10:43:29.786","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer:78","methodName":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer:logDuplicateJsonObjectsWarning-78","message":"{\"msg\":\"\\n\\nFound multiple occurrences of org.json.JSONObject on the class path:\\n\\n\\tjar:file:/Users/<USER>/.m2/repository/org/json/json/20240303/json-20240303.jar!/org/json/JSONObject.class\\n\\tjar:file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\\n\\nYou may wish to exclude one of them to ensure predictable runtime behavior\\n\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:29.790","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:29.791","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:660","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:logStartupProfileInfo-660","message":"The following 1 profile is active: \"dev\"","thrown":""}
{"sysTime":"2025-05-28 10:43:30.580","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-05-28 10:43:30.581","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-05-28 10:43:30.606","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-05-28 10:43:30.815","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=a9ae6eb7-41b2-398d-a4a4-8941d0869694","thrown":""}
{"sysTime":"2025-05-28 10:43:31.009","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.012","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.013","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$1195/0x00000070017c8aa0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.015","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.041","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.049","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.059","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.120","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.123","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:31.481","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-apply defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:31.532","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:33.505","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-etc} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:35.018","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-etc-proxy} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:36.820","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-apply} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:38.525","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-apply-proxy} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:40.312","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-5,db-order} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:41.774","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-6,db-order-proxy} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:43.538","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-7,db-service} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:45.077","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-8,db-service-proxy} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:46.929","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-9,db-minor} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:48.762","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-10,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:50.341","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-11,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-05-28 10:43:50.341","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.341","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-etc-proxy] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-apply] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-order-proxy] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-minor] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-apply-proxy] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.342","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service-proxy] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.343","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-etc] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.343","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-order] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.343","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-05-28 10:43:50.343","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [11] datasource,primary datasource named [db-apply]","thrown":""}
{"sysTime":"2025-05-28 10:43:50.432","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.metadata.TableInfoHelper:376","methodName":"c.b.m.core.metadata.TableInfoHelper:initTableFields-376","message":"{\"msg\":\"Can not find table primary key in Class: \\\"com.ets.apply.application.infra.entity.ApplyConfigEntity\\\".\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:50.434","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.injector.DefaultSqlInjector:56","methodName":"c.b.m.core.injector.DefaultSqlInjector:getMethodList-56","message":"{\"msg\":\"class com.ets.apply.application.infra.entity.ApplyConfigEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.392","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.metadata.TableInfoHelper:376","methodName":"c.b.m.core.metadata.TableInfoHelper:initTableFields-376","message":"{\"msg\":\"Can not find table primary key in Class: \\\"com.ets.apply.application.infra.entity.ProductPackageTmpEntity\\\".\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.392","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.injector.DefaultSqlInjector:56","methodName":"c.b.m.core.injector.DefaultSqlInjector:getMethodList-56","message":"{\"msg\":\"class com.ets.apply.application.infra.entity.ProductPackageTmpEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.545","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.metadata.TableInfoHelper:376","methodName":"c.b.m.core.metadata.TableInfoHelper:initTableFields-376","message":"{\"msg\":\"Can not find table primary key in Class: \\\"com.ets.apply.application.infra.entity.ProductPackageStockEntity\\\".\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.546","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.injector.DefaultSqlInjector:56","methodName":"c.b.m.core.injector.DefaultSqlInjector:getMethodList-56","message":"{\"msg\":\"class com.ets.apply.application.infra.entity.ProductPackageStockEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.886","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.metadata.TableInfoHelper:376","methodName":"c.b.m.core.metadata.TableInfoHelper:initTableFields-376","message":"{\"msg\":\"Can not find table primary key in Class: \\\"com.ets.apply.application.infra.entity.ProductOrderThirdTaskEntity\\\".\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:51.887","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.injector.DefaultSqlInjector:56","methodName":"c.b.m.core.injector.DefaultSqlInjector:getMethodList-56","message":"{\"msg\":\"class com.ets.apply.application.infra.entity.ProductOrderThirdTaskEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:52.451","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.metadata.TableInfoHelper:376","methodName":"c.b.m.core.metadata.TableInfoHelper:initTableFields-376","message":"{\"msg\":\"Can not find table primary key in Class: \\\"com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity\\\".\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:52.451","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.b.m.core.injector.DefaultSqlInjector:56","methodName":"c.b.m.core.injector.DefaultSqlInjector:getMethodList-56","message":"{\"msg\":\"class com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:53.365","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-apply defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:53.368","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:55.496","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-05-28 10:43:55.498","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-05-28 10:43:55.498","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-05-28 10:43:55.515","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-05-28 10:43:55.614","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@apply-application -> [{\"instanceId\":\"************#20070#DEFAULT#apply@@apply-application\",\"ip\":\"************\",\"port\":20070,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@apply-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-05-28 10:43:55.618","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@apply-application -> [{\"instanceId\":\"************#20070#DEFAULT#apply@@apply-application\",\"ip\":\"************\",\"port\":20070,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@apply-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-05-28 10:43:55.691","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:55.912","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-apply-product-pay, nameServerAddr=name-service:9876, topic=golden_pay_topic, tag=golden_tag_order_pay_callback_42","thrown":""}
{"sysTime":"2025-05-28 10:43:55.921","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.102","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-apply-product-refund, nameServerAddr=name-service:9876, topic=golden_pay_topic, tag=golden_tag_order_refund_callback_42","thrown":""}
{"sysTime":"2025-05-28 10:43:56.104","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-apply-product defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.106","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.110","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_baidu_tpl_msg_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.112","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.116","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_task_apply_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.118","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.120","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.301","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_task_apply_group, nameServerAddr=name-service:9876, topic=ets_task_apply, tag=queueTaskApply","thrown":""}
{"sysTime":"2025-05-28 10:43:56.442","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-apply defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.444","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.447","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.520","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@apply-application, groupName：apply, 实例：[Instance{instanceId='************#20070#DEFAULT#apply@@apply-application', ip='************', port=20070, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@apply-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-05-28 10:43:56.649","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-apply, nameServerAddr=name-service:9876, topic=ETS_APPLY_MAIN, tag=queueDefault","thrown":""}
{"sysTime":"2025-05-28 10:43:56.651","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.807","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-apply, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-05-28 10:43:56.812","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-05-28 10:43:56.982","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-php-event-apply, nameServerAddr=name-service:9876, topic=rabbitMqEvent, tag=rabbitMqEventIssuer","thrown":""}
{"sysTime":"2025-05-28 10:43:57.548","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:57.558","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 20 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-05-28 10:43:57.753","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:56","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:logStarted-56","message":"Started AfterSalesReviewsBusinessTest in 29.373 seconds (process running for 30.003)","thrown":""}
{"sysTime":"2025-05-28 10:43:57.757","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] apply-credit-card.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.758","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=apply-credit-card.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.758","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=apply-credit-card.yaml, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.758","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] template-msg-ids.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.758","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=template-msg-ids.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.758","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=template-msg-ids.yaml, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] apply-application-dev.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=apply-application-dev.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=apply-application-dev.yaml, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] apply-application+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=apply-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=apply-application, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] apply-application.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=apply-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.759","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=apply-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.760","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] big-data-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-05-28 10:43:57.760","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=big-data-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-05-28 10:43:57.760","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=big-data-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-05-28 10:43:57.767","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:creditCardRewardCoupon, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1939ecca[class com.ets.apply.application.app.job.CreditCardRewardJob$$SpringCGLIB$$0#creditCardRewardCouponHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.767","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryImportFailRecord, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4df207e1[class com.ets.apply.application.app.job.ImportRecordJob$$SpringCGLIB$$0#retryImportFailRecord]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.767","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryImportFailRecordByBatchNo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a2a94ae[class com.ets.apply.application.app.job.ImportRecordJob$$SpringCGLIB$$0#retryImportFailRecordByBatchNo]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:historyUserIpLUploadHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36840e6c[class com.ets.apply.application.app.job.IpUploadJob$$SpringCGLIB$$0#historyUserIpLUploadHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:syncToOrderCenter, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@600dfbca[class com.ets.apply.application.app.job.OrderCenterJob$$SpringCGLIB$$0#syncToOrderCenter]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:orderCenterSyncHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d1d3525[class com.ets.apply.application.app.job.OrderCenterSyncJob$$SpringCGLIB$$0#orderCenterSyncHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:orderCenterSyncOnceHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@735c49cc[class com.ets.apply.application.app.job.OrderCenterSyncJob$$SpringCGLIB$$0#orderCenterSyncOnceHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:orderCenterSyncByOrderSnHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@653ddea5[class com.ets.apply.application.app.job.OrderCenterSyncJob$$SpringCGLIB$$0#orderCenterSyncByOrderSnHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:orderCenterSyncCreatedHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ae1ba63[class com.ets.apply.application.app.job.OrderCenterSyncJob$$SpringCGLIB$$0#orderCenterSyncCreatedHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:rechargeBalanceNoticeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e010e07[class com.ets.apply.application.app.job.RechargeNoticeJob$$SpringCGLIB$$0#rechargeBalanceNoticeHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.768","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:StatisticDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f822222[class com.ets.apply.application.app.job.StatisticJob$$SpringCGLIB$$0#StatisticDataHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@24411981[class com.ets.apply.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecByReferTypeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@43948dc8[class com.ets.apply.application.app.job.TaskJob$$SpringCGLIB$$0#reExecByReferTypeHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:autoRefreshNumbers, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7553d28b[class com.ets.apply.application.app.job.UnicomApplyJob$$SpringCGLIB$$0#autoRefreshNumbers]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:syncUnicomOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4bfe7d31[class com.ets.apply.application.app.job.UnicomApplyJob$$SpringCGLIB$$0#syncUnicomOrderHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:syncOrderState, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@675fe8de[class com.ets.apply.application.app.job.UnicomApplyJob$$SpringCGLIB$$0#syncOrderState]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:wecarNotifyActivatedExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@c2d330[class com.ets.apply.application.app.job.WecarNotifyActivatedJob$$SpringCGLIB$$0#wecarNotifyActivatedExecHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:citicActivateCheck, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6bee9e29[class com.ets.apply.application.app.job.creditCard.CiticActivateCheckJob$$SpringCGLIB$$0#citicActivateCheckHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:activateFieldsCompensation, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@53416190[class com.ets.apply.application.app.job.creditCard.CreditCardJob$$SpringCGLIB$$0#activateFieldsCompensation]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:auditFieldsCompensation, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4bc06a3e[class com.ets.apply.application.app.job.creditCard.CreditCardJob$$SpringCGLIB$$0#auditFieldsCompensation]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:submitFieldsCompensation, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5d8d7b25[class com.ets.apply.application.app.job.creditCard.CreditCardJob$$SpringCGLIB$$0#submitFieldsCompensation]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyOrderStatisticHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@420da96b[class com.ets.apply.application.app.job.monitor.ApplyOrderMonitorJob$$SpringCGLIB$$0#applyOrderStatistic]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyOrderAverageStatisticHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a180d5b[class com.ets.apply.application.app.job.monitor.ApplyOrderMonitorJob$$SpringCGLIB$$0#applyOrderAverageStatistic]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.769","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyOrderBizTypeAverageStatisticHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@524105bc[class com.ets.apply.application.app.job.monitor.ApplyOrderMonitorJob$$SpringCGLIB$$0#applyOrderBizTypeAverageStatistic]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.770","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:thirdTaskNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64a31882[class com.ets.apply.application.app.job.productOrder.ProductOrderThirdTaskJob$$SpringCGLIB$$0#thirdTaskNotifyHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.770","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:thirdTaskNotifyGapDaysHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4bcaa38c[class com.ets.apply.application.app.job.productOrder.ProductOrderThirdTaskJob$$SpringCGLIB$$0#thirdTaskNotifyGapDaysHandler]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.770","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:truckTermCouponSendHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@11620d7b[class com.ets.apply.application.app.job.productOrder.ProductOrderTruckJob$$SpringCGLIB$$0#truckTermCouponSend]","thrown":""}
{"sysTime":"2025-05-28 10:43:57.912","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-05-28 10:43:57.998","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-05-28 10:43:58.008","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20072","thrown":""}
{"sysTime":"2025-05-28 10:43:58.340","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:109","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:testReviewNonExistentReview-109","message":"审核不存在审核单测试成功（正确抛出异常）","thrown":""}
{"sysTime":"2025-05-28 10:43:58.945","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:127","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:createAfterSalesReview-127","message":"创建售后审核单成功，审核单号：2505281043000000081","thrown":""}
{"sysTime":"2025-05-28 10:43:58.951","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"afterSalesReviewAutoAuditJobBean\",\"className\":\"com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"reviewSn\":\"2505281043000000081\"},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"3b9cd00d-6600-489f-8965-ec99dc82c9ae\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:43:59.199","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:318","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:review-318","message":"售后审核单审核完成，审核单号：2505281043000000081，审核结果：审核通过","thrown":""}
{"sysTime":"2025-05-28 10:43:59.570","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=AfterSalesReviewsNotifyFeign, name=AfterSalesReviewsNotifyFeign, url=http://uri)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"uri\\\",\\\"bo\\\"]\",\"args\":\"[\\\"http://etc-saleafter-main:6610/reactivate/reviewResult\\\",{\\\"operator\\\":\\\"loginCode: null\\\",\\\"orderSn\\\":\\\"TEST_ORDER_1748400238350\\\",\\\"reviewStatus\\\":1}]\",\"result\":\"{\\\"code\\\":-1,\\\"msg\\\":\\\"订单号TEST_ORDER_1748400238350不存在!\\\"}\",\"usedTime\":\"362ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:59.570","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:348","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:sendNotifyCallback-348","message":"{\"userId\":\"\",\"url\":\"\",\"msg\":\"售后审核单回调通知失败，审核单号: 2505281043000000081, 回调地址: http://etc-saleafter-main:6610/reactivate/reviewResult, 错误信息: 订单号TEST_ORDER_1748400238350不存在!\"}","thrown":""}
{"sysTime":"2025-05-28 10:43:59.570","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:41","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:testReviewApproved-41","message":"审核通过测试成功，审核单号: 2505281043000000081","thrown":""}
{"sysTime":"2025-05-28 10:44:00.121","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:127","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:createAfterSalesReview-127","message":"创建售后审核单成功，审核单号：2505281043000000091","thrown":""}
{"sysTime":"2025-05-28 10:44:00.122","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"afterSalesReviewAutoAuditJobBean\",\"className\":\"com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"reviewSn\":\"2505281043000000091\"},\"retry\":0,\"spanId\":\"0.3\",\"traceId\":\"90c28904-bd20-442b-8611-7600b9605b8a\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:44:00.198","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"90c28904-bd20-442b-8611-7600b9605b8a","spanId":"0.3","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"afterSalesReviewAutoAuditJobBean\",\"className\":\"com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"reviewSn\":\"2505281043000000091\"},\"retry\":0,\"spanId\":\"0.3\",\"traceId\":\"90c28904-bd20-442b-8611-7600b9605b8a\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:44:00.208","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:90","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:testReviewRejectedWithoutReason-90","message":"审核驳回无原因测试成功（正确抛出异常）","thrown":""}
{"sysTime":"2025-05-28 10:44:00.754","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:127","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:createAfterSalesReview-127","message":"创建售后审核单成功，审核单号：2505281044000000101","thrown":""}
{"sysTime":"2025-05-28 10:44:00.761","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"afterSalesReviewAutoAuditJobBean\",\"className\":\"com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"reviewSn\":\"2505281044000000101\"},\"retry\":0,\"spanId\":\"0.4\",\"traceId\":\"7b589fc7-3680-4fa3-b181-80c7ef48a64c\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:44:00.831","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"7b589fc7-3680-4fa3-b181-80c7ef48a64c","spanId":"0.4","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"afterSalesReviewAutoAuditJobBean\",\"className\":\"com.ets.apply.application.app.disposer.AfterSalesReviewAutoAuditDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"reviewSn\":\"2505281044000000101\"},\"retry\":0,\"spanId\":\"0.4\",\"traceId\":\"7b589fc7-3680-4fa3-b181-80c7ef48a64c\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:44:00.981","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:318","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:review-318","message":"售后审核单审核完成，审核单号：2505281044000000101，审核结果：审核驳回","thrown":""}
{"sysTime":"2025-05-28 10:44:01.069","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=AfterSalesReviewsNotifyFeign, name=AfterSalesReviewsNotifyFeign, url=http://uri)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"uri\\\",\\\"bo\\\"]\",\"args\":\"[\\\"http://etc-saleafter-main:6610/reactivate/reviewResult\\\",{\\\"operator\\\":\\\"loginCode: null\\\",\\\"orderSn\\\":\\\"TEST_ORDER_1748400240216\\\",\\\"rejectReason\\\":\\\"提供的行驶证照片不清晰，请重新上传\\\",\\\"rejectType\\\":\\\"材料不全\\\",\\\"reviewStatus\\\":2}]\",\"result\":\"{\\\"code\\\":-1,\\\"msg\\\":\\\"订单号TEST_ORDER_1748400240216不存在!\\\"}\",\"usedTime\":\"87ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.069","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:348","methodName":"c.e.a.a.a.b.a.AfterSalesReviewsBusiness:sendNotifyCallback-348","message":"{\"userId\":\"\",\"url\":\"\",\"msg\":\"售后审核单回调通知失败，审核单号: 2505281044000000101, 回调地址: http://etc-saleafter-main:6610/reactivate/reviewResult, 错误信息: 订单号TEST_ORDER_1748400240216不存在!\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.069","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"0","parentId":"","exportable":"","pid":"15654","thread":"main","className":"c.e.a.a.b.AfterSalesReviewsBusinessTest:65","methodName":"c.e.a.a.b.AfterSalesReviewsBusinessTest:testReviewRejected-65","message":"审核驳回测试成功，审核单号: 2505281044000000101","thrown":""}
{"sysTime":"2025-05-28 10:44:01.074","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.074","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.075","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.075","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:01.081","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:44:01.081","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:44:01.081","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:44:04.096","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:44:05.138","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"35c1fb25-b3dc-44e6-8f5a-9134bf5650fb","spanId":"0.1","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"UserIpAddressUploadJobBean\",\"className\":\"com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"uid\":1991927978,\"ip\":\"**************\",\"actionId\":112},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"35c1fb25-b3dc-44e6-8f5a-9134bf5650fb\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-05-28 10:44:05.220","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"35c1fb25-b3dc-44e6-8f5a-9134bf5650fb","spanId":"0.1","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataApiFeign, name=BigDataApiFeign, url=http://**********:8899)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/user/whetherOurCompany\\\\\\\"}, produces={}, value={\\\\\\\"/user/whetherOurCompany\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"uid\\\":1991927978}]\",\"result\":\"{\\\"code\\\":200,\\\"data\\\":{\\\"our_company_users\\\":0,\\\"uid\\\":1991927978},\\\"msg\\\":\\\"成功！\\\"}\",\"usedTime\":\"77ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:05.347","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"35c1fb25-b3dc-44e6-8f5a-9134bf5650fb","spanId":"0.1","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BaseApplicationFeign, name=BaseApplicationFeign, url=http://base-application:20120)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.GetMapping(consumes={\\\\\\\"application/json\\\\\\\"}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"}, produces={}, value={\\\\\\\"/ip/getIpAddressInfo\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={GET}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"ip\\\"]\",\"args\":\"[\\\"**************\\\"]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"city\\\":\\\"广州市\\\",\\\"cityCode\\\":\\\"440100\\\",\\\"country\\\":\\\"中国\\\",\\\"createdAt\\\":\\\"2025-02-11 16:13:23\\\",\\\"districtCode\\\":\\\"440105\\\",\\\"districts\\\":\\\"海珠区\\\",\\\"id\\\":1989,\\\"ip\\\":\\\"**************\\\",\\\"originData\\\":\\\"{\\\\\\\"ip\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ad_info\\\\\\\":{\\\\\\\"province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"nation\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"adcode\\\\\\\":440105,\\\\\\\"district\\\\\\\":\\\\\\\"海珠区\\\\\\\",\\\\\\\"nation_code\\\\\\\":156},\\\\\\\"location\\\\\\\":{\\\\\\\"lng\\\\\\\":113.3172,\\\\\\\"lat\\\\\\\":23.08331}}\\\",\\\"province\\\":\\\"广东省\\\",\\\"provinceCode\\\":\\\"440000\\\",\\\"updatedAt\\\":\\\"2025-02-11 16:13:23\\\"},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"127ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:05.384","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"35c1fb25-b3dc-44e6-8f5a-9134bf5650fb","spanId":"0.1","parentId":"0","exportable":"","pid":"15654","thread":"ConsumeMessageThread_ets-group-apply_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=BigDataLogFeign, name=BigDataLogFeign, url=http://**********:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/etc-log\\\\\\\"}, produces={}, value={\\\\\\\"/etc-log\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"json\\\"]\",\"args\":\"[\\\"{\\\\\\\"data\\\\\\\":[{\\\\\\\"action_id\\\\\\\":112,\\\\\\\"ip_addr\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"ip_to_city\\\\\\\":\\\\\\\"广州市\\\\\\\",\\\\\\\"ip_to_country\\\\\\\":\\\\\\\"中国\\\\\\\",\\\\\\\"ip_to_province\\\\\\\":\\\\\\\"广东省\\\\\\\",\\\\\\\"pages_timestamp\\\\\\\":1748400245348,\\\\\\\"path_query\\\\\\\":\\\\\\\"%7B%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22districtsCode%22%3A%22440105%22%2C%22province%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22city%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22provinceCode%22%3A%22440000%22%2C%22cityCode%22%3A%22440100%22%2C%22ip%22%3A%22**************%22%2C%22districts%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%7D\\\\\\\",\\\\\\\"user_id\\\\\\\":1991927978}]}\\\"]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\": -1, \\\\\\\"msg\\\\\\\": \\\\\\\"请求大数据埋点服务失败: Connection refused\\\\\\\", \\\\\\\"data\\\\\\\": null}\\\"\",\"usedTime\":\"34ms\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:07.105","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:44:07.108","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:44:07.108","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:44:07.108","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:44:07.108","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-05-28 10:44:07.108","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:07.109","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-05-28 10:44:07.109","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-05-28 10:44:07.109","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-05-28 10:44:07.109","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-05-28 10:44:07.884","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-05-28 10:44:07.965","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-apply-executor-dev', registryValue='**********:20072'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-05-28 10:44:07.965","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:44:07.966","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-05-28 10:44:07.967","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:44:07.967","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"Thread-34","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-05-28 10:44:08.064","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-05-28 10:44:08.067","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-11} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.070","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-11} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.070","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.071","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.071","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.071","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.071","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-6} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.071","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-6} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.072","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-10} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.072","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-10} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.072","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-9} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.072","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-9} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.072","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-8} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-8} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.073","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-5} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.074","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-5} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.074","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-7} closing ...","thrown":""}
{"sysTime":"2025-05-28 10:44:08.074","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-7} closed","thrown":""}
{"sysTime":"2025-05-28 10:44:08.074","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"15654","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
