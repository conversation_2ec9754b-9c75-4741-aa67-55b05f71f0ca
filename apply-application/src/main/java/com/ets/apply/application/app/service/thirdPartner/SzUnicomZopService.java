package com.ets.apply.application.app.service.thirdPartner;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallSzUnicomZopFeign;
import com.ets.apply.application.common.config.SzUnicomZopConfig;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.dto.request.szUnicomZop.*;
import com.ets.apply.application.common.vo.szUnicomZop.*;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
@Component(value = "SzUnicomZopService")
public class SzUnicomZopService {
    @Autowired
    private SzUnicomZopConfig szUnicomZopConfig;

    @Autowired
    private CallSzUnicomZopFeign callSzUnicomZopFeign;
    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;



    private static String getSHA256String(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest messageDigest;
        messageDigest = MessageDigest.getInstance("SHA-256");
        messageDigest.update(str.getBytes("UTF-8"));
        String encodeStr = byteArrayToHexString(messageDigest.digest());
        return encodeStr;
    }
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }
    public String getSign(Map<String,String> macMap) throws NoSuchAlgorithmException, UnsupportedEncodingException{
        macMap.put("secret",szUnicomZopConfig.getSecret());
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(StringUtils.isEmpty(macMap.get(keyArray[i]))){
                macMap.remove(keyArray[i]);
            }else if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }
            }
        }
        macMap.remove("secret");
        return getSHA256String(sb.toString());
    }
    /**
     *   订单状态同步 腾讯出行
     */
    public SzUnicomZopCheckUserVo checkUser(SzUnicomZopCheckUserDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:checkUser");
        thirdInterfaceLogDTO.setLogRequest("check-user");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("certName",dto.getCertName());
            macMap.put("certNum",dto.getCertNum());
            macMap.put("provinceCode",dto.getProvinceCode());
            macMap.put("cityCode",dto.getCityCode());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.checkUser(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopCheckUserVo.class);
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /**
     *   订单状态同步 腾讯出行
     */
    public SzUnicomZopPhoneListVo selectNum(SzUnicomZopSelectNumDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:selectNum");
        thirdInterfaceLogDTO.setLogRequest("selectNum");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("cityCode",dto.getCityCode());
            macMap.put("goodsId",szUnicomZopConfig.getGoodsId());
            macMap.put("provinceCode",dto.getProvinceCode());
            macMap.put("searchCategory",dto.getSearchCategory());
            macMap.put("searchType",dto.getSearchType());
            macMap.put("searchValue",dto.getSearchValue());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.selectNum(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            //thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            SzUnicomZopSelectNumVo szUnicomZopSelectNumVo = new ObjectMapper().readValue(jsonResult, SzUnicomZopSelectNumVo.class);
            SzUnicomZopPhoneListVo szUnicomZopPhoneListVo = new SzUnicomZopPhoneListVo();
            if(szUnicomZopSelectNumVo.getData() != null){
                List<String> dealData = szUnicomZopSelectNumVo.getData().getBody().getNumArray();
                List<SzUnicomZopPhoneListContentVo> returnPhoneList =  new ArrayList<>();
                for(int i = 0;i< dealData.size();  i = i +12){
                    SzUnicomZopPhoneListContentVo phoneContent = new SzUnicomZopPhoneListContentVo();
                    phoneContent.setPhoneNum(dealData.get(i));
                    phoneContent.setAdvanceLimit(dealData.get(i+1));
                    phoneContent.setMonthFeeLimit(dealData.get(i+3));
                    phoneContent.setNicerule(dealData.get(i+5));
                    phoneContent.setMonthlimit(dealData.get(i+6));
                    returnPhoneList.add(phoneContent);
                }
                szUnicomZopPhoneListVo.setPhoneList(returnPhoneList);
            }
            szUnicomZopPhoneListVo.setCode(szUnicomZopSelectNumVo.getCode());
            szUnicomZopPhoneListVo.setMessage(szUnicomZopSelectNumVo.getMessage());
            return szUnicomZopPhoneListVo;
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }



    /**
     *   3.1短信验证码发送 https://www.showdoc.com.cn/szunicomzopapi/6040797941929221
     */
    public SzUnicomZopNormalVo safeCode(SzUnicomZopSafeCodeDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:safeCode");
        thirdInterfaceLogDTO.setLogRequest("safeCode");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("certNo",dto.getCertNo());
            macMap.put("channel",szUnicomZopConfig.getContactCode());
            macMap.put("contactNum",dto.getContactNum());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.safeCode(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopNormalVo.class);
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }
    /**
     *   3.2短信验证码校验 https://www.showdoc.com.cn/szunicomzopapi/6040798794520352
     */
    public SzUnicomZopCheckCodeVo checkCode(SzUnicomZopCheckCodeDTO dto){
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:checkCode");
        thirdInterfaceLogDTO.setLogRequest("checkCode");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("certNo",dto.getCertNo());
            macMap.put("channel",szUnicomZopConfig.getContactCode());
            macMap.put("contactNum",dto.getContactNum());
            macMap.put("safeCode",dto.getSafeCode());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.checkCode(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopCheckCodeVo.class);
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /**
     *  2.3、风险监控 https://www.showdoc.com.cn/szunicomzopapi/6040796949900372
     */
    public SzUnicomZopNormalVo checkRisk(SzUnicomZopCheckRiskDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:checkRisk");
        thirdInterfaceLogDTO.setLogRequest("checkRisk");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("address",dto.getAddress());
            macMap.put("certName",dto.getCertName());
            macMap.put("certNo",dto.getCertNo());
            macMap.put("channel",szUnicomZopConfig.getContactCode());
            macMap.put("contactNum",dto.getContactNum());
            macMap.put("cityCode",dto.getCityCode());
            macMap.put("postCityCode",dto.getPostCityCode());
            macMap.put("postDistrictCode",dto.getPostDistrictCode());
            macMap.put("postProvinceCode",dto.getPostProvinceCode());
            macMap.put("provinceCode",dto.getProvinceCode());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.checkRisk(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopNormalVo.class);
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /**
     *  5.3 选号后置-意向单同步 https://www.showdoc.com.cn/szunicomzopapi/8097652306164079
     */
    public SzUnicomZopCreateOrderIntentionVo createOrderIntention(SzUnicomZopCreateOrderIntentionDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:createOrderIntention");
        thirdInterfaceLogDTO.setLogRequest("createOrderIntention");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("goodsId",szUnicomZopConfig.getGoodsId());
            macMap.put("certName",dto.getCertName());
            macMap.put("certNo",dto.getCertNo());
            macMap.put("contactNum",dto.getContactNum());
            macMap.put("postProvinceCode",dto.getPostProvinceCode());
            macMap.put("postCityCode",dto.getPostCityCode());
            macMap.put("postDistrictCode",dto.getPostDistrictCode());
            macMap.put("postAddr",dto.getPostAddr());
            macMap.put("channel",szUnicomZopConfig.getContactCode());
            macMap.put("referrerCode",szUnicomZopConfig.getReferrerCode());
            macMap.put("pageUrl",szUnicomZopConfig.getPageUrl());
            macMap.put("resourceId",szUnicomZopConfig.getResourceId());
            macMap.put("launchPlatform",szUnicomZopConfig.getLaunchPlatform());
            macMap.put("firstMonthFee", szUnicomZopConfig.getFirstMonthFee());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.createOrderIntention(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            SzUnicomZopCreateOrderIntentionVo vo = new ObjectMapper().readValue(jsonResult, SzUnicomZopCreateOrderIntentionVo.class);
            vo.setDataObj(JSON.parseObject(vo.getData()));
            vo.setData("");
            return vo;
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }
    /**
     *  5.5 选号后置-正式单同步（2I2C接口自动预占号码）https://www.showdoc.com.cn/szunicomzopapi/8743066635005584
     */
    public SzUnicomZopCreateOrderIntentionFormalVo createOrderIntentionFormal(SzUnicomZopCreateOrderIntentionFormalDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:intentionFormal");
        thirdInterfaceLogDTO.setLogRequest("createOrderIntentionFormal");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("goodsId",szUnicomZopConfig.getGoodsId());
            macMap.put("provinceCode",dto.getProvinceCode());
            macMap.put("cityCode",dto.getCityCode());
            macMap.put("phoneNum",dto.getPhoneNum());
            macMap.put("token",dto.getToken());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            macMap.put("createTime", sdf.format(new Date()));
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.createOrderIntentionFormal(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            SzUnicomZopCreateOrderIntentionFormalVo vo = new ObjectMapper().readValue(jsonResult, SzUnicomZopCreateOrderIntentionFormalVo.class);

            if (vo.getCode().equals("0")) {
                vo.setDataObj(JSON.parseObject((String) vo.getData()));
            }
            vo.setData(null);

            return vo;
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /**
     *  8.4根据订单号获取订单信息 https://www.showdoc.com.cn/szunicomzopapi/7157993617437798
     */
    public SzUnicomZopOrderGetCardVo orderGetCard(SzUnicomZopOrderGetCardDTO dto) {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:orderGetCard");
        thirdInterfaceLogDTO.setLogRequest("orderGetCard");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("orderNo",dto.getOrderNo());
            macMap.put("type",dto.getType());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.orderGetCard(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            //thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopOrderGetCardVo.class);

        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /**
     *  5.6.1资源上传接口 集合传数 https://www.showdoc.com.cn/szunicomzopapi/10493545282360839
     */
    public SzUnicomZopNormalVo resourceV1(){
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("UnicomZop:resourceV1");
        thirdInterfaceLogDTO.setLogRequest("resourceV1");
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("goodsId",szUnicomZopConfig.getGoodsId());
            macMap.put("channel",szUnicomZopConfig.getContactCode());
            macMap.put("appKey",szUnicomZopConfig.getAppKey());
            macMap.put("sign",getSign(macMap));
           // List<MultipartFile> files = new ArrayList<>();
            //files.add("");
            thirdInterfaceLogDTO.setLogParams(macMap.toString());
            String jsonResult = callSzUnicomZopFeign.orderGetCard(URI.create(szUnicomZopConfig.getHostUrl()),macMap);
            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            return new ObjectMapper().readValue(jsonResult, SzUnicomZopNormalVo.class);
        }catch (Exception e) {
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }
}
