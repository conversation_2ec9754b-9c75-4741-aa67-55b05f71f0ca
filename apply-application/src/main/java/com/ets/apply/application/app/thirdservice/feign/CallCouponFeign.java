package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallCouponFallBackFactory;
import com.ets.apply.application.app.thirdservice.request.coupon.CouponSettleDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${microUrls.coupon:http://etc-coupon-service:9000}",
        name = "CallCouponFeign", fallbackFactory = CallCouponFallBackFactory.class)
public interface CallCouponFeign {
    /**
     * 用户可用优惠券列表
     * @param couponSettleDTO 请求参数
     * @return 可用优惠券列表
     */
    @PostMapping("/coupon/use/availableNew")
    String listAvailableBySettle(@RequestBody CouponSettleDTO couponSettleDTO);
}
