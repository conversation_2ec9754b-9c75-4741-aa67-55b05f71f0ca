package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.creditCard.CiticBusiness;
import com.ets.apply.application.common.dto.request.citic.CiticOrderStatusQueryDTO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ets.apply.application.common.dto.request.citic.CiticOrderReceiveDTO;


/**
 * 中信银行
 */
@RequestMapping("/citic")
@RefreshScope
@RestController
@Slf4j
public class CiticController extends BaseController {


    @Autowired
    private CiticBusiness citicBusiness;

    /**
     * 中信银行接收订单状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/receiveOrder")
    public JsonResult<?> receiveOrder(@RequestBody CiticOrderReceiveDTO dto) {
        citicBusiness.receiveOrder(dto);
        return JsonResult.ok();
    }


    /**
     * 中信银行查询状态接口，提供给php 调用
     * @param dto
     * @return
     */
    @PostMapping("/statusQuery")
    public JsonResult<?> statusQuery(@RequestBody CiticOrderStatusQueryDTO dto) {
        return JsonResult.ok(citicBusiness.statusQuery(dto));
    }
}
