package com.ets.apply.application.infra.entity;

import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.ets.apply.application.common.bo.unicom.UnicomNotifyUrlBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 联通流量卡申请
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mission_unicom_apply")
public class MissionUnicomApplyEntity extends BaseEntity<MissionUnicomApplyEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 申请单号
     */
    private String unicomApplySn;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 办理手机号码（加密）
     */
    private String phone;

    /**
     * 号卡订单状态
     */
    private String unicomOrderState;

    /**
     * 用户号卡状态，同步接口状态
     */
    private Integer phoneStatus;

    /**
     * 是否三无
     */
    private Integer isThreeNone;

    /**
     * 是否黑名单
     */
    private Integer isBlackList;

    /**
     * 累计充值金额
     */
    private BigDecimal accumulatedAmount;

    /**
     * 首充金额，单位分
     */
    private Integer firstRecharge;

    /**
     * 申请状态
     */
    private Integer status;

    /**
     * 关联类型：1申办订单
     */
    private Integer referType;

    /**
     * 关联流水号，1申办订单号order_sn
     */
    private String referSn;

    /**
     * 联通商品编号
     */
    private String unicomGoodsId;

    /**
     * 联通订单号
     */
    private String unicomOrderSn;

    /**
     * 是否可复用: 1是，0否
     */
    private Integer canReuse;

    /**
     * 号卡资格有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 客户姓名(加密)
     */
    private String sendName;

    /**
     * 联系手机号（加密）
     */
    private String sendPhone;

    /**
     * 收货地区 省 市 区, 空格隔离
     */
    private String area;

    /**
     * 收货地区编码 省 市 区, 空格隔离
     */
    private String areaCode;

    /**
     * 收货详细地址（加密）
     */
    private String address;

    /**
     * 身份证号（加密）
     */
    private String certNo;

    /**
     * 奖励配置编号
     */
    private String missionConfigSn;

    /**
     * 是否已发放奖励
     */
    private Integer hasReward;

    /**
     * 奖励内容
     */
    private String rewardInfo;

    /**
     * 业务通知地址，json {"statusChange":"业务状态变更通知地址"}
     */
    private String notifyUrl;

    /**
     * 业务通知状态
     */
    private Integer notifyStatus;

    /**
     * 拒绝原因
     */
    private String rejectInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public UnicomNotifyUrlBO getNotifyUrlBO() {

        if (StringUtils.isEmpty(notifyUrl)) {
            return new UnicomNotifyUrlBO();
        } else {
            return JSON.parseObject(notifyUrl, UnicomNotifyUrlBO.class);
        }
    }

}
