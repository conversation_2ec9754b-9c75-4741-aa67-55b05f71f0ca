package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.BaseApplicationFallbackFactory;
import com.ets.apply.application.app.thirdservice.response.IpAddressInfoVO;
import com.ets.base.feign.request.sms.SendCodeRequestDTO;
import com.ets.base.feign.request.sms.VerifyCodeRequestDTO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;

@FeignClient(
        url = "${params.microUrls.etc-java-base}",
        name = "BaseApplicationFeign",
        fallbackFactory = BaseApplicationFallbackFactory.class
)
public interface BaseApplicationFeign {

    @GetMapping(value = "/ip/getIpAddressInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    JsonResult<IpAddressInfoVO> getIpAddressInfo(@RequestParam(value = "ip") String ip);

    @PostMapping("/sms/send-code")
    JsonResult<HashMap<String, String>> sendCode(@RequestBody SendCodeRequestDTO sendCodeDTO);

    @PostMapping("/sms/verify-code")
    JsonResult<Object> verifyCode(@RequestBody VerifyCodeRequestDTO requestDTO);

}
