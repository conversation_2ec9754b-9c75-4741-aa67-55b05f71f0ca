package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.SegmentBenefitConfigs;
import com.ets.apply.application.infra.mapper.SegmentBenefitConfigsMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 号段权益记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
@DS("db-apply")
public class SegmentBenefitConfigsService extends BaseService<SegmentBenefitConfigsMapper, SegmentBenefitConfigs> {

}
