package com.ets.apply.application.common.consts.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaymentModeEnum {
    PAYMENT_MODE_WEIXIN(1, "普通微信签约代扣"),
    PAYMENT_MODE_VEHICLE(2, "车主平台签约代扣"),
    PAYMENT_MODE_CCB(4, "建行(北京)签约代扣"),
    PAYMENT_MODE_CMB(5, "招行(广西)签约代扣"),
    PAYMENT_MODE_WEBANK(7, "微众银行签约代扣"),
    PAYMENT_MODE_VEHICLE_BIND_BANK(9, "车主平台绑定银行卡签约代扣");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (PaymentModeEnum node : PaymentModeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static PaymentModeEnum getEnumByCode(int code) {
        for (PaymentModeEnum node : PaymentModeEnum.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }
        return null;
    }
}
