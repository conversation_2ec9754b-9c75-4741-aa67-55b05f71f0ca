package com.ets.apply.application.common.dto.request.creditCard;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreditCardBankInfoV2DTO {


    @NotNull(message = "银行参数不能为空")
    private Integer whichBank;

    @NotEmpty(message = "关联订单号不能为空")
    private String referSn;

    @NotNull(message = "用户id不能为空")
    private Long uid;


}
