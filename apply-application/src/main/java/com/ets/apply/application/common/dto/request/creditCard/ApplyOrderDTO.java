package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class ApplyOrderDTO {

    private Integer classify;
    private String plateNo;

    @NotNull(message = "uid 不能为空")
    private Long uid;

    @NotNull(message = "银行类型不能为空")
    private Integer whichBank;
    private String username;
    private String phoneNumber;

    @NotNull(message = "关联单号不能为空")
    private String referSn;

    @NotNull(message = "关联类型不能为空")
    private Integer referType;

    /**
     * 回调url
     */
    private String callback = "";

    /**
     * 银行回跳前端地址
     */
    private String redirectUrl = "";

    private String version = "";

    /**
     * 第三方申请类型
     */
    private Integer subReferType;

    /**
     * 内部版本
     */
    private String innerVersion;

    /**
     * 银行申请编号
     */
    private String bankApplyNumber = "";

    /**
     * 渠道,标记不同渠道用来提供不同的申请链接
     */
    private String channel = "";
}
