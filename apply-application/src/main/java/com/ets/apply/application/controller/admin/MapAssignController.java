package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.map.MapAssignBusiness;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.map.MapAssignListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/mapAssign")
public class MapAssignController extends BaseController {
    @Autowired
    private MapAssignBusiness mapAssignBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/get-list")
    @ResponseBody
    public JsonResult<IPage<MapAssignListVO>> getList(@RequestBody(required = false) @Valid MapAssignDTO dto) {
        return JsonResult.ok(mapAssignBusiness.getList(dto));
    }



    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<Boolean> add(@RequestBody(required = false) @Valid MapAssignEditDTO dto) {
        mapAssignBusiness.addAssign(dto, UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify")
    @ResponseBody
    public JsonResult<Boolean> modify(@RequestBody(required = false) @Valid MapAssignEditDTO dto) {
        mapAssignBusiness.updateAssign(dto, UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }
    @RequestMapping("/del")
    @ResponseBody
    public JsonResult<Boolean> del(@RequestBody(required = false) @Valid MapAssignDelDTO dto) {
        mapAssignBusiness.delAssign(dto, UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }


}
