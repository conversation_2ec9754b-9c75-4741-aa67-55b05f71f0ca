package com.ets.apply.application;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
@EnableFeignClients(value = "com.ets.*")
@ComponentScans(value = @ComponentScan("com.ets.*"))
@MapperScan("com.ets.*")
@EnableAsync
@EnableCaching
public class ApplyApplication {

    public static void main(String[] args) {

        SpringApplication.run(ApplyApplication.class, args);
    }
}
