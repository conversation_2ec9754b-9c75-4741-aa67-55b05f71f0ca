# IntelliJ project files
.idea/
*.iml
out
gen

# Visual Studio Code
.history/
.vscode
.factorypath

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
!/.mvn/wrapper/maven-wrapper.jar

# Eclipse
.classpath
.settings/
.project
bin/

# System related
*.DS_Store
Thumbs.db
apply-application/src/main/resources/rebel.xml
/apply-feign/src/main/resources/rebel.xml
