package com.ets.apply.application.business;

import com.ets.apply.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfReviewsReviewDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.CreateAfterSalesReviewDTO;
import com.ets.apply.application.common.vo.aftersalesreivews.AfterSalesReviewsVO;
import com.ets.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 售后审核单业务测试
 */
@Slf4j
@SpringBootTest
public class AfterSalesReviewsBusinessTest {

    @Autowired
    private AfterSalesReviewsBusiness afterSalesReviewsBusiness;

    @Test
    public void testReviewApproved() {
        try {
            // 先创建一个审核单用于测试
            CreateAfterSalesReviewDTO createDTO = createTestReviewDTO();
            AfterSalesReviewsVO reviewVO = afterSalesReviewsBusiness.createAfterSalesReview(createDTO);

            // 测试审核通过
            AdminAfReviewsReviewDTO reviewDTO = new AdminAfReviewsReviewDTO();
            reviewDTO.setReviewSn(reviewVO.getReviewSn());
            reviewDTO.setReviewStatus(AftersalesReviewsStatusEnum.APPROVED.getValue());

            // 执行审核
            afterSalesReviewsBusiness.review(reviewDTO);

            log.info("审核通过测试成功，审核单号: {}", reviewVO.getReviewSn());

        } catch (Exception e) {
            log.error("审核通过测试失败", e);
        }
    }

    @Test
    public void testReviewRejected() {
        try {
            // 先创建一个审核单用于测试
            CreateAfterSalesReviewDTO createDTO = createTestReviewDTO();
            AfterSalesReviewsVO reviewVO = afterSalesReviewsBusiness.createAfterSalesReview(createDTO);

            // 测试审核驳回
            AdminAfReviewsReviewDTO reviewDTO = new AdminAfReviewsReviewDTO();
            reviewDTO.setReviewSn(reviewVO.getReviewSn());
            reviewDTO.setReviewStatus(AftersalesReviewsStatusEnum.REJECTED.getValue());
            reviewDTO.setRejectType("aftersales_reject_vehicle");
            reviewDTO.setRejectReason("提供的行驶证照片不清晰，请重新上传");

            // 执行审核
            afterSalesReviewsBusiness.review(reviewDTO);

            log.info("审核驳回测试成功，审核单号: {}", reviewVO.getReviewSn());

        } catch (Exception e) {
            log.error("审核驳回测试失败", e);
        }
    }

    @Test
    public void testReviewRejectedWithoutReason() {
        try {
            // 先创建一个审核单用于测试
            CreateAfterSalesReviewDTO createDTO = createTestReviewDTO();
            AfterSalesReviewsVO reviewVO = afterSalesReviewsBusiness.createAfterSalesReview(createDTO);

            // 测试审核驳回但不提供拒绝原因（应该抛出异常）
            AdminAfReviewsReviewDTO reviewDTO = new AdminAfReviewsReviewDTO();
            reviewDTO.setReviewSn(reviewVO.getReviewSn());
            reviewDTO.setReviewStatus(AftersalesReviewsStatusEnum.REJECTED.getValue());
            // 故意不设置 rejectReason

            // 执行审核，应该抛出异常
            assertThrows(BizException.class, () -> {
                afterSalesReviewsBusiness.review(reviewDTO);
            });

            log.info("审核驳回无原因测试成功（正确抛出异常）");

        } catch (Exception e) {
            log.error("审核驳回无原因测试失败", e);
        }
    }

    @Test
    public void testReviewNonExistentReview() {
        // 测试审核不存在的审核单（应该抛出异常）
        AdminAfReviewsReviewDTO reviewDTO = new AdminAfReviewsReviewDTO();
        reviewDTO.setReviewSn("99999"); // 使用不存在的
        reviewDTO.setReviewStatus(AftersalesReviewsStatusEnum.APPROVED.getValue());

        // 执行审核，应该抛出异常
        assertThrows(BizException.class, () -> {
            afterSalesReviewsBusiness.review(reviewDTO);
        });

        log.info("审核不存在审核单测试成功（正确抛出异常）");
    }

    /**
     * 创建测试用的审核单DTO
     */
    private CreateAfterSalesReviewDTO createTestReviewDTO() {
        CreateAfterSalesReviewDTO dto = new CreateAfterSalesReviewDTO();
        dto.setOrderSn("TEST_ORDER_" + System.currentTimeMillis());
        dto.setOrderType("reactivate");
        dto.setIssuerId(1);
        dto.setUid(12345L);
        dto.setPlateNo("京A" + (int)(Math.random() * 10000));
        dto.setPlateColor(0);
        dto.setNotifyUrl("http://etc-saleafter-main:6610/reactivate/reviewResult");

        // 创建行驶证信息
        CreateAfterSalesReviewDTO.VehicleInfoDTO vehicleInfo = new CreateAfterSalesReviewDTO.VehicleInfoDTO();
        vehicleInfo.setPlateNo(dto.getPlateNo());
        vehicleInfo.setPlateColor(dto.getPlateColor());
        vehicleInfo.setOwner("测试用户");
        vehicleInfo.setType("小型汽车");
        vehicleInfo.setUseCharacter("非营运");
        vehicleInfo.setPassengers("5");
        vehicleInfo.setFrontImgUrl("https://example.com/front.jpg");
        vehicleInfo.setBackImgUrl("https://example.com/back.jpg");
        vehicleInfo.setFrontCarImgUrl("https://example.com/car_front.jpg");
        vehicleInfo.setGearActivateImgUrl("https://example.com/gear.jpg");

        dto.setReviewVehicleInfo(vehicleInfo);
        dto.setOrderVehicleInfo(vehicleInfo);

        return dto;
    }
}
