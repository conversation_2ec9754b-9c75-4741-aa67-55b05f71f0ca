package com.ets.apply.application.common.dto.creditCardBank;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CreditCardBankModifyDTO {


    @NotNull(message = "id不能为空")
    private Integer id;



    /**
     * 标签
     */
    private List<Object> tagList;

    /**
     * 主权益
     */
    private String welfare;

    /**
     * 权益说明
     */
    private List<Object> welfareDetail;

    /**
     * 附加信息
     */
    private Object extraInfo;


}
