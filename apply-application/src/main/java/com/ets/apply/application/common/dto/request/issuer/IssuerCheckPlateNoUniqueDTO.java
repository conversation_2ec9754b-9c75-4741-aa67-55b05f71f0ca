package com.ets.apply.application.common.dto.request.issuer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerCheckPlateNoUniqueDTO {


    @JsonProperty("plate_color")
    private Integer plateColor;

    @JsonProperty("plate_no")
    private String plateNo;

    @JsonProperty("car_type")
    private Integer carType;
    @JsonProperty("send_phone")
    private String sendPhone;

    private String owner;

    private Long uid;

    private String number;

    @JsonProperty("vehicle_belong")
    private Integer vehicleBelong;

    @JsonProperty("send_area")
    private String sendArea;

    @JsonProperty("send_address")
    private String sendAddress;


}
