package com.ets.apply.application.infra.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 线下渠道表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("etc_promotion_channel")
public class PromotionChannelEntity extends BaseEntity<PromotionChannelEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 省份
     */
    private String province;

    /**
     * 渠道类型
     */
    private Integer type;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 卡指定类型
     */
    private Integer cardMod;

    /**
     * 卡id
     */
    private Integer cardId;

    /**
     * 渠道状态
     */
    private Integer status;

    /**
     * 渠道标识
     */
    private String tag;

    /**
     * 付费类型
     */
    private Integer payType;

    /**
     * 功能场景
     */
    private Integer businessMod;

    /**
     * 奖励金额
     */
    private BigDecimal reward;

    /**
     * 合作链接
     */
    private String url;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 运营负责人
     */
    private String manager;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 渠道人数
     */
    private Integer cnt;

    /**
     * 渠道pl值
     */
    private Integer pl;

    /**
     * 渠道是否公开
     */
    private Integer isOpen;


}
