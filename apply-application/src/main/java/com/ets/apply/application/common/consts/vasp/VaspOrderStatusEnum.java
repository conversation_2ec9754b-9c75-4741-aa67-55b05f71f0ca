package com.ets.apply.application.common.consts.vasp;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VaspOrderStatusEnum {
    ORDER_STATUS_CANCEL("0", "订单取消"),
    ORDER_STATUS_AUDIT("1", "初审通过"),
    ORDER_STATUS_PREPARING("2", "配货完成"),
    ORDER_STATUS_SHIP("3", "产生物流单号"),
    ORDER_STATUS_RECEIVE("4", "客户签收"),
    ORDER_STATUS_REJECT("5", "客户拒收"),
    ORDER_STATUS_RETURN("6", "客户退货"),
    ORDER_STATUS_EXCHANGE_GOODS("7", "客户换货"),
    ORDER_STATUS_FINISH("9", "订单完成");
    private final String code;

    private final String description;
}
