package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VehicleBelongEnum {
    Personal(1, "个人车"),

    Company(2, "单位车");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (VehicleBelongEnum node : VehicleBelongEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
