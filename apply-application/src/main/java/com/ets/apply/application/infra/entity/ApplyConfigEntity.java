package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 申办配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("apply_config")
public class ApplyConfigEntity extends BaseEntity<ApplyConfigEntity> {

    private static final long serialVersionUID = 1L;

    private String configSn;

    /**
     * 配置缓存键
     */
    private String domainKey;

    /**
     * 键值
     */
    private String params;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否删除[0-未删除 1-已删除]
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
