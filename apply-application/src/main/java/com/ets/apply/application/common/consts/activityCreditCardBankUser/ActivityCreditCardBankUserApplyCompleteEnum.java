package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserApplyCompleteEnum {

    APPLY_DEFAULT(0, "默认"),
    APPLY_COMPLETED(1, "申请完成"),
    APPLY_FAILED(2,"申请失败");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserApplyCompleteEnum node : ActivityCreditCardBankUserApplyCompleteEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
