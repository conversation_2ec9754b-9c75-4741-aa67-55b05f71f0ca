package com.ets.apply.application.common.vo.orderCenter;

import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.common.consts.productPackage.DeviceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderCenterApplyOrderInfoVO {
    /**
     * 订单号
     */
    String orderSn;

    /**
     * 业务类型
     */
    Integer businessType;
    String businessTypeStr;

    /**
     * 订单状态
     */
    Integer orderStatus;
    String orderStatusStr;

    /**
     * 设备类型
     */
    Integer deviceType = DeviceTypeEnum.NORMAL.getValue();

    /**
     * 设备名称
     */
    String deviceName;

    /**
     * 商品图片
     */
    String goodsImgUrl = "https://wecos.etczs.net/apply/progress/obu-img-first-normal-0721.png";

    /**
     * 车牌号
     */
    String plateNo;

    /**
     * 车牌颜色
     */
    Integer plateColor;

    /**
     * 支付金额
     */
    BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime paidTime;

    /**
     * 保证金金额
     */
    BigDecimal depositFee;

    /**
     * 保证金状态
     */
    Integer depositStatus;

    /**
     * 售后状态 0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
     */
    Integer aftersaleStatus;

    /**
     * 激活状态 0未激活，1已激活，2已注销
     */
    Integer activatedStatus;
    /**
     * 能否做商品售后，0不可，1可以
     */
    Integer canAftersaleGoods = 0;
    /**
     * 商品售后状态：1售后中2售后完成
     */
    Integer aftersaleGoodsStatus = 0;

    /**
     * 能否做激活售后，0不可，1可以
     */
    Integer canAftersaleIssuer = 0;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime updatedAt;


    String goodsOrderSn;

    public String getOrderStatusStr() {
        return StatusEnum.getDescByCode(orderStatus);
    }


    /**
     * 是否显示回收按钮
     */
    private Boolean showRecovery = false;

    /**
     * 是否前装设备 是否前装[0-默认 1-前装]
     */
    private Integer isFront;

    /*
     *  活动标识对象
     */

    private Object activity;
}
