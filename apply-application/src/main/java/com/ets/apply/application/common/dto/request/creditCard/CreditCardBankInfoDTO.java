package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class CreditCardBankInfoDTO {

    @NotNull(message = "类型不能为空")
    private Integer referType;

    @NotNull(message = "银行参数不能为空")
    private Integer whichBank;

    @NotEmpty(message = "关联订单号不能为空")
    private String referSn;


}
