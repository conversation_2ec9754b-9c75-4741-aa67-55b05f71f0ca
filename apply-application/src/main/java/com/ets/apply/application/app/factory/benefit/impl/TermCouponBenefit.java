package com.ets.apply.application.app.factory.benefit.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallCenterMarketingFeign;
import com.ets.apply.application.app.thirdservice.request.SendTermCouponDTO;
import com.ets.apply.application.common.bo.orderBenefitRecord.TermCouponContentBO;
import com.ets.apply.application.common.consts.segmentBenefit.BenefitTypeEnum;
import com.ets.apply.application.infra.entity.OrderBenefitRecord;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TermCouponBenefit extends BenefitBase {

    @Autowired
    private CallCenterMarketingFeign centerMarketingFeign;

    @Override
    public OrderBenefitRecord add(String orderSn, String benefitContent) {
        OrderBenefitRecord record = super.add(orderSn, benefitContent);
        record.setBenefitType(BenefitTypeEnum.TERM_COUPON.getType());
        orderBenefitRecordService.create(record);

        return record;
    }

    @Override
    public String getContentDesc(String benefitContent) {
        TermCouponContentBO contentBO = JSON.parseObject(benefitContent, TermCouponContentBO.class);
        return "券包【" + contentBO.getTermName() + "】（term：" + contentBO.getTerm() + "）";
    }

    @Override
    public void send(Integer recordId) {
        OrderBenefitRecord record = orderBenefitRecordService.getById(recordId);
        if (ObjectUtils.isNotEmpty(record)) {
            TermCouponContentBO contentBO = JSON.parseObject(record.getBenefitContent(), TermCouponContentBO.class);

            SendTermCouponDTO dto = new SendTermCouponDTO();
            dto.setBusinessOrderNo(record.getOrderSn());
            dto.setUserId(contentBO.getUid());
            dto.setSendNo(contentBO.getTerm());
            dto.setPlateNo(contentBO.getPlateNo());
            String result = centerMarketingFeign.sendTermCoupon(dto);
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.getInteger("code") != 0) {
                ToolsHelper.throwException(jsonObject.getString("msg"));
            }
        }
    }
}
