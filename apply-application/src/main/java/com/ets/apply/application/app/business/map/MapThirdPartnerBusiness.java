package com.ets.apply.application.app.business.map;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.common.consts.map.MapStatusEnum;
import com.ets.apply.application.common.dto.map.MapThirdPartnerAddPackageDTO;
import com.ets.apply.application.common.dto.map.MapThirdPartnerPackageDTO;
import com.ets.apply.application.common.dto.map.MapThirdPartnerProdDTO;
import com.ets.apply.application.common.vo.map.*;
import com.ets.apply.application.common.vo.wecar.WecarProductVo;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerCacheEntity;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerEntity;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerPackageEntity;
import com.ets.apply.application.infra.service.*;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class MapThirdPartnerBusiness {
    @Autowired
    private MapThirdPartnerService mapThirdPartnerService;
    @Autowired
    private MapThirdPartnerPackageService mapThirdPartnerPackageService;
    @Autowired
    private ProductPackageBusiness productPackageBusiness;
    @Autowired
    private MapLogService mapLogService;
    @Autowired
    private MapThirdPartnerCacheService mapCacheDatasService;
    /*
     * 获取全部模块列表
     */
    public IPage<MapThirdPartnerListVO> getList() {
        // 分页设置
        IPage<MapThirdPartnerEntity> oPage = new Page<>(0, 1000, true);

        // 查询条件设置
        LambdaQueryWrapper<MapThirdPartnerEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.orderByDesc(MapThirdPartnerEntity::getSort);
        IPage<MapThirdPartnerEntity> pageList = mapThirdPartnerService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            MapThirdPartnerListVO vo = new MapThirdPartnerListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setModule("thirdPartner");
            return vo;
        });
    }

    /*
     * 获取全部产品包列表
     */
    public IPage<MapThirdPartnerPackageListVO> getPackageList(MapThirdPartnerPackageDTO dto) {
        // 分页设置
        IPage<MapThirdPartnerPackageEntity> oPage = new Page<>(1, 1000, true);
        // 查询条件设置
        LambdaQueryWrapper<MapThirdPartnerPackageEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MapThirdPartnerPackageEntity::getPartnerId, dto.getPartnerId())
               .eq(MapThirdPartnerPackageEntity::getStatus, MapStatusEnum.STAUTS_NORMAL.getCode())
                .eq(StringUtils.isNotEmpty(dto.getPackageSn()), MapThirdPartnerPackageEntity::getPackageSn, dto.getPackageSn())
               .orderByDesc(MapThirdPartnerPackageEntity::getSort);
        IPage<MapThirdPartnerPackageEntity> pageList = mapThirdPartnerPackageService.getPageListByWrapper(oPage, wrapper);
        MapThirdPartnerEntity mapThirdPartnerEntity = mapThirdPartnerService.getById(dto.getPartnerId());
        if(mapThirdPartnerEntity == null){
            ToolsHelper.throwException("合作方id错误："+dto.getPartnerId());
        }
        return pageList.convert(record-> {
            MapThirdPartnerPackageListVO vo = new MapThirdPartnerPackageListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setCompanyName(mapThirdPartnerEntity.getName());
            return vo;
        });
    }

    /*
     * 新增产品包
     */
    public Boolean addPackage(MapThirdPartnerAddPackageDTO dto, String loginCode){
        MapThirdPartnerPackageEntity mapThirdPartnerPackage = new MapThirdPartnerPackageEntity();
        mapThirdPartnerPackage.setPartnerId(dto.getPartnerId());
        mapThirdPartnerPackage.setAliasName(dto.getAliasName());
        mapThirdPartnerPackage.setPackageSn(dto.getPackageSn());
        mapThirdPartnerPackage.setOriginalPrice(dto.getOriginalPrice());
        mapThirdPartnerPackage.setPresentPrice(dto.getPresentPrice());
        mapThirdPartnerPackage.setProductImg(dto.getProductImg());
        mapThirdPartnerPackage.setIcon(dto.getIcon());
        mapThirdPartnerPackage.setOperator(loginCode);
        mapThirdPartnerPackage.setSort(dto.getSort());
        mapThirdPartnerPackageService.create(mapThirdPartnerPackage);

        //记录日志
        mapLogService.addLog(
                loginCode,
                "thirdPartner",
                dto.getPartnerId(),
                "addPackage",
                "新增产品包",
                "",
                JSONObject.toJSONString(dto)
        );
        //更新缓存
        //mapBusiness.updatePreviewCache(mapPackageSnEntity.getId());

        return true;
    }

    public Boolean updatePackage(MapThirdPartnerAddPackageDTO dto, String loginCode){
        MapThirdPartnerPackageEntity mapThirdPartnerPackage = mapThirdPartnerPackageService.getById(dto.getId());
        if(mapThirdPartnerPackage == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        //对比数据
//        JSONObject compareDifffObject = compareDiff(mapThirdPartnerPackage,dto);
//        if((compareDifffObject.getJSONObject("diff")).isEmpty()){
//            ToolsHelper.throwException("数据无变更");
//        }
        LambdaUpdateWrapper<MapThirdPartnerPackageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapThirdPartnerPackageEntity::getId, dto.getId())
                .set(dto.getStatus()>0,MapThirdPartnerPackageEntity::getStatus, dto.getStatus())
                .set(dto.getSort()>0,MapThirdPartnerPackageEntity::getSort, dto.getSort())
                .set(StringUtils.isNotEmpty(loginCode),MapThirdPartnerPackageEntity::getOperator, loginCode)
                .set(StringUtils.isNotEmpty(dto.getPackageSn()),MapThirdPartnerPackageEntity::getPackageSn, dto.getPackageSn())
                .set(StringUtils.isNotEmpty(dto.getIcon()),MapThirdPartnerPackageEntity::getIcon, dto.getIcon())
                .set(StringUtils.isNotEmpty(dto.getAliasName()),MapThirdPartnerPackageEntity::getAliasName, dto.getAliasName())
                .set(StringUtils.isNotEmpty(dto.getProductImg()),MapThirdPartnerPackageEntity::getProductImg, dto.getProductImg())
                .set(dto.getOriginalPrice() >= 0,MapThirdPartnerPackageEntity::getOriginalPrice, dto.getOriginalPrice())
                .set(dto.getPresentPrice() >= 0,MapThirdPartnerPackageEntity::getPresentPrice, dto.getPresentPrice())
                .set(StringUtils.isNotEmpty(dto.getAliasName()),MapThirdPartnerPackageEntity::getAliasName, dto.getAliasName())
                .set(MapThirdPartnerPackageEntity::getUpdatedAt, LocalDateTime.now());
        mapThirdPartnerPackageService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
                loginCode,
                "thirdPartner",
                mapThirdPartnerPackage.getPartnerId(),
                "updatePackage",
                "更新产品包",
                JSONObject.toJSONString(mapThirdPartnerPackage),
                JSONObject.toJSONString(dto)
        );
        return true;
    }

    public Boolean delPackage(MapThirdPartnerAddPackageDTO dto,String loginCode){
        MapThirdPartnerPackageEntity mapThirdPartnerPackage = mapThirdPartnerPackageService.getById(dto.getId());
        if(mapThirdPartnerPackage == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }

        LambdaUpdateWrapper<MapThirdPartnerPackageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapThirdPartnerPackageEntity::getId, dto.getId())
                .set(MapThirdPartnerPackageEntity::getOperator, loginCode)
                .set(MapThirdPartnerPackageEntity::getUpdatedAt, LocalDateTime.now())
                .set(dto.getStatus()>0,MapThirdPartnerPackageEntity::getStatus, dto.getStatus());
        mapThirdPartnerPackageService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
                loginCode,
                "thirdPartner",
                mapThirdPartnerPackage.getPartnerId(),
                "delPackage",
                "删除产品包",
                JSONObject.toJSONString(mapThirdPartnerPackage),
                ""
        );
        return true;
    }
    /*
     * 生成通用的缓存记录
     */
    public Boolean updateProdCache(MapThirdPartnerProdDTO dto, String loginCode){
        MapThirdPartnerEntity mapThirdPartner = mapThirdPartnerService.getById(dto.getPartnerId());
        if(mapThirdPartner == null){
            ToolsHelper.throwException(dto.getPartnerId()+"不存在数据");
        }
        //获取产品包列表数据
        LambdaQueryWrapper<MapThirdPartnerPackageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MapThirdPartnerPackageEntity::getPartnerId, dto.getPartnerId())
                .eq(MapThirdPartnerPackageEntity::getStatus, MapStatusEnum.STAUTS_NORMAL.getCode())
                .orderByDesc(MapThirdPartnerPackageEntity::getSort);
        List<MapThirdPartnerPackageEntity> dataslist = mapThirdPartnerPackageService.getListByWrapper(wrapper);
        List<JSONObject> listJson = new ArrayList<>();
        Map<String, WecarProductVo> productPackageMap = new HashMap<>();
        dataslist.forEach(mapThirdPartnerPackage-> {
            JSONObject jsonObject = new JSONObject();
            if(!productPackageMap.containsKey(mapThirdPartnerPackage.getPackageSn())){
                productPackageMap.put(mapThirdPartnerPackage.getPackageSn(),productPackageBusiness.getWecarProductVoByPackageSn(mapThirdPartnerPackage.getPackageSn()));
            }
            jsonObject.put("id",mapThirdPartnerPackage.getPackageSn());
            jsonObject.put("aliasName",mapThirdPartnerPackage.getAliasName());
            jsonObject.put("icon",mapThirdPartnerPackage.getIcon());
            jsonObject.put("originalPrice",mapThirdPartnerPackage.getOriginalPrice());
            jsonObject.put("presentPrice",mapThirdPartnerPackage.getPresentPrice());
            jsonObject.put("cardProviderId",productPackageMap.get(mapThirdPartnerPackage.getPackageSn()).getCardProviderId());
            jsonObject.put("cardProviderName",productPackageMap.get(mapThirdPartnerPackage.getPackageSn()).getCardProviderName());
            jsonObject.put("obuType",productPackageMap.get(mapThirdPartnerPackage.getPackageSn()).getObuType());
            jsonObject.put("productImg",mapThirdPartnerPackage.getProductImg());
            listJson.add(jsonObject);
        });

        String type = "prod";
        String cacheMainKey = "MapCache:ThirdPartner:Prod";
        String cacheKey = mapThirdPartner.getCompanyId();
        MapThirdPartnerCacheEntity mapCacheDatas = mapCacheDatasService.getOneByTypeAndMainKeyAndKey(type,cacheMainKey,cacheKey);
        String preOperate = "";
        if(mapCacheDatas == null){
            MapThirdPartnerCacheEntity mapCacheDatasNew = new MapThirdPartnerCacheEntity();
            mapCacheDatasNew.setCacheKey(cacheKey);
            mapCacheDatasNew.setType(type);
            mapCacheDatasNew.setCacheMainKey(cacheMainKey);
            mapCacheDatasNew.setCacheValues(JSONObject.toJSONString(listJson));
            mapCacheDatasService.create(mapCacheDatasNew);
        }else{
            preOperate = mapCacheDatas.getCacheValues();
            mapCacheDatas.setCacheValues(JSONObject.toJSONString(listJson));
            mapCacheDatasService.updateById(mapCacheDatas);
        }

        //记录日志
        mapLogService.addLog(
                loginCode,
                "thirdPartner",
                dto.getPartnerId(),
                "updateProdCache",
                "生产发布",
                preOperate,
                JSONObject.toJSONString(listJson)
        );
        return true;
    }
}
