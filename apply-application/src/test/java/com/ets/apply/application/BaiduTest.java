package com.ets.apply.application;

import com.ets.apply.application.app.business.BaiduBusiness;
import com.ets.apply.application.common.dto.BaiduTplMsgDto;
import com.ets.common.BizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class BaiduTest {

    @Autowired
    BaiduBusiness baiduBusiness;

    @Test
    public void sendTplMsg() throws BizException {
        String data = "{\"keyword1\":{\"value\":\"电子标签设备及ETC卡片\"}," +
                "\"keyword2\":{\"value\": \"" +
                "中通快递" +
                "\"}," +
                "\"keyword3\":{\"value\": \"" +
                "***********" +
                "\"}," +
                "\"keyword4\":{\"value\": \"请收到货后按照说明书指引完成安装激活\"}}";

        BaiduTplMsgDto dto = new BaiduTplMsgDto();
        dto.setUid(1L);
        dto.setTemplateId("order_ship");
        dto.setData(data);
        dto.setPage("pages/apply/applyProgress/applyProgress");
        baiduBusiness.sendTplMsg(dto);
    }
}
