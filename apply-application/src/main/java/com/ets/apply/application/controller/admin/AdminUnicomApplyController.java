package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.unicom.AdminUnicomApplyBusiness;
import com.ets.apply.application.common.dto.adminUnicom.AdminUnicomApplyEditDTO;
import com.ets.apply.application.common.dto.adminUnicom.AdminUnicomGetPageListDTO;
import com.ets.apply.application.common.vo.SelectOptionsVO;
import com.ets.apply.application.common.vo.adminUnicom.UnicomPageListVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;


@RestController
@RequestMapping("/admin/unicomApply")
@Slf4j
public class AdminUnicomApplyController {

	@Autowired
	private AdminUnicomApplyBusiness adminUnicomApplyBusiness;

	@PostMapping("/getPageList")
	public JsonResult<IPage<UnicomPageListVO>> getPageList(@Valid @RequestBody AdminUnicomGetPageListDTO dto) {

		return JsonResult.ok(adminUnicomApplyBusiness.getPageList(dto));
	}

	@PostMapping("/edit")
	public JsonResult<Object> edit(@Valid @RequestBody AdminUnicomApplyEditDTO dto) {

		adminUnicomApplyBusiness.edit(dto);

		return JsonResult.ok();
	}

	@PostMapping("/getSelectOptions")
	public JsonResult<HashMap<String, List<SelectOptionsVO>>> getSelectOptions()  {

		return JsonResult.ok(adminUnicomApplyBusiness.getSelectOptions());
	}


}
