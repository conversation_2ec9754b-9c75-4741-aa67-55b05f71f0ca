package com.ets.apply.application.common.vo.admin.orderOrder;


import com.baomidou.mybatisplus.annotation.TableId;
import com.ets.apply.application.infra.entity.ReviewOrderEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
public class AdminOrderOrderListVO {

    /**
     * 订单流水号
     */
    @TableId
    private String orderSn;

    /**
     * 审核单号
     */
    private String reviewOrderSn;

    /**
     * 物流单号
     */
    private String logisticOrderSn;

    private String cancelApplySn;

    /**
     * 场景值 1、普通客车 2、出租车 3、储值卡转化 4、老客户转化 5、货车 6、储值卡申办（旧数据迁移用）
     */
    private Integer scene;

    /**
     * 1、etc助手小程序 2、h5
     */
    private Integer clientType;

    /**
     * 下单人uid
     */
    private Long uid;

    /**
     * 下单人昵称
     */
    private String nickname;

    /**
     * 1、待支付 2 、待发货 3、待签收 4、已完成 5、已取消
     */
    private Integer status;

    private String  statusStr;

    private String  logisticStatusStr;
    private String  cancelStatusStr;
    private String  reviewStatusStr;

    /**
     * 卡种 etc_cards.id
     */
    private Integer cardId;

    /**
     * 卡名称（冗余）
     */
    private String cardName;
    private String cardProvince;
    /**
     * 1个人车 2公司车
     */
    private Integer vehicleBelong;

    /**
     * 车牌号（冗余）
     */
    private String plateNo;

    /**
     * 0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 11: 绿色【不用】 12: 红色
     */
    private Integer plateColor;

    /**
     * 邮寄联系人
     */
    private String sendName;

    /**
     * 邮寄联系手机
     */
    private String sendPhone;

    /**
     * 邮寄地区
     */
    private String sendArea;

    /**
     * 邮寄地址
     */
    private String sendAddress;

    /**
     * 总计需支付（元）
     */
    private BigDecimal needPay;

    /**
     * 设备费（元）
     */
    private BigDecimal productFee;

    /**
     * 运费（元）
     */
    private BigDecimal freightFee;

    /**
     * 保证金金额
     */
    private BigDecimal depositFee;

    /**
     * 服务费金额（元）
     */
    private BigDecimal serviceFee;

    /**
     * 通行费保证金（元）
     */
    private BigDecimal tollDepositFee;

    /**
     * 商品套餐金额（元）
     */
    private BigDecimal packageFee;

    /**
     * 商品套餐编码
     */
    private String packageSn;

    /**
     * 我方支付订单流水号（支付成功）
     */
    private String paymentSn;

    /**
     * 退款单号
     */
    private String refundSn;

    /**
     * 没有保证金 1 使用保证金 2 保证金已使用（退款）
     */
    private Integer depositStatus;

    /**
     * 优惠券号
     */
    private String couponCode;

    /**
     * 标识是否用券
     */
    private Integer couponUsed;

    /**
     * 1 微信卡券 2 内部券
     */
    private Integer couponSource;

    /**
     * 代金券id
     */
    private String cashCouponId;

    /**
     * 订单取消原因
     */
    private String cancelMsg;

    /**
     * 是否需要线上发货，0不用发货1需要发货
     */
    private Integer needOnlineDelivery;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付方式（包括免费）1 省份免费 2 申办信用卡免费 3腾讯大王申办 4 绑定银行支付申办 5 ... 具体先看代码
     */
    private Integer purchaseType;

    /**
     * 卡所属银行/机构与类别(不仅仅是信用卡） 2平安银行信用卡 4 交通银行信用卡 5 腾讯大王卡 6招商银行 7... 具体先看代码
     */
    private Integer purchaseParty;

    /**
     * 支付方式确认状态 0 未知状态 1 确定成功 2确定失败
     */
    private Integer purchaseTypeConfirmed;

    /**
     * 申办流程:1 普通2.0订单流程 2 地推2.0流程
     */
    private Integer flowType;

    /**
     * 是否已经签过约
     */
    private Integer hasOnceSigned;

    /**
     * 催货状态：0：初始；10：已催发货；20：无催发货已催快递；21：已催发货已催快递
     */
    private Integer urgeStatus;

    /**
     * 订单来源：1.didi, 2.weiche, 3.kuaigou
     */
    private Integer thirdType;

    /**
     * 推广渠道来源标识
     */
    private String source;

    /**
     * 1 用户在订单状态为收货之前，声称收到了货
     */
    private Integer declareReceived;

    /**
     * 用户声称收到货的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime declareReceivedTime;

    /**
     * 推广人
     */
    private String promoter;

    /**
     * 推广渠道id
     */
    private Integer channelId;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 订单期数[1-默认 2-二期活动支付类型]
     */
    private Integer term;

    /**
     * 召回状态[1-无需召回 2-待召回 3-已召回]
     */
    private Integer recallStatus;

    /**
     * 售后状态:0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
     */
    private Integer aftersaleStatus;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidAt;

    // 套餐名称
    private String packageName;
    // 套餐名称
    private String deviceName;
    //业务归属
    private String bizTypeStr;
    //办理方式
    private String purchaseTypeName;
    //卡号
    private String cardNo;
    //标签
    private String label;
    //激活状态
    private Integer activatedStatus;

    private ReviewOrderEntity reviewOrder;
}
