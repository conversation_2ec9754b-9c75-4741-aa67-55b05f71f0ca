package com.ets.apply.application.common.dto.request.channelProductOrder;

import lombok.Data;

import jakarta.validation.constraints.Min;

@Data
public class ChannelProductOrderListDTO {

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 商品订单号
     */
    private String productOrderSn;


    /**
     * 订单状态 10:初始状态 20:待发货 30:已发货 40:已签收  50:售后中 60：售后完成 90
     */
    private Integer orderStatus;

    /**
     * 产品包sn
     */
    private String packageSn;

    /**
     * 支付时间开始
     */
    private String paidTimeBegin;

    /**
     * 渠道订单号
     */
    private Long channelBindId;
    private Long channelId = 0L;

    /**
     * 支付时间结束
     */
    private String paidTimeEnd;

    private String createTimeBegin;

    private String createTimeEnd;

    /**
     * 交易单号
     */
    private String transactionId;

}
