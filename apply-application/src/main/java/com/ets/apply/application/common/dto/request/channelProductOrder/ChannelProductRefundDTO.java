package com.ets.apply.application.common.dto.request.channelProductOrder;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

@Data
public class ChannelProductRefundDTO {



    /**
     * 商品订单号
     */
    @NotBlank(message = "商品订单号不能为空")
    private String productOrderSn;

    /**
     * 退款原因
     */
    private String reason = "";

    /**
     * 操作人
     */
    private String operator = "";

}
