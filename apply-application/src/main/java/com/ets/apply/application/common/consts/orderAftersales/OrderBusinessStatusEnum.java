package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderBusinessStatusEnum {
    STATUS_PROCESSING(0, "未完成"),
    STATUS_FINISHED(1, "已完成");

    private final Integer status;
    private final String desc;


    public static OrderBusinessStatusEnum getByStatus(int status) {
        for (OrderBusinessStatusEnum node : OrderBusinessStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node;
            }
        }
        return null;
    }
}
