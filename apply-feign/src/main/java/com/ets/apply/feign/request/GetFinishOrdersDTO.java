package com.ets.apply.feign.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

@Data
public class GetFinishOrdersDTO {

    @Positive(message = "uid不能为空")
    private Long uid;

    /**
     * 渠道值
     *     const SOURCE_JD   = 1260;//京东
     *     const SOURCE_BDH5 = 1256;//百度H5
     *     const SOURCE_BD   = 1257;//百度小程序
     *     const SOURCE_TIKTOK = 1258;//抖音
     *     const SOURCE_XHS   = 1259;//小红书
     *     const SOURCE_TAOBAO = 1262;//淘宝商城
     *     const SOURCE_PIN_DUO_DUO = 1263;//拼多多
     *     const SOURCE_VASP = 1553; // 中国人寿
     *     const SOURCE_JD_SELF = 1976; // 京东自营渠道
     *     const SOURCE_MARKET = 1700; // 市场渠道
     *     const SOURCE_JS_DOUYIN = 2352; // 江苏抖音直播间
     *     const SOURCE_WECAR = 2396; // 腾讯出行
     */
    private String source;
}
