package com.ets.apply.application.common.vo.productPackageResource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ProductPackageResourceListVO {


    /**
     * 资源配置sn
     */
    private String resourceSn;

    /**
     * 资源名称
     */
    private String resourceName;

   

    private Integer resourceType;

    /**
     * 资源状态[0-初始状态 1-上架 2-下架]
     */
    private Integer resourceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private LocalDateTime updatedAt;

    /**
     * 操作人
     */
    private String operator;



}
