package com.ets.apply.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.common.consts.productPackageTmp.AllowModifyEnum;
import com.ets.apply.application.common.consts.productPackageTmp.ProductPackageTmpStatusEnum;
import com.ets.apply.application.common.consts.productPackageTmp.ReleaseStatusEnum;
import com.ets.apply.application.common.dto.ProductPackageTmp.ProductPackageTmpParticularModifyDTO;
import com.ets.apply.application.infra.entity.PackageChangeLogEntity;
import com.ets.apply.application.infra.entity.ProductPackageTmpEntity;
import com.ets.apply.application.infra.service.PackageChangeLogService;
import com.ets.apply.application.infra.service.ProductPackageTmpService;
import com.ets.common.ToolsHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class ProductPackageTmpBusiness {


    @Autowired
    private ProductPackageTmpService productPackageTmpService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private PackageChangeLogService packageChangeLogService;

    public void copy(String packageSn, String loginCode) {
        // 获取已有的套餐信息
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getOneByColumn(packageSn, ProductPackageTmpEntity::getPackageSn);
        if (ObjectUtil.isNull(productPackageTmpEntity)) {
            ToolsHelper.throwException("产品包信息不存在");
        }
        // 生成新的套餐信息
        ProductPackageTmpEntity newProductPackageTmpEntity = new ProductPackageTmpEntity();

        // 原产品包信息复制到新的实体中
        BeanUtils.copyProperties(productPackageTmpEntity, newProductPackageTmpEntity);
        newProductPackageTmpEntity.setPackageSn(generatePackageSn(productPackageTmpEntity.getIsTruck() == 1));

        newProductPackageTmpEntity.setStatus(ProductPackageTmpStatusEnum.DEFAULT.getValue());
        newProductPackageTmpEntity.setReleaseStatus(ReleaseStatusEnum.WAIT.getValue());
        productPackageTmpEntity.setExpireDate(LocalDateTime.of(2037, 12, 30, 23, 59));
        newProductPackageTmpEntity.setCreatedAt(LocalDateTime.now());
        newProductPackageTmpEntity.setUpdatedAt(LocalDateTime.now());
        // 获取当前时间 格式HHmmss
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddHHmmss");
        String formattedNow = now.format(formatter);
        String packageName = productPackageTmpEntity.getPackageName();
        if (packageName.length() > 30) {
            packageName = packageName.substring(0, 30);
        }
        newProductPackageTmpEntity.setPackageName(packageName + "-副本" + formattedNow);
        newProductPackageTmpEntity.setAllowModify(AllowModifyEnum.ALLOW_MODIFY_ENUM.getValue());
        productPackageTmpService.create(newProductPackageTmpEntity);

        // 记录操作日志
        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(newProductPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore("");
        packageChangeLogEntity.setChangeAfter("");
        packageChangeLogEntity.setRemark("复制产品包，原产品包编号：" + productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setOperater(loginCode);
        packageChangeLogService.create(packageChangeLogEntity);
    }

    public String generatePackageSn(Boolean isTruck){
        String newGenNumSn = ToolsHelper.genNum(redisPermanentTemplate, "newPackageSn", "prod", 8);
        return isTruck ?"Truck"+newGenNumSn:"CarPackage"+newGenNumSn;
    }

}
