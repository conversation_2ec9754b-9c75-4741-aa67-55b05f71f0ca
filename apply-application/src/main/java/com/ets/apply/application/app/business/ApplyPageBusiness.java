package com.ets.apply.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.consts.applyPage.ApplyPageStatusEnum;
import com.ets.apply.application.common.consts.applyPage.ApplyPageTypeEnum;
import com.ets.apply.application.common.consts.applyPageLog.ApplyPageLogTypeEnum;
import com.ets.apply.application.common.consts.cache.CacheKeyEnum;
import com.ets.apply.application.common.dto.request.applyPage.*;
import com.ets.apply.application.common.vo.applyPage.ApplyPageVO;
import com.ets.apply.application.infra.entity.ApplyPageEntity;
import com.ets.apply.application.infra.entity.ApplyPageLogEntity;
import com.ets.apply.application.infra.service.ApplyPageLogService;
import com.ets.apply.application.infra.service.ApplyPageService;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ApplyPageBusiness extends BaseBusiness {


    @Autowired
    private ApplyPageService applyPageService;


    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ApplyPageLogService applyPageLogService;

    public IPage<ApplyPageVO> getList(ApplyPageListDTO pageListDTO) {
        IPage<ApplyPageEntity> oPage = new Page<>(pageListDTO.getPageNum(), pageListDTO.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ApplyPageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(pageListDTO.getPageSn()), ApplyPageEntity::getPageSn, pageListDTO.getPageSn())
                .like(StringUtils.isNotBlank(pageListDTO.getPageName()), ApplyPageEntity::getPageName, pageListDTO.getPageName())
                .eq(ObjectUtil.isNotNull(pageListDTO.getPageType()), ApplyPageEntity::getPageType, pageListDTO.getPageType())
                .eq(ObjectUtil.isNotNull(pageListDTO.getPageCategory()), ApplyPageEntity::getPageCategory, pageListDTO.getPageCategory())
                .orderByDesc(ApplyPageEntity::getPageType)
                .orderByDesc(ApplyPageEntity::getCreatedAt)
                .orderByAsc(ApplyPageEntity::getPageStatus)
        ;
        IPage<ApplyPageEntity> pageList = applyPageService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
                    ApplyPageVO pageListVO = new ApplyPageVO();

                    BeanUtils.copyProperties(record, pageListVO);
                    return pageListVO;
                }
        );
    }


    public void create(ApplyPageCreateDTO pageCreatDTO) {
        ApplyPageEntity applyPageEntity = new ApplyPageEntity();
        applyPageEntity.setPageSn(ToolsHelper.genNum(redisPermanentTemplate, "applyPageSn", appConfig.getEnv(), 8));
        applyPageEntity.setPageName(pageCreatDTO.getPageName());

        applyPageEntity.setContent(JSONObject.toJSONString(pageCreatDTO.getContent()));
        if (ObjectUtil.isNotNull(pageCreatDTO.getTemplateSn())) {
            applyPageEntity.setTemplateSn(pageCreatDTO.getTemplateSn());
        }
        applyPageEntity.setRemark(pageCreatDTO.getRemark());
        applyPageEntity.setPageType(ApplyPageTypeEnum.NORMAL.getType());
        applyPageEntity.setPageStatus(ApplyPageStatusEnum.LISTING.getStatus());
        applyPageEntity.setPageCategory(pageCreatDTO.getPageCategory());
        applyPageEntity.setOperator(RequestHelper.getAdminOperator());

        applyPageService.create(applyPageEntity);

        // 记录日志
        createLog(
                ApplyPageLogTypeEnum.CREATED.getCode(),
                applyPageEntity,
                applyPageEntity.getPageStatus(),
                "创建",
                RequestHelper.getAdminOperator()
        );
    }

    public void modify(ApplyPageModifyDTO modifyDTO) {

        ApplyPageEntity applyPageEntity = applyPageService.getOneByColumn(modifyDTO.getPageSn(), ApplyPageEntity::getPageSn);
        if (ObjectUtil.isNull(applyPageEntity)) {
            ToolsHelper.throwException("未找到需要修改的页面配置，请确认后重新提交");
        }
        LambdaUpdateWrapper<ApplyPageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ApplyPageEntity::getPageSn, modifyDTO.getPageSn())
                .set(ApplyPageEntity::getContent, JSONObject.toJSONString(modifyDTO.getContent()))
                .set(ApplyPageEntity::getPageName, (modifyDTO.getPageName()))
                .set(ApplyPageEntity::getRemark, modifyDTO.getRemark())
                .set(ObjectUtil.isNotNull(modifyDTO.getTemplateSn()),ApplyPageEntity::getTemplateSn,modifyDTO.getTemplateSn())
                .set(ApplyPageEntity::getOperator, RequestHelper.getAdminOperator())

        ;
        applyPageService.updateByWrapper(wrapper);
        // 修改时删除缓存
        redisTemplate.delete(CacheKeyEnum.APPLY_PAGE_CACHE_KEY_PREFIX.getCode() + modifyDTO.getPageSn());
        // 记录日志
        createLog(
                ApplyPageLogTypeEnum.MODIFY.getCode(),
                applyPageEntity,
                applyPageEntity.getPageStatus(),
                "修改配置信息:" + JSONObject.toJSONString(modifyDTO),
                RequestHelper.getAdminOperator()
        );
    }

    public void setStatus(ApplyPageSetStatusDTO setStatusDTO) {
        ApplyPageEntity applyPageEntity = applyPageService.getOneByColumn(setStatusDTO.getPageSn(), ApplyPageEntity::getPageSn);
        if (ObjectUtil.isNull(applyPageEntity)) {
            ToolsHelper.throwException("未找到需要修改的页面配置，请确认后重新提交");
        }

        // 模板页面不能下架
        if (applyPageEntity.getPageType().equals(ApplyPageTypeEnum.TEMPLATE.getType())) {
            ToolsHelper.throwException("模板页面不支持下架");
        }
        if (applyPageEntity.getPageStatus().equals(setStatusDTO.getStatus())) {
            return;
        }
        LambdaUpdateWrapper<ApplyPageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ApplyPageEntity::getPageSn, setStatusDTO.getPageSn())
                .set(ApplyPageEntity::getPageStatus, setStatusDTO.getStatus())
                .set(ApplyPageEntity::getOperator, RequestHelper.getAdminOperator())

        ;
        applyPageService.updateByWrapper(wrapper);

        // 修改时删除缓存
        redisTemplate.delete(CacheKeyEnum.APPLY_PAGE_CACHE_KEY_PREFIX.getCode() + setStatusDTO.getPageSn());
        // 记录日志
        createLog(
                applyPageEntity.getPageStatus().equals(ApplyPageStatusEnum.LISTING.getStatus()) ?
                        ApplyPageLogTypeEnum.LISTING.getCode() : ApplyPageLogTypeEnum.DELIST.getCode(),
                applyPageEntity,
                setStatusDTO.getStatus(),
                ApplyPageStatusEnum.getDescByCode(setStatusDTO.getStatus()),
                RequestHelper.getAdminOperator()
        );
    }

    public ApplyPageVO getDetail(String pageSn) {
        ApplyPageEntity applyPageEntity = applyPageService.getOneByColumn(pageSn, ApplyPageEntity::getPageSn);
        if (ObjectUtil.isNull(applyPageEntity)) {
            ToolsHelper.throwException("未找到需要修改的页面配置，请确认后重新提交");
        }
        ApplyPageVO applyPageVO = new ApplyPageVO();
        BeanUtils.copyProperties(applyPageEntity, applyPageVO);
        applyPageVO.setContent(JSONObject.parseObject(applyPageEntity.getContent()));
        return applyPageVO;

    }


    /**
     * 设置兜底模板
     *
     * @param setPageTypeDTO
     */
    public void setPageType(ApplyPageSetPageTypeDTO setPageTypeDTO) {
        ApplyPageEntity applyPageEntity = applyPageService.getOneByColumn(setPageTypeDTO.getPageSn(), ApplyPageEntity::getPageSn);
        if (ObjectUtil.isNull(applyPageEntity)) {
            ToolsHelper.throwException("未找到需要修改的页面配置，请确认后重新提交");
        }
        if (applyPageEntity.getPageType().equals(setPageTypeDTO.getPageType())) {
            return;
        }
        // 设置兜底模板逻辑,已有模板会被顶掉
        if (ApplyPageTypeEnum.TEMPLATE.getType().equals(setPageTypeDTO.getPageType())) {
            ApplyPageEntity templatePage = applyPageService.getTemplatePage(applyPageEntity.getPageCategory());
            if (ObjectUtil.isNotNull(templatePage)) {
                LambdaUpdateWrapper<ApplyPageEntity> templateWrapper = new LambdaUpdateWrapper<>();
                templateWrapper.eq(ApplyPageEntity::getPageSn, templatePage.getPageSn())
                        .set(ApplyPageEntity::getPageType,ApplyPageTypeEnum.NORMAL.getType())
                        .set(ApplyPageEntity::getOperator, RequestHelper.getAdminOperator())
                ;
                applyPageService.updateByWrapper(templateWrapper);

            }
        }
        // 设置兜底模板时，一个页面只能有一个兜底模板，有已有模板时，原兜底模板切换为普通
        applyPageService.setTemplatePage(setPageTypeDTO.getPageSn(), applyPageEntity.getPageCategory(),
                setPageTypeDTO.getPageType(), RequestHelper.getAdminOperator());
        // 修改时删除缓存
        redisTemplate.delete(CacheKeyEnum.APPLY_PAGE_CACHE_KEY_PREFIX.getCode() + setPageTypeDTO.getPageSn());

        // 记录日志
        createLog(
                ApplyPageLogTypeEnum.SET_PAGE_TYPE.getCode(),
                applyPageEntity,
                applyPageEntity.getPageStatus(),
                "设置兜底模板",
                RequestHelper.getAdminOperator()
        );
    }

    /**
     * 获取页面配置信息，如果页面下线，
     *
     * @param pageSn
     * @return
     */
    public ApplyPageVO getByPageSnWithTemplate(String pageSn, Integer pageCategory) {
        ApplyPageVO applyPageVO = new ApplyPageVO();

        try {
            // 有缓存的话获取缓存的值
            String cachePage =
                    redisTemplate.boundValueOps(CacheKeyEnum.APPLY_PAGE_CACHE_KEY_PREFIX.getCode() + pageSn).get();
            if (ObjectUtil.isNotNull(cachePage)) {
                applyPageVO = JSONObject.parseObject(cachePage, ApplyPageVO.class);
                if (ObjectUtil.isEmpty(applyPageVO.getPageSn())) {
                    return null;
                } else {
                    return applyPageVO;
                }
            }

            ApplyPageEntity applyPageEntity = applyPageService.getOneByColumn(pageSn, ApplyPageEntity::getPageSn);

            // 页面配置已上架时返回对应页面信息，否则返回兜底模板信息
            if (ObjectUtil.isNotNull(applyPageEntity) && ApplyPageStatusEnum.LISTING.getStatus().equals(applyPageEntity.getPageStatus())) {
                BeanUtils.copyProperties(applyPageEntity, applyPageVO);
                applyPageVO.setContent(JSONObject.parseObject(applyPageEntity.getContent()));
                // 设置缓存
                redisTemplate.boundValueOps(CacheKeyEnum.APPLY_PAGE_CACHE_KEY_PREFIX.getCode() + pageSn).set(JSONObject.toJSONString(applyPageVO), 10, TimeUnit.MINUTES);

            } else {
                if (ObjectUtil.isNotEmpty(applyPageEntity)) {
                    pageCategory = applyPageEntity.getPageCategory();
                }
                if (ObjectUtil.isNotEmpty(pageCategory)) {
                    ApplyPageEntity templatePage = applyPageService.getTemplatePage(pageCategory);
                    if (ObjectUtil.isNotNull(templatePage)) {
                        BeanUtils.copyProperties(templatePage, applyPageVO);
                        applyPageVO.setContent(JSONObject.parseObject(templatePage.getContent()));
                    }
                }
            }


        } catch (Exception e) {
            log.error("getByPageSnWithTemplate 处理异常" + e.getMessage());
            return null;
        }
        if (ObjectUtil.isEmpty(applyPageVO.getPageSn())) {
            return null;
        }
        return applyPageVO;

    }

    // 创建日志记录
    public void createLog(Integer logType, ApplyPageEntity entity, Integer afterStatus, String content, String operator) {

        try {
            ApplyPageLogEntity logEntity = new ApplyPageLogEntity();
            logEntity.setOperateUser(operator);
            logEntity.setPageSn(entity.getPageSn());
            logEntity.setLogType(logType);
            logEntity.setContent(content);
            logEntity.setAfterStatus(afterStatus);
            logEntity.setPreStatus(entity.getPageStatus());
            applyPageLogService.create(logEntity);
        } catch (Exception e) {
            log.info("createLog applyPageLog 异常" + e.getMessage());
        }

    }
}
