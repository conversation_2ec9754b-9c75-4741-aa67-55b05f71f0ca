package com.ets.apply.application.app.thirdservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class IpAddressInfoVO {
    private Integer id;

    private String ip;

    private String country;

    private String province;

    private String city;

    private String districts;

    private String provinceCode;

    private String cityCode;

    private String districtCode;

    private String originData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

}
