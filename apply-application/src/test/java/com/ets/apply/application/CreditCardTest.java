package com.ets.apply.application;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.creditCard.CmbcBusiness;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.business.creditCard.CreditCardRewardBusiness;
import com.ets.apply.application.common.bo.creditCard.CreditCardCompensateBO;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardBO;
import com.ets.apply.application.app.business.creditCard.CiticBusiness;
import com.ets.apply.application.common.dto.request.citic.CiticOrderReceiveDTO;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCallBackDTO;
import com.ets.apply.application.common.utils.SM2Utils;
import com.ets.apply.application.common.utils.bank.spd.EncryptTools;
import com.ets.apply.application.common.utils.bank.spd.EncryptUtil;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.common.BizException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Base64Utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class CreditCardTest {

    @Autowired
    CiticBusiness citicBusiness;
    @Autowired
    CreditCardRewardBusiness creditCardRewardBusiness;

    @Autowired
    CreditCardBusiness creditCardBusiness;

    @Autowired
    CmbcBusiness cmbcBusiness;

    @Test
    public void receiveData(){
        CiticOrderReceiveDTO citicOrderReceiveDTO = new CiticOrderReceiveDTO();
        citicOrderReceiveDTO.setData("{\\\"applyStatus\\\":\\\"20\\\",\\\"applyTime\\\":\\\"2022-03-22 19:47:03\\\",\\\"newCustFlag\\\":\\\"0\\\",\\\"paId\\\":\\\"2208241116000000081\\\",\\\"pushTime\\\":\\\"2022-08-10 14:44:45\\\",\\\"firstPurchaseTime\\\":\\\"2022-08-10 14:44:45\\\"}");
        citicBusiness.receiveOrder(citicOrderReceiveDTO);
    }



    /**
     * 测试信用卡奖励奖励任务
     * @throws BizException
     */
    @Test
    public void reward() throws BizException{
        String startTime = "2022-11-01 00:00:00";
        String endTime = "2023-01-30 00:00:00";
        CreditCardRewardBO creditCardRewardBO = new CreditCardRewardBO();
        creditCardRewardBO.setEndTime(endTime);
        creditCardRewardBO.setStartTime(startTime);
        List<Integer> whichBank = new ArrayList<>();
        whichBank.add(31);
        whichBank.add(32);
        whichBank.add(30);
        creditCardRewardBO.setWhichBankList(whichBank);
        List<Integer> classify = new ArrayList<>();
        classify.add(3);
        classify.add(5);
        creditCardRewardBO.setClassify(classify);
        creditCardRewardBusiness.reward(creditCardRewardBO);
    }


    @Test
    public void testCmbcSign(){
        log.info(cmbcBusiness.sign("BMhUm74TU9cvcjWVUCXkm8Bg7MGhOFAV0tBaVPZgrr2gnkfAhq07W1XXJ29WSn63yyxRVnxMLoZuGDhcN3G4Lz%2FLzvnTsiurGYJN0KWnc94u1Mb0qsahxN2F8Jjbmb%2FF3dVhMlskw1%2FOjWMyvQJQaEtzfcQquvFqUSiUmK7DXiXCkf9Jfo4NnHpbW9uPghIjmOb6J2EEhO1rPOkumUWt4LOqsV%2BMrAgPqBPs5qkiZTl7GPnbvDZEuAj9Th4bz5hKiNIc8yU9oJsFMfRKFpAv7tIICNu3vkkZa27Cml6FvevTsqP88HQ03CTaIDjsUkVP7IMRQ3kx3w%3D%3D"));
    }

    @Test
    public void testCmbcEncrypt(){
        log.info(cmbcBusiness.encrypt("{\"version\":\"2.0\",\"apply_info\":{\"partner_pin\":\"**********\",\"prod_id\":\"137\"},\"request_id\":\"22102417090000000000000000000041\",\"source_code\":\"CJ-BJGDCS1\"}"));
    }

    @Test
    public void testCmbcEncryptLocal(){
        String encrypt1  = cmbcBusiness.encryptLocal("{\"applyNo\":\"123456\",\"userType\":\"1\",\"approveStatus\":\"A\",\"finalApproveStatus\":\"Z0\",\"finalApproveTime\":\"20200601\",\"activationTime\":\"20190716\",\"firstSwipeCardTime\":\"20190716\",\"partnerPin\":\"**********\"}");
        log.info(encrypt1);
//        cmbcBusiness.sign(encrypt1);
        log.info(cmbcBusiness.sign(encrypt1));
    }

    @Test
    public void testCmbcDecrypt() throws UnsupportedEncodingException {

        String encryptStr1 = this.encryptLocal("{\"version\":\"2.0\",\"apply_info\":{\"partner_pin\":\"**********\",\"prod_id\":\"137\"},\"request_id\":\"22102417090000000000000000000041\",\"source_code\":\"CJ-BJGD1\"}");
        log.info("加密："+encryptStr1);
//        encryptStr1 =  "BNA52KumLZg8rI%2F9M5MATCokG0BDf2Hz4hI5EeQmWiAxQO4btS7w%2BdWq8qcgiO6lcr3YJ1ymFanlhEc7tn6a6jivemT2QjS8aSv%2BzpA978hVFDFKbrAnjGX6VnlFi%2BPrxUL2rlevzSCQ0Sk0sNxDVSh6qXSiFWbt5TF3IkQeP591%2BgXjjcupnkf5XXS34pykGFaWtjhe5HqhX6A3hgqDMFFvySwvZmKSknEgFiDpqe%2FrcF7NdtS7n0iWToA8%2FK%2B%2Fj0AyjX1rNSDXxgYcLc5MWt%2FWdbzaLPOdAJKIM67V0kE%2BqPoe0Lk6Bispt1pF3di1MroQlpWLjlAGXGrxnSwFvTvH6YBNxAX8GYyC0DbFG%2FGQNK2V54Am%2FnPS%2B03Y44EInw%3D%3D";
        log.info(this.decryptLocal(encryptStr1));
    }


    public String encryptLocal(String jsonStr) {
        log.info("民生银行：原始参数" + jsonStr);

        String key = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEvTII5QcUttiLw/02t1gO0iuZFAAZS3NZMb956JtHOiiMFytCfRCkaiREDybR1VuXTey7ZtCgUWSY5iE5F9pViw==";
        try {
            String ciphertext = Base64Utils.encodeToString(SM2Utils.encrypt(jsonStr.getBytes(StandardCharsets.UTF_8), Base64.decode(key)));
            return URLEncoder.encode(ciphertext, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String decryptLocal(String data) {
        String encryptKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgSIVTXfll2qJ1vJyFk121WNq5zX/WH9HtVnwf5eNvQ3igCgYIKoEcz1UBgi2hRANCAAS9MgjlBxS22IvD/Ta3WA7SK5kUABlLc1kxv3nom0c6KIwXK0J9EKRqJEQPJtHVW5dN7Ltm0KBRZJjmITkX2lWL";
        try {
            data = URLDecoder.decode(data, "UTF-8");
            //解密
            String decryptStr =  new String(SM2Utils.decrypt(Base64Utils.decode(data.getBytes()), Base64.decode(encryptKey.getBytes())), StandardCharsets.UTF_8);
            log.info("民生银行解密结果："+decryptStr);
            return decryptStr;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 测试民生银行申请信用卡
     */
    @Test
    public void  testApplyOrder(){

        // {"plate_no":"领ZEST75","which_bank":"161","classify":"2","user_name":"","phone_number":"","uid":**********}
        ApplyOrderDTO applyOrderDTO = new ApplyOrderDTO();
        applyOrderDTO.setClassify(2);
        applyOrderDTO.setPlateNo("领ZEST75");
        applyOrderDTO.setUid(**********L);
        applyOrderDTO.setUsername("");
        applyOrderDTO.setWhichBank(161);
        applyOrderDTO.setPhoneNumber("");
        CreditCardApplyOrderVO creditCardApplyOrderVO = creditCardBusiness.applyOrder(applyOrderDTO);
        log.info(creditCardApplyOrderVO.getUrl());
    }


    @Test
    public void testCmbcCallback() throws UnsupportedEncodingException {
        String str= "BDsNNsklnNNVVF4khPCvCw0v7LFEBUmXeWqH8pmp6Lh5M3EWHhv%2BZsQi9dnW61DdNJ5O%2BpsjbHFiwZ0%2Bj%2BbJePITD7gS2r3MgOLdDSTM7kpPFMFYtiJTfwVlM%2B4CgFcIb2n1%2Fcaen0WHcgSQ286g2WlFadLpBmYPTDTisPtScara4DWQZNJ2aHo5xeL3hxJqunUPtQUQMTGYJsjmZP3174qR4%2BJvV6ZWzkTtQEP94j4SZham5NEZGehFD3038rZv56b4sdrWuV4pyiV%2Fl5OaXn1vG8ky1KUdhcpMl3K2qh17xU5MJHqrbV5UNXImcJqNocUAEOzncVCcEjy7XyS6A96Rk%2BjOU16CM1qY";
        CreditCardCallBackDTO dto = new CreditCardCallBackDTO();
        dto.setData(str);
        dto.setSign("629cbe4c5b6a5dd1156d8ad7ca63fbd2891c13957243d76a7ab51e286aa61cc0");
        dto.setWhichBank(161);
        creditCardBusiness.callback(dto);

    }

    @Test
    public void sm2Test() {

        String text = "test";

        //使用随机生成的密钥对加密或解密
        System.out.println("使用随机生成的密钥对加密或解密====开始");
        SM2 sm2 = SmUtil.sm2();
        // 公钥加密
        String encryptStr = sm2.encryptBcd(text, KeyType.PublicKey);
        System.out.println("公钥加密：" + encryptStr);
        //私钥解密
        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
        System.out.println("私钥解密：" + decryptStr);
        System.out.println("使用随机生成的密钥对加密或解密====结束");

        //使用自定义密钥对加密或解密
        System.out.println("使用自定义密钥对加密或解密====开始");

        KeyPair pair = SecureUtil.generateKeyPair("SM2");
        byte[] privateKey = pair.getPrivate().getEncoded();
        byte[] publicKey = pair.getPublic().getEncoded();

        SM2 sm22 = SmUtil.sm2(privateKey, publicKey);
        // 公钥加密
        String encryptStr2 = sm22.encryptBcd(text, KeyType.PublicKey);
        System.out.println("公钥加密：" + encryptStr2);
        //私钥解密
        String decryptStr2 = StrUtil.utf8Str(sm22.decryptFromBcd(encryptStr2, KeyType.PrivateKey));
        System.out.println("私钥解密：" + decryptStr2);
        System.out.println("使用自定义密钥对加密或解密====结束");

    }

    @Test
    public void receive() throws ParseException {
        cmbcBusiness.receiveOrder("{\"data\":\"BDsNNsklnNNVVF4khPCvCw0v7LFEBUmXeWqH8pmp6Lh5M3EWHhv%2BZsQi9dnW61DdNJ5O%2BpsjbHFiwZ0%2Bj%2BbJePITD7gS2r3MgOLdDSTM7kpPFMFYtiJTfwVlM%2B4CgFcIb2n1%2Fcaen0WHcgSQ286g2WlFadLpBmYPTDTisPtScara4DWQZNJ2aHo5xeL3hxJqunUPtQUQMTGYJsjmZP3174qR4%2BJvV6ZWzkTtQEP94j4SZham5NEZGehFD3038rZv56b4sdrWuV4pyiV%2Fl5OaXn1vG8ky1KUdhcpMl3K2qh17xU5MJHqrbV5UNXImcJqNocUAEOzncVCcEjy7XyS6A96Rk%2BjOU16CM1qY\",\"sign\":\"629cbe4c5b6a5dd1156d8ad7ca63fbd2891c13957243d76a7ab51e286aa61cc0\"}");
    }


    /**
     * 测试中信银行激活补偿任务
     */
    @Test
    public void testCiticActivateCompensation(){

//        String startTime = "2022-09-25 10:00:00";
//        String endTime = "2022-11-25 17:00:00";

        String params ="120";
        int checkMinutes = Integer.parseInt(params);
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.MINUTE, -checkMinutes);// 5分钟之前的时间
        Date startTimeD = beforeTime.getTime();
        String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTimeD);
        String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        citicBusiness.activateCompensation(startTime,endTime);
    }

    /**
     * 测试浦发申请验签
     * @throws Exception
     */
    @Test
    public void testSpdApply() throws Exception {

        String example = "{\"mobilephone\":\"***********\",\"backurl\":\"http://www.baidu.com\",\"linkNum\":\"A2204130953071985943\",\"userId\":\"DCCPFYH20220415104406325717128\"}";

        String sm4Key = "JQPRRfK4Xt0ykFZY";
        String sm2Key = "D4nyHHPea4gGPawUASZ82I+mLouuMF/kgiU3oRhjM9o=";



        //SM2浦发侧公钥pufaPublickey：1Lk+Yd32ouEXPBv9Eq3cJHqcfA923a8N1NCYHofA5evYK8UiIFlO0INhY7EkARcPtUTgVyGAT0KPYvOm2p5uIw==
        String sm2PublicKey = "1Lk+Yd32ouEXPBv9Eq3cJHqcfA923a8N1NCYHofA5evYK8UiIFlO0INhY7EkARcPtUTgVyGAT0KPYvOm2p5uIw==";


        String encryptData = EncryptTools.encrypt(example, sm4Key);
        log.info("浦发SM4 加密后data:"+encryptData);

        String urlData = URLEncoder.encode(encryptData, "UTF-8");
        log.info("浦发SM4 URLEncoder加密后data:"+urlData);

        // encryptData 进行sm2 加签
        String encryptSign = EncryptTools.sign(encryptData, sm2Key);
        log.info("浦发SM2 加密后sign:"+encryptSign);

        String sign =URLEncoder.encode(encryptSign, "UTF-8");
        log.info("浦发SM2 URLEncoder加密后sign:"+sign);


        // 解密 1、url decode 2、sm4 解密  3、验签
        // h
        String  urlDecodeData = URLDecoder.decode(urlData, "UTF-8");
        String decrypeData = EncryptTools.decrypt(urlDecodeData,sm4Key);
        log.info("浦发SM2 URLEndoder解密后decrypeData :"+decrypeData);

        // 验签
       Boolean result =  EncryptTools.validate(urlDecodeData,encryptSign,sm2PublicKey);
        log.info("浦发SM2 验签结果 :"+result);
    }


    @Test
    public void spdGenerateKey(){
        EncryptUtil.SM2KeyPair pair =   EncryptUtil.SM2Util.generateKeyPair();
        log.info("浦发SM2 生成私钥 :"+pair.getBase64PriKey());
        log.info("浦发SM2 生成公钥 :"+pair.getBase64PubKey());

    }

    @Test
    public void activateFieldsCompensation() {
        String params = "{\"firstUseStartTime\":\"2023-01-01 00:00:00\",\"firstUseEndTime\":\"2023-10-01 00:00:00\",\"activateStartTime\":\"\",\"activateEndTime\":\"\",\"auditStartTime\":\"\",\"auditEndTime\":\"\"}";
        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getFirstUseStartTime()) || ObjectUtil.isNull(compensateBO.getFirstUseEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setFirstUseStartTime(beginDate);
            compensateBO.setFirstUseEndTime(endDate);
        }
        creditCardBusiness.activateFieldsCompensation(compensateBO.getFirstUseStartTime(), compensateBO.getFirstUseEndTime());

    }

    /**
     * 已激活，审核数据缺失数据进行补偿操作
     *
     */
    @Test
    public void auditFieldsCompensation() {
        String params = "{\"activateStartTime\":\"2023-01-01 00:00:00\",\"activateEndTime\":\"2024-12-01 00:00:00\",\"auditStartTime\":\"\",\"auditEndTime\":\"\"}";

        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getActivateStartTime()) || ObjectUtil.isNull(compensateBO.getActivateEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setActivateStartTime(beginDate);
            compensateBO.setActivateEndTime(endDate);
        }
        creditCardBusiness.auditFieldsCompensation(compensateBO.getActivateStartTime(), compensateBO.getActivateEndTime());
    }

    /**
     * 审核通过进件数据补偿
     */
    @Test
    public void submitFieldsCompensation() {
        String params = "{\"firstUseStartTime\":\"2023-01-01 00:00:00\",\"firstUseEndTime\":\"2023-10-01 00:00:00\",\"activateStartTime\":\"2023-01-01 00:00:00\",\"activateEndTime\":\"2023-10-01 00:00:00\",\"auditStartTime\":\"2023-01-01 00:00:00\",\"auditEndTime\":\"2023-10-01 00:00:00\"}";
        CreditCardCompensateBO compensateBO = JSONObject.parseObject(params, CreditCardCompensateBO.class);
        // 如果没有传计算激活信息的开始或者结束时间，默认时间是前一天的数据
        if (ObjectUtil.isNull(compensateBO.getAuditStartTime()) || ObjectUtil.isNull(compensateBO.getAuditEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(24);
            LocalDateTime endDate = LocalDateTime.now().minusHours(1);
            compensateBO.setAuditEndTime(beginDate);
            compensateBO.setAuditEndTime(endDate);
        }
        creditCardBusiness.submitFieldsCompensation(compensateBO.getAuditStartTime(), compensateBO.getAuditEndTime());
    }

    @Test
    public void testNull(){
        // 如果空
        LocalDateTime testTime = null;
        Optional.ofNullable(testTime).ifPresent(t->{
            t.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            System.out.println(t);

        });

        LocalDateTime testTime2 = LocalDateTime.now();
        Optional.ofNullable(testTime2).ifPresent(t->{
            System.out.println(t.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toString());

        });
    }
}
