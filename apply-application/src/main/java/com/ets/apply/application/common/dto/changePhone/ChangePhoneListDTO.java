package com.ets.apply.application.common.dto.changePhone;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class ChangePhoneListDTO {
    private String plateNo;
    private Long uid;
    private Integer status;

    @Min(value = 1, message = "当前页码必须大于0")
    private Integer pageNum = 1;

    @Min(value = 1, message = "每页条数必须大于0")
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
