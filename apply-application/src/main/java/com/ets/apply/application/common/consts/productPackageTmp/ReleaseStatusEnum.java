package com.ets.apply.application.common.consts.productPackageTmp;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReleaseStatusEnum {
    WAIT(1, "待发布"),
    PROD(2, "已发布");

    private final Integer value;
    private final String desc;

    public static String getDescByCode(int code) {
        for (ReleaseStatusEnum node : ReleaseStatusEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
