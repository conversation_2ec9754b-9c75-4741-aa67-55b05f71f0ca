package com.ets.apply.application.app.business.creditCard;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.disposer.CreditCardActivateNotifyDisposer;
import com.ets.apply.application.app.service.bank.CommV2Service;
import com.ets.apply.application.common.bo.creditCard.CreditCardLogBO;
import com.ets.apply.application.common.bo.creditCard.CreditCardProgressBO;
import com.ets.apply.application.common.config.creditBank.CommCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.*;
import com.ets.apply.application.common.consts.activityCreditCardOperateLog.CreditCardOperateLogTypeEnum;
import com.ets.apply.application.common.consts.activityCreditCardOriginalLog.CreditCardOriginalDataType;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.common.consts.comm.ApplyStatusEnum;
import com.ets.apply.application.common.consts.comm.ApprovalResult;
import com.ets.apply.application.common.dto.bank.CreditCardActivateNotifyDTO;
import com.ets.apply.application.common.dto.request.bank.comm.ResultNotifyDto;
import com.ets.apply.application.common.dto.request.bank.comm.SubmitResultNotifyDTO;
import com.ets.apply.application.common.dto.request.bank.comm.SubmitResultNotifyDataDTO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardLogService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.ToolsHelper;
import com.ets.starter.queue.QueueDefault;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class CommBusiness extends CreditCardBaseBusiness {

    @Autowired
    private CommCreditBankConfig commCreditBankConfig;

    @Autowired
    private CommV2Service commV2Service;


    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;

    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    @Autowired
    private CreditCardUsersInfoBusiness creditCardUsersInfoBusiness;
    @Autowired
    private ActivityCreditCardLogService creditCardLogService;

    @Autowired
    private QueueDefault queueDefault;

    public Map<String, Object> receiveOrder(ResultNotifyDto resultNotifyDto) {

        try{
            // 记录原始数据
            this.saveOriginalDataLog(
                    resultNotifyDto.getSerialNum(),
                    JSON.toJSONString(resultNotifyDto),
                    ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode(),
                    CreditCardOriginalDataType.TYPE_RECEIVE.getCode(),
                    Thread.currentThread().getStackTrace()[1].getMethodName()
            );

            ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity =
                    activityCreditCardUsersInfoService.getOneByColumn(resultNotifyDto.getSerialNum(),
                            ActivityCreditCardUsersInfoEntity::getOrderSn);
            if (activityCreditCardUsersInfoEntity == null) {
                ToolsHelper.throwException("记录不存在");
            }

            ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity =
                    activityCreditCardBankUsersService.getOneByColumn(resultNotifyDto.getSerialNum(),
                            ActivityCreditCardBankUsersEntity::getApplyNumber);
            if (activityCreditCardBankUsersEntity == null) {
                ToolsHelper.throwException("记录不存在");
            }
            // 有激活标志的，标记激活
            if (ObjectUtil.isNotNull(resultNotifyDto.getActiveTime())) {
                this.activate(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, resultNotifyDto);
            } else if (ApprovalResult.APPROVED.getStatus().equals(resultNotifyDto.getApprovalResult())) {
                // 无激活标志，处理审核逻辑
                this.auditPass(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, resultNotifyDto);
            } else if (ApprovalResult.REJECTED.getStatus().equals(resultNotifyDto.getApprovalResult())) {
                this.auditRefuse(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, resultNotifyDto);
            }

            return this.formatResponse("000000", "成功");
        }catch (Exception e){
            log.error("交通银行receiveOrder接口异常", e);
            return this.formatResponse("999999", "处理失败");
        }

    }

    public CreditCardProgressBO resultNotify(SubmitResultNotifyDTO resultNotifyDTO) throws UnsupportedEncodingException {

        // 参数解密
        String decryptData = commV2Service.decryptAndVerifyData(resultNotifyDTO.getData(), resultNotifyDTO.getSign());
        String urlDecodeString = URLDecoder.decode(decryptData, "UTF-8");
        log.info("交通银行resultNotify结果通知解密后数据：{}", urlDecodeString);

        SubmitResultNotifyDataDTO resultNotifyDataDTO = JSONObject.parseObject(urlDecodeString,
                SubmitResultNotifyDataDTO.class);
        log.info("交通银行resultNotify结果通知解密后数据：{}", JSON.toJSONString(resultNotifyDataDTO));
        // 入库记录
        if (StringUtils.isEmpty(resultNotifyDataDTO.getSerialNum())) {
            ToolsHelper.throwException("合作方流水号缺失，请确认");
        }
        // 记录原始数据
        this.saveOriginalDataLog(
                resultNotifyDataDTO.getSerialNum(),
                urlDecodeString,
                ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode(),
                CreditCardOriginalDataType.TYPE_RECEIVE.getCode(),
                Thread.currentThread().getStackTrace()[1].getMethodName()
        );
        // 获取信用卡申请记录
        ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity =
                activityCreditCardUsersInfoService.getOneByColumn(resultNotifyDataDTO.getSerialNum(),
                        ActivityCreditCardUsersInfoEntity::getOrderSn);
        if (activityCreditCardUsersInfoEntity == null) {
            ToolsHelper.throwException("记录不存在");
        }

        ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity =
                activityCreditCardBankUsersService.getOneByColumn(resultNotifyDataDTO.getSerialNum(),
                        ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (activityCreditCardBankUsersEntity == null) {
            ToolsHelper.throwException("记录不存在");
        }
        // 处理数据
        this.handleResultNotify(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity,
                resultNotifyDataDTO);
        CreditCardProgressBO progressBO = new CreditCardProgressBO();
        progressBO.setClassify(activityCreditCardUsersInfoEntity.getClassify());
        return progressBO;
    }

    public void handleResultNotify(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                                   ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                                   SubmitResultNotifyDataDTO notifyDataDTO) {
        ApplyStatusEnum applyStatusEnum = ApplyStatusEnum.getApplyStatusEnumByStatus(notifyDataDTO.getApplyStatus());

        switch (Objects.requireNonNull(applyStatusEnum)) {

            case APPLY_STATUS_ONE:
                this.applySubmitSuccess(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity,
                        notifyDataDTO);
                break;

            case APPLY_STATUS_ZERO:
            case APPLY_STATUS_THREE:
            case APPLY_STATUS_FOUR:
            case APPLY_STATUS_FIVE:
                this.applySubmitFail(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity,
                        notifyDataDTO);
                break;

            case APPLY_STATUS_TWO:
            default:
                break;

        }
    }


    private void applySubmitSuccess(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                                    ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                                    SubmitResultNotifyDataDTO receiveDto) {
        // 重复通知状态未变化不进行修改
        if (ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode() == activityCreditCardBankUsersEntity.getApplyCompleted()) {
            log.info(activityCreditCardUsersInfoEntity.getOrderSn() + "申请单已经进件，不能重复进件");
            return;
        }
        creditCardUsersInfoBusiness.applySubmit(activityCreditCardUsersInfoEntity);
        //时间时间戳转localDateTime
        LocalDateTime requestTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(receiveDto.getRequestTime())),
                        ZoneId.systemDefault());

        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> activityCreditCardBankUsersEntityLambdaUpdateWrapper =
                new LambdaUpdateWrapper<>();
        activityCreditCardBankUsersEntityLambdaUpdateWrapper
                .eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getSubmitTime, requestTime)
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted,
                        ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode());
        activityCreditCardBankUsersService.updateByWrapper(activityCreditCardBankUsersEntityLambdaUpdateWrapper);

        // 记录申请日志
        String content = "申请完成，申请时间：" +  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(requestTime);
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_APPLY.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        /**
         * 进件通知
         */
        this.creditCardSubmitNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber()
        );

    }

    private void applySubmitFail(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                                 ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                                 SubmitResultNotifyDataDTO receiveDto) {
        // 重复通知状态未变化不进行修改
        if (ActivityCreditCardBankUserApplyCompleteEnum.APPLY_FAILED.getCode() == activityCreditCardBankUsersEntity.getApplyCompleted()) {
            log.info(activityCreditCardUsersInfoEntity.getOrderSn() + "申请单已经进件失败，不能重复进件");
            return;
        }
        creditCardUsersInfoBusiness.auditRefuse(activityCreditCardUsersInfoEntity,
                commCreditBankConfig.getFailReason());

        //时间时间戳转localDateTime
        LocalDateTime requestTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(receiveDto.getRequestTime())),
                        ZoneId.systemDefault());

        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> activityCreditCardBankUsersEntityLambdaUpdateWrapper =
                new LambdaUpdateWrapper<>();
        activityCreditCardBankUsersEntityLambdaUpdateWrapper
                .eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getSubmitTime, requestTime)
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted,
                        ActivityCreditCardBankUserApplyCompleteEnum.APPLY_FAILED.getCode());
        activityCreditCardBankUsersService.updateByWrapper(activityCreditCardBankUsersEntityLambdaUpdateWrapper);

        // 记录申请日志
        String content = "进件失败，申请时间：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(requestTime);
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_APPLY.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        /**
         * 进件通知
         */
        this.creditCardSubmitNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber()
        );

    }

    private void activate(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                          ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                          ResultNotifyDto receiveDto) {
        // 重复通知状态未变化不进行修改
        // 允许银行在激活状态未到最高返佣状态前重复通知
        if (activityCreditCardBankUsersEntity.getActivated() == ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode()) {
            log.info(activityCreditCardBankUsersEntity.getApplyNumber() + "重复通知激活不进行处理");
            return;
        }


        // 审核时间时间戳转localDateTime
        LocalDateTime approvalTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(receiveDto.getApprovalResultTime())),
                ZoneId.systemDefault());
        // 激活时间时间戳转localDateTime
        LocalDateTime activateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(receiveDto.getActiveTime())),
                ZoneId.systemDefault());
        // 标记激活
        creditCardUsersInfoBusiness.newUserActivate(activityCreditCardUsersInfoEntity);

        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper =
                new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId,
                        activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getActivated,
                        ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())
                .set(ActivityCreditCardBankUsersEntity::getStatus,
                        ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted,
                        ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ActivityCreditCardBankUsersEntity::getActivateTime,
                        activateTime)
                .set(ActivityCreditCardBankUsersEntity::getIsNewUser,
                        ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode())
        ;
        // 更新银行卡表信息

        // 补充信息记录：如果进件时间、审核时间为空，更新进件时间、审核时间
        if (ObjectUtil.isNull(activityCreditCardBankUsersEntity.getAuditTime())) {
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditTime,
                    approvalTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 激活日志
        String content = "用户激活信用卡，激活时间：" + activateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_ACTIVATE.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        CreditCardActivateNotifyDTO activateNotifyDTO = new CreditCardActivateNotifyDTO();
        activateNotifyDTO.setApplyNumber(activityCreditCardBankUsersEntity.getApplyNumber());
        activateNotifyDTO.setUid(activityCreditCardUsersInfoEntity.getUid());
        activateNotifyDTO.setReferType(activityCreditCardUsersInfoEntity.getReferType());
        activateNotifyDTO.setClassify(activityCreditCardUsersInfoEntity.getClassify());
        // 延迟通知10 s
        queueDefault.push(new CreditCardActivateNotifyDisposer(activateNotifyDTO), 3);

    }


    /**
     * 审核不通过
     *
     * @param activityCreditCardUsersInfoEntity
     * @param activityCreditCardBankUsersEntity
     * @param resultNotifyDto
     */
    private void auditRefuse(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                             ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                             ResultNotifyDto resultNotifyDto
    ) {
        // 重复通知状态未变化不进行修改
        // 已审核通过不能处理失败
        if (Arrays.asList(
                ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode()
        ).contains(activityCreditCardBankUsersEntity.getStatus())) {
            return;
        }
        // 用户申办单进行驳回
        creditCardUsersInfoBusiness.auditRefuse(activityCreditCardUsersInfoEntity,
                commCreditBankConfig.getFailReason());
//      审核时间时间戳转localDateTime
        LocalDateTime approvalTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(resultNotifyDto.getApprovalResultTime())),
                ZoneId.systemDefault());
        // 银行信息记录表更新
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper =
                new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId,
                        activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus,
                        ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplicationStatus,
                        ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted,
                        ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ObjectUtil.isNotNull(resultNotifyDto.getApprovalResultTime()),
                        ActivityCreditCardBankUsersEntity::getAuditTime, approvalTime)
        ;
        if (ObjectUtil.isNull(activityCreditCardBankUsersEntity.getSubmitTime())) {
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getSubmitTime,
                    approvalTime);
        }
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);
        // 记录日志审核不通过
        String content =
                "审核拒绝，拒绝原因：" + commCreditBankConfig.getFailReason() + "，拒绝时间：" + DateTimeFormatter.ofPattern("yyyy-MM" +
                        "-dd HH:mm:ss").format(approvalTime);
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        this.creditCardAuditFailedNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber());
    }

    /**
     * 审核通过
     *
     * @param activityCreditCardUsersInfoEntity
     */
    private void auditPass(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                           ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                           ResultNotifyDto resultNotifyDto) {
        // 重复通知状态未变化不进行修改
        // 未审核、无需审核、审核失败、等待审核状态允许通知审核通过
        if (ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode() == activityCreditCardBankUsersEntity.getStatus()) {
            log.info(activityCreditCardBankUsersEntity.getApplyNumber() + "重复通知审核通过不进行处理");
            return;
        }

        creditCardUsersInfoBusiness.auditPass(activityCreditCardUsersInfoEntity, commCreditBankConfig.getInnerVersion());

        //      审核时间时间戳转localDateTime
        LocalDateTime approvalTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(resultNotifyDto.getApprovalResultTime())),
                        ZoneId.systemDefault());
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper =
                new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId,
                        activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus,
                        ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplicationStatus,
                        ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted,
                        ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ActivityCreditCardBankUsersEntity::getAuditTime, approvalTime)
        ;

        //新旧户处理
        // 申请单标记审核通过
        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditNewUser,
                ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode());
        creditCardUsersInfoBusiness.auditPass(activityCreditCardUsersInfoEntity,
                commCreditBankConfig.getInnerVersion());

        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 审核通过日志
        String content = "审核通过，通过时间：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(approvalTime);
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        // 审核通知
        this.creditCardAuditNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getReferType(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber());
    }

    public Map<String, Object> formatResponse(String code, String message) {
        Map<String, Object> dataMap = ImmutableMap.of("responseCode", code, "responseDesc", message);
        return dataMap;
    }

    /**
     * @param data
     * @return
     * @throws Exception
     */
    public HashMap<String, String> sign(String data) throws Exception {

        return commV2Service.sign(URLEncoder.encode(data, "UTF-8"));
    }
}