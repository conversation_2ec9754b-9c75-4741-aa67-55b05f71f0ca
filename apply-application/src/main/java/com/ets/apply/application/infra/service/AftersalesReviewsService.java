package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.mapper.AftersalesReviewsMapper;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsService extends BaseService<AftersalesReviewsMapper, AftersalesReviews> {

    public IPage<AftersalesReviews> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<AftersalesReviews> queryWrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getOrderType()), AftersalesReviews::getOrderType, dto.getOrderType())
                .eq(dto.getIssuerId() != null, AftersalesReviews::getIssuerId, dto.getIssuerId())
                .eq(StringUtils.isNotBlank(dto.getPlateNo()), AftersalesReviews::getPlateNo, dto.getPlateNo())
                .eq(dto.getUid() != null, AftersalesReviews::getUid, dto.getUid())
                .eq(StringUtils.isNotBlank(dto.getOrderSn()), AftersalesReviews::getOrderSn, dto.getOrderSn())
                .eq(StringUtils.isNotBlank(dto.getReviewSn()), AftersalesReviews::getReviewSn, dto.getReviewSn())
                .eq(dto.getReviewStatus() != null, AftersalesReviews::getReviewStatus, dto.getReviewStatus())
                .orderByDesc(AftersalesReviews::getCreatedAt);

        if (ObjectUtils.isNotEmpty(dto.getApplyTimeStart())) {
            queryWrapper.ge(AftersalesReviews::getApplyTime, dto.getApplyTimeStart().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(dto.getApplyTimeEnd())) {
            queryWrapper.le(AftersalesReviews::getApplyTime, dto.getApplyTimeEnd().atTime(LocalTime.MAX));
        }

        return this.page(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
    }

    public AftersalesReviews getByOrderSn(String orderSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getOrderSn, orderSn)
                .orderByDesc(AftersalesReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public AftersalesReviews getByReviewSn(String reviewSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
