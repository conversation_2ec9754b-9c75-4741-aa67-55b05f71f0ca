package com.ets.apply.application.common.dto.request.issuerAftersales;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerAftersalesApplyDTO {
    @JsonProperty(value = "order_sn")
    private String orderSn;
    @JsonProperty(value = "plate_no")
    private String plateNo;
    @JsonProperty(value = "plate_color")
    private Integer plateColor;
    @JsonProperty(value = "apply_order_sn")
    private String applyOrderSn;


    @JsonProperty(value = "after_sales_buss_scene")
    private Integer afterSalesBussScene = 50;

    @JsonProperty(value = "after_sales_type")
    private Integer afterSalesType;

    @JsonProperty(value = "after_sales_buss_type")
    private Integer afterSalesBussType;

    @JsonProperty(value = "after_sales_reason")
    private Integer afterSalesReason = 99;

    @JsonProperty(value = "after_sales_remark")
    private String afterSalesRemark;

    @JsonProperty(value = "callback_url")
    private String callbackUrl = "";
}
