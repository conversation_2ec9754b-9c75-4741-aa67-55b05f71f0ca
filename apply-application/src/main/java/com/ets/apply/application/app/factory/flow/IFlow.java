package com.ets.apply.application.app.factory.flow;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.common.bo.flowType.FlowTypeProcessBO;
import com.ets.apply.application.common.vo.flow.FlowStepListVO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;

import java.util.LinkedHashMap;


public interface IFlow {
    Boolean hasDone(Object obj,String process,OrderOrderEntity order, JSONObject params);
    Boolean isAllow(Object obj,String process,OrderOrderEntity order, JSONObject params);
    FlowStepListVO getFlowStepList(OrderOrderEntity Order, JSONObject params);
    LinkedHashMap<String, FlowTypeProcessBO> getProcessList();

    Boolean isAllowCreateOrder(OrderOrderEntity Order, JSONObject params);
    Boolean hasDoneCreateOrder(OrderOrderEntity Order, JSONObject params);
    Boolean isAllowPushLogisticOrder(OrderOrderEntity Order, JSONObject params);
    Boolean hasDonePushLogisticOrder(OrderOrderEntity order, JSONObject params);
    Boolean isAllowPay(OrderOrderEntity Order, JSONObject params);
    Boolean hasDonePay(OrderOrderEntity Order, JSONObject params);

    Boolean isAllowUpload(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneUpload(OrderOrderEntity order, JSONObject params);

    Boolean isAllowSubmitReviewOrder(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneSubmitReviewOrder(OrderOrderEntity order, JSONObject params);

    Boolean isAllowSignEntrust(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneSignEntrust(OrderOrderEntity order, JSONObject params);

    Boolean isAllowVerifyBindBank(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneVerifyBindBank(OrderOrderEntity order, JSONObject params);

    Boolean isAllowCancelOrder(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneCancelOrder(OrderOrderEntity order, JSONObject params);

    Boolean isAllowPushReviewOrder(OrderOrderEntity order, JSONObject params);
    Boolean hasDonePushReviewOrder(OrderOrderEntity order, JSONObject params);

    Boolean isAllowActivate(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneActivate(OrderOrderEntity order, JSONObject params);

    Boolean isAllowModifyAddress(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneModifyAddress(OrderOrderEntity order, JSONObject params);

    Boolean isAllowShipOrder(OrderOrderEntity order, JSONObject params);
    Boolean hasDoneShipOrder(OrderOrderEntity order, JSONObject params);

    Boolean isAllowDeclareReceived(OrderOrderEntity order,JSONObject params);
    Boolean hasDoneDeclareReceived(OrderOrderEntity order,JSONObject params);

    Boolean isAllowRejectReviewOrder(OrderOrderEntity order,JSONObject params);

    Boolean isAllowChangeOrderInfo(OrderOrderEntity order,JSONObject params);
    Boolean after(Object obj,String process,OrderOrderEntity order,JSONObject params);
    Boolean isAllowDoStartUsing(OrderOrderEntity order,JSONObject params);

    Boolean afterSubmitReviewOrder(OrderOrderEntity order,JSONObject params);
    Boolean afterSignEntrust(OrderOrderEntity order,JSONObject params);
    Boolean readyForPushReviewOrder(OrderOrderEntity order,JSONObject params);
    Boolean afterVerifyBindBank(OrderOrderEntity order,JSONObject params);
    Boolean afterReviewOrderAudit(OrderOrderEntity order,JSONObject params);

    Boolean readyForPushLogisticOrderJob(OrderOrderEntity order,JSONObject params);
    Boolean isNeedFurtherAudit(OrderOrderEntity order,JSONObject params);
    Boolean afterCancelOrder(OrderOrderEntity order,JSONObject params);


}
