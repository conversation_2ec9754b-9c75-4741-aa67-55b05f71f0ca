package com.ets.apply.application.common.consts.recharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RechargeLogTypeEnum {
    RECHARGE(1, "充值"),
    REFUND(2, "撤销"),
    GET_SECRET_KEY(3, "圈存请求"),
    WRITE_COMPLETE(4, "圈存完成");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RechargeLogTypeEnum[] enums = RechargeLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
