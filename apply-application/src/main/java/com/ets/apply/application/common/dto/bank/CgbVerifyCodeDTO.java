package com.ets.apply.application.common.dto.bank;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class CgbVerifyCodeDTO {

    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @NotEmpty(message = "子业务不能为空")
    private String subBusinessType;

    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    @NotEmpty(message = "卡号不能为空")
    private String cardNo;

    private String msgNo;

    private String msgContent;
}
