package com.ets.apply.application.infra.entity.mapRule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 映射规则item
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("map_rule_item")
public class MapRuleItemEntity extends BaseEntity<MapRuleItemEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则编码
     */
    private String itemSn;

    /**
     * 规则名称
     */
    private String itemName;

    /**
     * 规则json
     */
    private String itemValues;

    /**
     * 状态（有效1，无效2）
     */
    private Integer status;

    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 操作人
     */
    private String operator;
}
