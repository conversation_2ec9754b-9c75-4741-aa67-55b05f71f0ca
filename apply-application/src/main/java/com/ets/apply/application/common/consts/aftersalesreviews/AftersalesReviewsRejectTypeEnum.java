package com.ets.apply.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后审核驳回类型枚举
 */
@Getter
@AllArgsConstructor
public enum AftersalesReviewsRejectTypeEnum {

    OTHER("aftersales_reject_other", "其他"),

    /**
     * 行驶证
     */
    VEHICLE("aftersales_reject_vehicle", "行驶证"),
    
    /**
     * 车头照
     */
    FRONT_CAR_IMG("aftersales_reject_front_car_img", "车头照"),
    
    /**
     * 车内照
     */
    CAR_INNER_IMG("aftersales_reject_car_inner_img", "车内照");

    private final String value;
    private final String desc;

    public static final Map<String, String> map;
    static {
        map = Arrays.stream(AftersalesReviewsRejectTypeEnum.values()).collect(Collectors.toMap(AftersalesReviewsRejectTypeEnum::getValue, AftersalesReviewsRejectTypeEnum::getDesc));
    }
}
