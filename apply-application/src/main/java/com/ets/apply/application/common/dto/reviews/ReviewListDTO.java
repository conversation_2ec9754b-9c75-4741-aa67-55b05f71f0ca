package com.ets.apply.application.common.dto.reviews;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

@Data
public class ReviewListDTO {

    @NotEmpty(message = "开始日期不能为空")
    private String createdStartDate;

    @NotEmpty(message = "结束日期不能为空")
    private String createdEndDate;

    private Integer orderType;

    /**
     * 页码
     */
    @Min(value = 1, message = "页数至少为1")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 2000, message = "每页最多2000条")
    private Integer pageSize = 2000;
}
