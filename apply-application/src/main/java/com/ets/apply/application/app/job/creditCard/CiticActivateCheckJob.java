package com.ets.apply.application.app.job.creditCard;

import com.ets.apply.application.app.business.creditCard.CiticBusiness;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Component
public class CiticActivateCheckJob {

    // 中信银行激活标记
    @Autowired
    private CiticBusiness citicBusiness;

    @XxlJob("citicActivateCheck")
    public ReturnT<String> citicActivateCheckHandler(String params) {
        int checkMinutes = Integer.parseInt(params);
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.MINUTE, -checkMinutes);// 5分钟之前的时间
        Date startTimeD = beforeTime.getTime();
        String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTimeD);
        String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        citicBusiness.activateCompensation(startTime,endTime);
        return ReturnT.SUCCESS;
    }

}
