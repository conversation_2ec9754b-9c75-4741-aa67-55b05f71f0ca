package com.ets.apply.application.feign;

import com.ets.apply.application.app.thirdservice.feign.AfterSalesReviewsNotifyFeign;
import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewsNotifyBO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.net.URI;
import java.time.LocalDateTime;

/**
 * 售后审核单回调通知 Feign 测试
 */
@Slf4j
@SpringBootTest
@RunWith(value = SpringRunner.class)
@ActiveProfiles(value = "dev")
public class AfterSalesReviewsNotifyFeignTest {

    @Autowired
    private AfterSalesReviewsNotifyFeign afterSalesReviewsNotifyFeign;

    @Test
    public void testReviewStatusNotify() {
        try {
            // 构建测试数据
            AfterSalesReviewsNotifyBO notifyBO = new AfterSalesReviewsNotifyBO();
            notifyBO.setOrderSn("SH1250526150000176");
            notifyBO.setReviewStatus(1); // 审核通过
            notifyBO.setOperator("system");

            // 测试回调地址（这里使用一个测试地址）
            URI callbackUri = URI.create("http://etc-saleafter-main:6610/reactivate/auditResult");

            // 发送回调通知
            JsonResult<Object> result = afterSalesReviewsNotifyFeign.reviewStatusNotify(callbackUri, notifyBO);

            log.info("回调通知结果: {}", result);

        } catch (Exception e) {
            log.error("测试回调通知失败", e);
        }
    }
}
