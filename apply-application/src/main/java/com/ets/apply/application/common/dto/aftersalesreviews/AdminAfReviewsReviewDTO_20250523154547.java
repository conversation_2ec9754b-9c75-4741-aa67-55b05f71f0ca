package com.ets.apply.application.common.dto.aftersalesreviews;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AdminAfReviewsReviewDTO {

    @NotBlank(message = "审核单流水号不能为空")
    private String reviewSn;

    @NotNull(message = "审核状态不能为空")
    private Integer reviewStatus;

    @Size(max = 100, message = "拒绝原因不能超过100个字")
    private String rejectReason = "";
}
