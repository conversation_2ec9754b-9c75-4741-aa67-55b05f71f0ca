package com.ets.apply.application.common.dto.request.spd;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SpdOrderReceiveDataDto {

    /**
     * 浦发生成的申请书编号
     */
    private String appId;

    /**
     * 第三方唯一流水号
     */
    private String userId;

    /**
     * Y: 一卡；N: 二卡
     * 仅提交状态提交时推送
     */
    private String firstCard;

    /**
     * 卡片状态
     */
    private String cardState;


    /**
     * 卡片状态变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
}
