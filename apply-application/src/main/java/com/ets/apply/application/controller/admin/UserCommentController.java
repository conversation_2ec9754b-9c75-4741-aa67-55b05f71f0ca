package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.CommentBusiness;
import com.ets.apply.application.common.dto.request.comment.CommentListDTO;
import com.ets.apply.application.common.vo.comment.CommentListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/userComment")
public class UserCommentController extends BaseController {

    @Autowired
    CommentBusiness commentBusiness;

    @RequestMapping("/list")
    public JsonResult<IPage<CommentListVO>> list(@RequestBody CommentListDTO listDTO) {
        return JsonResult.ok(commentBusiness.list(listDTO));
    }
}
