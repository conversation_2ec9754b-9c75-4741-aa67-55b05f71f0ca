package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 出租车导入日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_taxi_import_log")
public class TaxiImportLogEntity extends BaseEntity<TaxiImportLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 导入的数据id
     */
    private Integer importId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 申办订单号
     */
    private String applyOrderSn;

    /**
     * 导入状态
     */
    private Integer importStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
