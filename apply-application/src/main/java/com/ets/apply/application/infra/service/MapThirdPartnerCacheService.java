package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerCacheEntity;
import com.ets.apply.application.infra.mapper.MapThirdPartnerCacheMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;


/**
 * <p>
 * 映射第三方产品包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@DS("db-apply")
public class MapThirdPartnerCacheService extends BaseService<MapThirdPartnerCacheMapper, MapThirdPartnerCacheEntity> {
    /*
     * 通过 type CacheMainKey cacheKey 查找
     */
    public MapThirdPartnerCacheEntity getOneByTypeAndMainKeyAndKey(String type,String cacheMainKey,String cacheKey){
        Wrapper<MapThirdPartnerCacheEntity> wrapper = Wrappers.<MapThirdPartnerCacheEntity>lambdaQuery()
            .eq(MapThirdPartnerCacheEntity::getType, type)
            .eq(MapThirdPartnerCacheEntity::getCacheMainKey, cacheMainKey)
            .eq(MapThirdPartnerCacheEntity::getCacheKey, cacheKey)
            .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }
}
