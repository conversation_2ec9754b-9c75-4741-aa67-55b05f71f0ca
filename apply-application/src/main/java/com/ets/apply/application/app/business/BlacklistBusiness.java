package com.ets.apply.application.app.business;

import com.ets.apply.application.app.thirdservice.feign.BlacklistFeign;
import com.ets.apply.application.common.bo.blacklist.BlacklistBO;
import com.ets.apply.application.common.consts.blacklist.BlacklistOperationEnum;
import com.ets.apply.application.common.dto.blacklist.BlacklistDTO;
import com.ets.common.ToolsHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class BlacklistBusiness extends BaseBusiness{

    @Autowired
    private BlacklistFeign blacklistFeign;
    public void notify(BlacklistBO blacklistBO){
        switch (Objects.requireNonNull(BlacklistOperationEnum.getByCode(blacklistBO.getOperation()))){
            case ADD:
                BlacklistDTO blacklistDTO = new BlacklistDTO();
                BeanUtils.copyProperties(blacklistBO, blacklistDTO);
                blacklistFeign.add(blacklistDTO);
                break;
            default:
                ToolsHelper.throwException("未定义黑名单操作类型");
        }
    }
}
