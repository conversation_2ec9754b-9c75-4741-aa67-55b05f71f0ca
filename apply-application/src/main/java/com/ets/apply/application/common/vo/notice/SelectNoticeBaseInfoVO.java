package com.ets.apply.application.common.vo.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class SelectNoticeBaseInfoVO {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 通知id
     */
    private String noticeId;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 通知标题
     */
    private String noticeTitle;

    /**
     * 模板标题
     */
    private String templateTitle;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 使用业务
     */
    private String costBusiness;

    /**
     * 推送方式:0-etc助手公众号,1-etc助手小程序,2-etc会员小程序
     */
    private Integer sendMode;

    /**
     * 跳转方式:0-第三方小程序页面,1-h5页面
     */
    private Integer jumpMode;

    /**
     * appId
     */
    private String appId;

    /**
     * 图片地址
     */
    private String pageUrl;

    /**
     * 推送类型:0-手动,1-自动文案
     */
    private Integer sendType;

    /**
     * 推送人数
     */
    private Integer sendNum;

    /**
     * 推送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 推送权重
     */
    private Integer weight;

    /**
     * 1一次性订阅消息 2-永久订阅消息
     */
    private Integer msgType;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 更新人id
     */
    private String updatorId;

    /**
     * 0-未删除，1-已删除
     */
    private Integer dr;
}
