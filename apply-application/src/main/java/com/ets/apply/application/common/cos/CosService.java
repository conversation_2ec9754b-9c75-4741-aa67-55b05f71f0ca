package com.ets.apply.application.common.cos;

import com.ets.apply.application.common.config.CosConfig;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import cn.hutool.core.io.file.FileReader;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Date;

/**
 * @Description 腾讯云cos服务器上传工具类
 * <AUTHOR>
 * @Since 2021/7/28
 */
@Data
@Component
@Slf4j
public class CosService {
    //自定义host
    private String customHost;

    @Autowired
    private CosConfig cosConfig;

    /**
     * 上传腾讯云
     *
     * @param bytes    文件字节
     * @param filePath 文件路径
     * @return 腾讯云访问路径
     */
    public String upload(byte[] bytes, String filePath) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);

        // 处理文件路径
        filePath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 判断文件大小（小文件上传建议不超过20M）
        int length = bytes.length;
        // 获取文件流
        InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        objectMetadata.setContentLength(length);

        PutObjectRequest putObjectRequest = new PutObjectRequest(cosConfig.getBucket(), filePath, byteArrayInputStream, objectMetadata);
        // 设置 Content type, 默认是 application/octet-stream
        putObjectRequest.setMetadata(objectMetadata);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia)
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

        String eTag = putObjectResult.getETag();
        log.info(eTag);
        // 关闭客户端
        cosClient.shutdown();
        // http://{buckname}-{appid}.cosgz.myqcloud.com/image/1545012027692.jpg
        return cosConfig.getBaseUrl() + filePath;
    }

    /**
     * 上传腾讯云
     *
     * @param file     文件
     * @param filePath 文件路径
     * @return 腾讯云访问路径
     */
    public String upload(File file, String filePath) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);

        // 处理文件路径
        filePath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 判断文件大小（小文件上传建议不超过20M）
        FileReader fileReader = new FileReader(file);
        byte[] bytes = fileReader.readBytes();
        int length = bytes.length;

        // 获取文件流
        InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        objectMetadata.setContentLength(length);

        PutObjectRequest putObjectRequest = new PutObjectRequest(cosConfig.getBucket(), filePath, byteArrayInputStream, objectMetadata);
        // 设置 Content type, 默认是 application/octet-stream
        putObjectRequest.setMetadata(objectMetadata);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia)
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

        String eTag = putObjectResult.getETag();
        log.info(eTag);
        // 关闭客户端
        cosClient.shutdown();
        // http://{buckname}-{appid}.cosgz.myqcloud.com/image/1545012027692.jpg
        return cosConfig.getBaseUrl() + filePath;
    }

    /**
     * 下载cos文件
     * @param bucketName 桶名称
     * @param cosPath    cos路径
     * @param localFile  生成的本地文件
     */
    public void getObjectToFile(String bucketName, String cosPath, File localFile) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, cosPath);
        cosClient.getObject(getObjectRequest, localFile);
        // 关闭客户端
        cosClient.shutdown();
    }

    /**
     * 永久密钥生成一个带签名的下载链接
     * @param bucketName
     * @param key
     * @param expireTime
     * @return
     */
    public String getObjectSignUrl(String bucketName, String key, Date expireTime) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        GeneratePresignedUrlRequest req =
                new GeneratePresignedUrlRequest(bucketName, key, HttpMethodName.GET);
        req.setExpiration(expireTime);
        URL url = cosClient.generatePresignedUrl(req);
        cosClient.shutdown();
        return replaceUrl(url.toString(), customHost);
    }

    /**
     * 替换为自定义域名
     *
     * @param originalURL
     * @param newHost
     * @return
     */
    public String replaceUrl(String originalURL, String newHost) {
        try {
            return originalURL.replace(getDomainName(originalURL), newHost);
        } catch (URISyntaxException e) {
            return originalURL;
        }
    }

    /**
     * 获得url的host
     * @param url
     * @return
     * @throws URISyntaxException
     */
    public static String getDomainName(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String domain = uri.getHost();
        return domain.startsWith("www.") ? domain.substring(4) : domain;
    }
}
