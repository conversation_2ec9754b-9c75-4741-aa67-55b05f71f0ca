package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.dongfeng")
public class DongFengConfig {
    private String appid;
    // 密钥 按对接方的格式，不要自己改
    private String appkey;

    private String serverPublicKey;
    // 客户端私钥
    private String clientPrivateKey;
    // API 通知地址
    private String orderNotifyApi;

    // 日志上报通知地址
    private String eventLogNotifyApi;

    // 订单来源
    private List<String> sources;

}
