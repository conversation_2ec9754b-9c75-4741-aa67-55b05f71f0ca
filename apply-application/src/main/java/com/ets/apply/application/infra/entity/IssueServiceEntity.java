package com.ets.apply.application.infra.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * etc发行服务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_issue_service")
public class IssueServiceEntity extends BaseEntity<IssueServiceEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 发行服务单编号
     */
    private String serviceSn;

    /**
     * 类型[1:比亚迪APP,3:东风本田小程序]
     */
    private Integer type;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 商品订单编号，支付成功后更新
     */
    private String goodsOrderSn;

    /**
     * 签约记录id
     */
    private Integer entrustId;

    /**
     * 协议流水号-sign_serial_num 
     */
    private String contractId;

    /**
     * 签约号(冗余)
     */
    private String contractNumber;

    /**
     * 状态[0:默认,1:已支付,6:已完成,7:已注销,9:已取消]
     */
    private Integer status;

    /**
     * 退款状态: 1 已申请退款，0未退款
     */
    private Integer refundStatus;

    private LocalDateTime contractTime;

    /**
     * 对应usercard的id
     */
    private Integer vehicleId;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 支付金额
     */
    private BigDecimal paidFee;

    /**
     * 发卡方编号
     */
    private String issuerCode;

    /**
     * json格式，其他用户信息字段值
     */
    private String userInfo;

    /**
     * json格式，车辆信息
     */
    private String vehicleInfo;

    /**
     * 激活状态
     */
    private Integer activatedStatus;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
