package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单售后申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_aftersales_apply")
public class OrderAftersalesApplyEntity extends BaseEntity<OrderAftersalesApplyEntity> {

    private static final long serialVersionUID = 1L;

    private String applySn;
    @Override
    public Serializable pkVal() {
        return this.applySn;
    }
    /**
     * 申请类型
     */
    private Integer type;

    private Long uid;

    /**
     * 订单流水号
     */
    private String orderSn;

    private String reason;

    /**
     * 取消失败的原因
     */
    private String error;

    /**
     * 车牌号（冗余）
     */
    private String plateNo;

    /**
     * 0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 11: 绿色【不用】 12: 红色 
     */
    private Integer plateColor;

    /**
     * 订单申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyAt;
    
    /**
     * 申请状态
     */
    private Integer status;
    /**
     * 业务状态：0未完成1已完成
     */
    private Integer businessStatus;
    /**
     * 对应发卡方编码
     */
    private String issuerCode;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
