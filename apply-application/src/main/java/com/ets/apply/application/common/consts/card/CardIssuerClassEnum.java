package com.ets.apply.application.common.consts.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CardIssuerClassEnum {
    JSUTONG_TAXI("JSuTongTaxi", "江苏出租车");

    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (CardIssuerClassEnum node : CardIssuerClassEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}
