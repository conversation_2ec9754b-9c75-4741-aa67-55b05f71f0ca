package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.dto.request.comment.CommentListDTO;
import com.ets.apply.application.infra.entity.CommentEntity;
import com.ets.apply.application.infra.mapper.CommentMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户评论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Service
@DS("db-apply")
public class CommentService extends BaseService<CommentMapper, CommentEntity> {

    public IPage<CommentEntity> getPage(CommentListDTO listDTO) {
        Wrapper<CommentEntity> wrapper = Wrappers.<CommentEntity>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(listDTO.getUid()), CommentEntity::getUid, listDTO.getUid())
                .eq(ObjectUtils.isNotEmpty(listDTO.getPlateNo()), CommentEntity::getPlateNo, listDTO.getPlateNo())
                .eq(ObjectUtils.isNotEmpty(listDTO.getFeatured()), CommentEntity::getFeatured, listDTO.getFeatured())
                .ge(ObjectUtils.isNotEmpty(listDTO.getCreateTimeStart()), CommentEntity::getCreatedAt, listDTO.getCreateTimeStart())
                .le(ObjectUtils.isNotEmpty(listDTO.getCreateTimeEnd()), CommentEntity::getCreatedAt, listDTO.getCreateTimeEnd())
                .orderByDesc(CommentEntity::getCreatedAt);

        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }
}
