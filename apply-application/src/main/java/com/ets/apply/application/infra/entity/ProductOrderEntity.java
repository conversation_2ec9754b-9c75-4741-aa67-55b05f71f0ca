package com.ets.apply.application.infra.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商品订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("product_order")
public class ProductOrderEntity extends BaseEntity<ProductOrderEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 商品订单号 
     */
    @TableId
    private String productOrderSn;

    /**
     * 第三方订单号
     */
    private String thirdOrderSn;

    /**
     * 产品包sn
     */
    private String packageSn;

    /**
     * 申办单号
     */
    private String applyOrderSn;

    /**
     * 快递公司
     */
    private String logisticCompany;

    /**
     * 快递单号
     */
    private String logisticNumber;

    /**
     * 发货状态 0:未推送 1:已推送 3:已发货 4:已签收
     */
    private Integer logisticStatus;

    /**
     * 订单状态 10:初始状态 20:待发货 30:已发货 40:已签收  50:售后中 60：售后完成 90
     */
    private Integer orderStatus;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 收货人
     */
    private String sendName;

    /**
     * 收货手机
     */
    private String sendPhone;

    /**
     * 办理手机号
     */
    private String etcPhone;

    /**
     * 订单来源
     */
    private String source;

    /**
     * 收货地区
     */
    private String sendArea;

    /**
     * 收货详细地址
     */
    private String sendAddress;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidTime;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 收货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receivedTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    /**
     * 异常信息
     */
    private String msg;

    /**
     * 支付单号
     */
    private String paymentSn;

    private String refundSn;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundAt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String remark;

    private String cancelReason;

    /**
     * 渠道绑定id
     */
    private Long channelBindId;
    /**
     * 渠道号
     */
    private Long channelId;

    /**
     * 交易单号
     */
    private String transactionId;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 关联类型
     */
    private Integer referType;

    /**
     *
     * 关联值
     */
    private Integer referValue;

    /**
     * 关联用户
     */
    private String referUserCode;
}
