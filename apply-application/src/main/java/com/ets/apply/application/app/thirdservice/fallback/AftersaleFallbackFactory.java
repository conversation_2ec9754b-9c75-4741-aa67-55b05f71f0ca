package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.AftersaleFeign;
import com.ets.apply.application.app.thirdservice.request.aftersale.CheckAuthorizeActivateDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AftersaleFallbackFactory implements FallbackFactory<AftersaleFeign> {

    @Override
    public AftersaleFeign create(Throwable throwable) {
        return new AftersaleFeign() {
            @Override
            public String authorizeActivate(CheckAuthorizeActivateDTO dto) {
                return JsonResult.error("请求etc-saleafter-main 服务失败: " + throwable.getMessage()).toString();
            }

        };
    }
}
