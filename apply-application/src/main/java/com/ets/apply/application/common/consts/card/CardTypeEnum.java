package com.ets.apply.application.common.consts.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CardTypeEnum {
    DEBIT_CARD(1, "记账卡"),
    PRE_PAY_CARD(2, "储值卡");


    private final Integer cardType;
    private final String name;

    public static String getDescByCardType(Integer cardType) {
        for (CardTypeEnum node : CardTypeEnum.values()) {
            if (node.getCardType().equals(cardType)) {
                return node.getName();
            }
        }
        return "";
    }

}
