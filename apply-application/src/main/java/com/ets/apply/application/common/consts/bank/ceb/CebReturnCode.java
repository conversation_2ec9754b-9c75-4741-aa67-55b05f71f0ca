package com.ets.apply.application.common.consts.bank.ceb;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CebReturnCode {
    SUCCESS("0000", "接口调用成功"),
    PARAMS_ILLEGAL("1001", "请求参数非法"),
    SIGN_ILLEGAL("1002", "请求签名非法"),
    DECRYPT_FAIL("1003", "参数解密故障"),
    SYSTEM_ERROR("1004", "服务内部异常"),
    SYSTEM_BUSY("1005", "系统繁忙，请稍后再试"),
    FAIL("1000", "交易失败");
    private final String code;
    private final String description;

}
