package com.ets.apply.application.common.vo.usercard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UserCardFindByOrderSnVO {

    @JsonProperty("card_no")
    private String cardNo;

    @JsonProperty("card_id")
    private Integer cardId;

    @JsonProperty("first_activated_at")
    private String firstActivatedAt;

    @JsonProperty("vehicle_obu")
    private VehicleObu vehicleObu;

    @Data
    public static class VehicleObu {
        @JsonProperty("warranty_start_at")
        private String warrantyStartAt;

        @JsonProperty("warranty_end_at")
        private String warrantyEndAt;

    }
}
