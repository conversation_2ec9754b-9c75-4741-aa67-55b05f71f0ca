package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ApplyGuideResult;
import com.ets.apply.application.infra.mapper.ApplyGuideResultMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Service
@DS("db-etc")
public class ApplyGuideResultService extends BaseService<ApplyGuideResultMapper, ApplyGuideResult> {

}
