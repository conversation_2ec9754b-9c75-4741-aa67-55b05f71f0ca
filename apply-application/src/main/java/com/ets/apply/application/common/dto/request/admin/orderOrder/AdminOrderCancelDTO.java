package com.ets.apply.application.common.dto.request.admin.orderOrder;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class AdminOrderCancelDTO {
    @NotNull(message = "orderSn不可为空")
    @JsonProperty(value = "order_sn")
    private String orderSn;
    @JsonProperty(value = "force_cancel")
    private Integer forceCancel =1;

    @JsonProperty(value = "cancel_msg")
    private String cancelMsg = "后台强制取消";

    @JsonProperty(value = "admin_uid")
    private Integer adminUid = 1;
}
