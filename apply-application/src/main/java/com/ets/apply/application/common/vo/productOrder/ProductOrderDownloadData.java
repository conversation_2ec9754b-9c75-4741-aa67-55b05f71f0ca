package com.ets.apply.application.common.vo.productOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class ProductOrderDownloadData {

    @ExcelProperty("订单编号")
    private String productOrderSn;
    /**
     * 第三方订单号
     */
    @ExcelProperty("渠道订单号")
    private String thirdOrderSn;

    @ExcelProperty("渠道用户编号")
    private String referUserCode;


    /**
     * 产品包名称
     */
    @ExcelProperty("办理方式")
    public String packageStr;

    /**
     * 实际支付金额
     */
    @ExcelProperty("实际销售价格（元）")
    private BigDecimal paidAmount;

    /**
     * 快递单号
     */
    @ExcelProperty("物流单号")
    private String logisticNumber;

    /**
     * 发货状态 0:未推送 1:已推送 3:已发货 4:已签收
     */
    @ExcelProperty("物流状态")
    private String logisticStatusStr;

    @ExcelProperty("订单状态")
    private String statusStr;

    /**
     * 订单金额
     */
    @ExcelProperty("订单总金额（元）")
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


}
