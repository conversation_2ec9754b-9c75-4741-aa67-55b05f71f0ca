package com.ets.apply.application.common.bo.vasp;

import lombok.Data;

@Data
public class VaspOrderResultBO {
    /**
     * 订单状态
     */
    private String orderId;
    /**
     * 0-订单取消，1-初审通过、2-配货完成，3-产生物流单号，4-客户签收，5、客户拒收，6-客户退货，7-客户换货，9-订单完成
     */
    private String orderStatus;
    /**
     * 当前状态的时间
     */
    private String statusDTime;
    /**
     * 状态变化摘要 取消原因
     */
    private String cancelReason;
    /**
     * 订单地址
     */
    private String orderAddr;
    /**
     * 订单备注
     */
    private String orderDesc;

    /**
     * 是否退款
     */
    private String isRefund;
    /**
     * 退款金额
     */
    private Double refundAmt;

    /**
     * 运单号
     */
    private String deliveryNo;

    /**
     * 物流公司
     */
    private String logisticCompany;
    /**
     * 商户id
     */
    private String agent_id;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 签名
     */
    private String sign;
}


