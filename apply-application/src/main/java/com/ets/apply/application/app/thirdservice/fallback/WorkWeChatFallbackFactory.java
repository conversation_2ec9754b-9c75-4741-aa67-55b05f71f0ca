package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.WorkWeChatFeign;
import com.ets.apply.application.app.thirdservice.response.workWeChat.WorkWeChatSendVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class WorkWeChatFallbackFactory implements FallbackFactory<WorkWeChatFeign> {
    @Override
    public WorkWeChatFeign create(Throwable throwable) {
        return new WorkWeChatFeign() {
            @Override
            public WorkWeChatSendVO send(String json, String key) {
                return null;
            }

            @Override
            public String uploadMedia(MultipartFile file, String key, String type) {
                return null;
            }
        };
    }
}
