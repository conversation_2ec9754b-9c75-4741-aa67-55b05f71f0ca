package com.ets.apply.application.app.business;

import cn.hutool.core.util.HexUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.disposer.RechargeNotifyDisposer;
import com.ets.apply.application.app.service.machine.SelfServiceMachineService;
import com.ets.apply.application.common.bo.recharge.*;
import com.ets.apply.application.common.consts.recharge.RechargeCardTypeEnum;
import com.ets.apply.application.common.consts.recharge.RechargeIssuerRaiseStatusEnum;
import com.ets.apply.application.common.consts.recharge.RechargeRecordPayStatusEnum;
import com.ets.apply.application.common.consts.recharge.RechargeWriteStatusEnum;
import com.ets.apply.application.common.consts.redisCache.MachineRedisCacheKeyConstants;
import com.ets.apply.application.common.consts.writeCard.WriteCardResultEnum;
import com.ets.apply.application.common.dto.machine.MachineReadCardDTO;
import com.ets.apply.application.common.dto.machine.MachineWriteCardDTO;
import com.ets.apply.application.common.vo.machine.MachineReadCardVO;
import com.ets.apply.application.infra.entity.RechargeRecord;
import com.ets.apply.application.infra.entity.RechargeWriteCard;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.common.util.UserUtil;
import com.ets.starter.queue.QueueDefault;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SelfServiceMachineBusiness {

    @Autowired
    RechargeBusiness rechargeBusiness;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisDefaultTemplate;

    @Autowired
    SelfServiceMachineService selfServiceMachineService;

    @Autowired
    QueueDefault queue;

    public MachineReadCardVO readCard(MachineReadCardDTO readCardDTO) {
        ReadCardBO readCard = null;
        Integer balance = null;
        try {
            // 开卡
            String openCard = selfServiceMachineService.openCard(readCardDTO.getDeviceNo());
            log.info("【读卡信息】开卡：{}", openCard);

            // 进目录
            String enterDir = selfServiceMachineService.enterDir(readCardDTO.getDeviceNo());
            log.info("【读卡信息】进目录：{}", enterDir);

            // 读卡
            readCard = selfServiceMachineService.readCard(readCardDTO.getDeviceNo());
            log.info("【读卡信息】读卡：{}", readCard);

            // 余额
            balance = selfServiceMachineService.balance(readCardDTO.getDeviceNo());
            log.info("【读卡信息】余额：{}", balance);
        } catch (Throwable e) {
            log.error("【读卡信息】失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("读卡异常，请重试");
        }

        // 缓存当前操作机器 用户 卡号(uid,cardNo,process)
        Long uid = UserUtil.getUid();
        Map<String, Object> machine = new HashMap<>();
        machine.put("uid", uid);
        machine.put("cardSn", readCard.getNetCode() + readCard.getCardNo());
        machine.put("process", "openCard");
        String machineStr = JSON.toJSONString(machine);
        redisDefaultTemplate.opsForValue().set(MachineRedisCacheKeyConstants.MACHINE_RECHARGE_PROCESS + readCardDTO.getDeviceNo(), machineStr);

        // 判断卡方
        Map<Integer, String> map = new HashMap<>();
        map.put(1, "3201");

        if (!map.getOrDefault(readCardDTO.getIssuerId(), "未知").equals(readCard.getNetCode())) {
            ToolsHelper.throwException("充值卡片不属于所选卡方");
        }

        // 判断是否储值卡
        if (!RechargeCardTypeEnum.PRE_PAY_CARD.getValue().equals(readCard.getCardType())) {
            ToolsHelper.throwException("卡片非储值卡，无法充值");
        }

        // 判断有效时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate startDate = LocalDate.parse(readCard.getStartDate(), formatter);
        LocalDate expiredDate = LocalDate.parse(readCard.getExpiredDate(), formatter);
        LocalDate today = LocalDate.now();
        if (today.isAfter(expiredDate) || today.isBefore(startDate)) {
            ToolsHelper.throwException("卡片已过有效期，无法充值");
        }

        MachineReadCardVO readCardVO = new MachineReadCardVO();
        readCardVO.setCardSn(readCard.getNetCode() + readCard.getCardNo());
        readCardVO.setPlateNo(readCard.getPlateNo());
        readCardVO.setPlateColor(Integer.parseInt(readCard.getPlateColor()));
        readCardVO.setBalance(new BigDecimal(balance).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));

        return readCardVO;
    }

    public void writeCard(MachineWriteCardDTO writeCardDTO) {
        // 获取充值订单信息
        RechargeRecord record = rechargeBusiness.getRecordByOrderSn(writeCardDTO.getOrderSn());
        if (ObjectUtils.isEmpty(record)) {
            ToolsHelper.throwException("充值记录不存在");
        }

        // 获取机器当前操作状态
        String machine = redisDefaultTemplate.opsForValue().get(MachineRedisCacheKeyConstants.MACHINE_RECHARGE_PROCESS + writeCardDTO.getDeviceNo());
        if (StringUtils.isEmpty(machine)) {
            ToolsHelper.throwException("请重新读卡");
        }
        JSONObject jsonObject = JSON.parseObject(machine);
        Long uid = jsonObject.getLong("uid");
        String cardSn = jsonObject.getString("cardSn");
        // uid
        if (!record.getUid().equals(uid)) {
            ToolsHelper.throwException("对不起，这不是你的储值订单");
        }

        // cardSn
        if (!record.getCardSn().equals(cardSn)) {
            ToolsHelper.throwException("充值卡号与待圈存卡号不一致, 充值卡号为：" + record.getCardSn());
        }

        // 支付状态
        if (record.getPayStatus().equals(RechargeRecordPayStatusEnum.DEFAULT.getValue())) {
            ToolsHelper.throwException("支付结果确认中，请稍后重试");
        }

        // 写卡状态
        if (record.getIsWriteCard().equals(RechargeWriteStatusEnum.SUCCESS.getValue())) {
            ToolsHelper.throwException("储值订单已圈存成功");
        }

        // 卡方充值状态
        if (record.getIssuerRaiseStatus().equals(RechargeIssuerRaiseStatusEnum.FAILED.getValue())) {
            ToolsHelper.throwException("系统异常，请联系在线客服");
        }

        if (record.getIssuerRaiseStatus().equals(RechargeIssuerRaiseStatusEnum.DEFAULT.getValue())) {
            ToolsHelper.throwException("发卡方充值中，请稍后再试或联系在线客服");
        }

        // 获取写卡记录
        List<ConsumeRecordBO> recordList = new ArrayList<>();
        RechargeWriteCard writeCard = rechargeBusiness.getWriteCardByOrderSn(writeCardDTO.getOrderSn());
        if (ObjectUtils.isNotEmpty(writeCard) && writeCard.getResult().equals(WriteCardResultEnum.BEGIN_WRITE.getValue())) {
            // 读取交易记录
            recordList = cardConsumeRecords(writeCardDTO.getDeviceNo(), 50);
            log.info("【圈存】【交易记录】{}", recordList);
            // 上传记录
            rechargeBusiness.cardConsumeRecord(record, recordList);
        }

        // 获取mac1
        WriteCardMacBO macBO = getMac(writeCardDTO.getDeviceNo(), record.getRaiseValue());
        log.info("【圈存】【获取mac1】{}", macBO);

        // 异常处理
        if (ObjectUtils.isNotEmpty(writeCard) && writeCard.getResult().equals(WriteCardResultEnum.BEGIN_WRITE.getValue())) {
            Boolean handleResult = rechargeBusiness.handleWriteCard(record, writeCard, macBO, recordList);
            // 圈存成功 返回
            if (handleResult) {
                int serialNoInt = Integer.parseInt(macBO.getSerialNo(), 16);
                String afterSerialNo = String.format("%04d", (serialNoInt + 1));
                String tac = selfServiceMachineService.tac(writeCardDTO.getDeviceNo(), afterSerialNo);
                log.info("【圈存】【异常处理】已圈存 serialNo：{} tac：{}", afterSerialNo, tac);

                // 写卡成功
                WriteCardSuccessBO successBO = new WriteCardSuccessBO();
                successBO.setRechargeRecord(record);
                successBO.setWriteCard(writeCard);
                successBO.setTac(tac);
                successBO.setBalance(macBO.getBalance());
                successBO.setSerialNo(afterSerialNo);
                rechargeBusiness.writeCardSuccess(successBO);

                // 通知卡方
                RechargeNotifyBO notifyBO = new RechargeNotifyBO();
                notifyBO.setOrderSn(record.getOrderSn());
                notifyBO.setNeedNotify(false);
                queue.push(new RechargeNotifyDisposer(notifyBO));

                // 闭卡
                String closeCard = selfServiceMachineService.closeCard(writeCardDTO.getDeviceNo());
                log.info("【圈存】【闭卡】{}", closeCard);

                // 清缓存
                redisDefaultTemplate.delete(MachineRedisCacheKeyConstants.MACHINE_RECHARGE_PROCESS + writeCardDTO.getDeviceNo());
                return;
            }
        }

        // 获取mac2
        WriteCardSecretKeyBO secretKeyBO = rechargeBusiness.getSecretKey(record, writeCard, macBO);
        log.info("【圈存】【获取mac2】{}", secretKeyBO);

        // 获取mac2成功
        writeCard = rechargeBusiness.getWriteCardByOrderSn(record.getOrderSn());
        // 写卡
        String tac = null;
        try {
            tac = selfServiceMachineService.writeCard(writeCardDTO.getDeviceNo(), secretKeyBO.getTradeTime(), secretKeyBO.getSecretKey());
            log.info("【圈存】【写卡】{}", tac);
        } catch (BizException e) {
            if (e.getErrorCode() > 0) {
                log.error("【圈存】【写卡】错误码：{} 失败：{}", e.getErrorCode(), e.getLocalizedMessage());
                // 写卡失败
                rechargeBusiness.writeCardFail(writeCard, macBO);
                ToolsHelper.throwException("写卡失败，请重试");
            }
        }

        // 读取余额
        Integer balance = selfServiceMachineService.balance(writeCardDTO.getDeviceNo());
        log.info("【圈存】【写卡】写卡后余额：{}", balance);

        // 写卡成功
        int serialNoInt = Integer.parseInt(macBO.getSerialNo(), 16);
        String afterSerialNo = String.format("%04X", (serialNoInt + 1));
        log.info("【圈存】【写卡】写卡后serialNo：{}", afterSerialNo);

        WriteCardSuccessBO successBO = new WriteCardSuccessBO();
        successBO.setRechargeRecord(record);
        successBO.setWriteCard(writeCard);
        successBO.setTac(tac);
        successBO.setBalance(balance);
        successBO.setSerialNo(afterSerialNo);
        rechargeBusiness.writeCardSuccess(successBO);

        // 通知卡方
        RechargeNotifyBO notifyBO = new RechargeNotifyBO();
        notifyBO.setOrderSn(record.getOrderSn());
        notifyBO.setNeedNotify(true);
        queue.push(new RechargeNotifyDisposer(notifyBO));

        // 闭卡
        String closeCard = selfServiceMachineService.closeCard(writeCardDTO.getDeviceNo());
        log.info("【圈存】【闭卡】{}", closeCard);

        // 清缓存
        redisDefaultTemplate.delete(MachineRedisCacheKeyConstants.MACHINE_RECHARGE_PROCESS + writeCardDTO.getDeviceNo());
    }

    public WriteCardMacBO getMac(String deviceNo, BigDecimal amount) {
        try {
            // 进目录
            String enterDir = selfServiceMachineService.enterDir(deviceNo);
            log.info("【圈存】【获取mac1】进目录：{}", enterDir);

            // 读卡
            ReadCardBO readCard = selfServiceMachineService.readCard(deviceNo);
            log.info("【圈存】【获取mac1】读卡：{}", readCard);

            // pin码验证
            String pin = selfServiceMachineService.pin(deviceNo, readCard.getCardVersion());
            if (ObjectUtils.isEmpty(pin)) {
                ToolsHelper.throwException("pin码执行失败，请重试");
            }

            // 获取mac1
            String amountStr = String.format("%8s", Integer.toHexString(amount.multiply(new BigDecimal(100)).intValue())).replace(" ", "0");
            return selfServiceMachineService.mac1(deviceNo, amountStr);
        } catch (Throwable e) {
            log.error("获取mac1失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("卡片圈存密钥mac1获取失败");
        }
        return null;
    }

    /**
     * 读卡交易记录
     * @param deviceNo 机器编号
     * @param count 记录数量
     * @return 交易记录
     */
    public List<ConsumeRecordBO> cardConsumeRecords(String deviceNo, Integer count) {
        List<ConsumeRecordBO> recordList = new ArrayList<>();
        try {
            // 进目录
            String enterDir = selfServiceMachineService.enterDir(deviceNo);
            log.info("【圈存】【交易记录】进目录：{}", enterDir);

            // 读卡
            ReadCardBO readCard = selfServiceMachineService.readCard(deviceNo);
            log.info("【圈存】【交易记录】读卡：{}", readCard);

            // pin码验证
            String pin = selfServiceMachineService.pin(deviceNo, readCard.getCardVersion());
            if (ObjectUtils.isEmpty(pin)) {
                ToolsHelper.throwException("pin码执行失败，请重试");
            }

            for (int i = 1; i < count; i++) {
                String number = String.format("%02X", i & 0xFF);
                ConsumeRecordBO record = selfServiceMachineService.consumeRecord(deviceNo, number);
                // 空记录
                if (ObjectUtils.isEmpty(record)) {
                    break;
                }

                // 时间格式不正确
                LocalDateTime dateTime = LocalDateTime.parse(record.getInTransTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                if (dateTime.isBefore(LocalDateTime.parse("20170101000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))) {
                    continue;
                }
                recordList.add(record);
            }
        } catch (Throwable e) {
            log.error("读取交易记录失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("卡片交易记录读取失败");
        }
        return recordList;
    }


}
