package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPhpBaseFallbackFactory;
import com.ets.apply.application.common.dto.request.notice.MinaNoticeSendDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用PHP队列
 */
@FeignClient(
        url = "${params.microUrls.gd-micro-base}",
        name = "CallPhpBaseFeign",
        fallbackFactory = CallPhpBaseFallbackFactory.class
)
public interface CallPhpBaseFeign {


    @PostMapping(value = "/mina-notice/send")
    String sendMinaNotice(@RequestBody MinaNoticeSendDTO minaNoticeSendDTO);
}
