package com.ets.apply.application.app.disposer;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ets.apply.application.common.bo.aftersalesreviews.AfterSalesReviewAutoAuditBO;
import com.ets.apply.application.common.bo.aftersalesreviews.VehicleDataComparisonResultBO;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsLogService;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.ToolsHelper;
import com.ets.common.queue.BaseDisposer;

import lombok.NoArgsConstructor;

@NoArgsConstructor
@Component(value = "afterSalesReviewAutoAuditJobBean")
public class AfterSalesReviewAutoAuditDisposer extends BaseDisposer {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Autowired
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    public AfterSalesReviewAutoAuditDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "afterSalesReviewAutoAuditJobBean";
    }

    @Override
    public void execute(Object content) {
        AfterSalesReviewAutoAuditBO bo = getParamsObject(content, AfterSalesReviewAutoAuditBO.class);

        // 获取审核单数据
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(bo.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("售后审核单记录不存在");
            return;
        }

       if (AftersalesReviewsStatusEnum.CANCELED.getValue().equals(aftersalesReviews.getReviewStatus())) {
            ToolsHelper.throwException("审核单已取消");
            return;
       }

       if (Arrays.asList(
                AftersalesReviewsStatusEnum.APPROVED.getValue(),
                AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
        ) {
            ToolsHelper.throwException("审核单已有审核结果");
            return;
        }

        // 获取行驶证数据
        List<AftersalesReviewsVehicles> vehiclesList = aftersalesReviewsVehiclesService.getByReviewSn(aftersalesReviews.getReviewSn());
        if (ObjectUtils.isEmpty(vehiclesList)) {
            ToolsHelper.throwException("行驶证数据不存在");
            return;
        }


        // 获取审核资料和申办资料的数据
        AftersalesReviewsVehicles reviewData = vehiclesList.stream()
            .filter(vehicle -> AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue().equals(vehicle.getDataType()))
            .findFirst()
            .orElse(null);
        
        AftersalesReviewsVehicles orderData = vehiclesList.stream()
            .filter(vehicle -> AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue().equals(vehicle.getDataType()))
            .findFirst()
            .orElse(null);

        if (ObjectUtils.isEmpty(reviewData) || ObjectUtils.isEmpty(orderData)) {
            ToolsHelper.throwException("行驶证数据不完整，需要同时存在审核资料和申办资料的数据");
        }

        // 判断是否可以自动审核
            
        // 对比行驶证数据是否一致
        VehicleDataComparisonResultBO compareVehicleData = compareVehicleData(reviewData, orderData);
        
        if (compareVehicleData.isMatch()) {
            // 更新审核状态为通过
            AftersalesReviews updateReviews = new AftersalesReviews();
            updateReviews.setId(aftersalesReviews.getId());
            updateReviews.setReviewStatus(AftersalesReviewsStatusEnum.APPROVED.getValue());
            updateReviews.setReviewTime(LocalDateTime.now());
            updateReviews.setAutoAudit(1);
            updateReviews.setOperator("system");
            updateReviews.setReviewRemark("自动审核通过");
            updateReviews.setUpdatedAt(LocalDateTime.now());
            aftersalesReviewsService.updateById(aftersalesReviews);
        } else {
            // 资料不一致 需人工审核
            // 记录操作日志
            AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
            reviewsLog.setReviewSn(bo.getReviewSn());
            reviewsLog.setOperateContent("【" + compareVehicleData.getMismatchField() + "】信息不一致，原订单："++"OCR结果：" + com);
            reviewsLog.setOperator("system");
            reviewsLog.setType("create");
            aftersalesReviewsLogService.create(reviewsLog);
        }
    }

    private VehicleDataComparisonResultBO compareVehicleData(AftersalesReviewsVehicles reviewData, AftersalesReviewsVehicles orderData) {
        if (reviewData == null || orderData == null) {
            return new VehicleDataComparisonResultBO(false, "数据对象", "null", "null");
        }
        
        // 比较行驶证关键字段
        if (!Objects.equals(reviewData.getPlateNo(), orderData.getPlateNo())) {
            return new VehicleDataComparisonResultBO(false, "车牌号", 
                reviewData.getPlateNo(), orderData.getPlateNo());
        }
        if (!Objects.equals(reviewData.getPlateColor(), orderData.getPlateColor())) {
            return new VehicleDataComparisonResultBO(false, "车牌颜色", 
                reviewData.getPlateColor().toString(), orderData.getPlateColor().toString());
        }
        if (!Objects.equals(reviewData.getOwner(), orderData.getOwner())) {
            return new VehicleDataComparisonResultBO(false, "车主", 
                reviewData.getOwner(), orderData.getOwner());
        }
        if (!Objects.equals(reviewData.getType(), orderData.getType())) {
            return new VehicleDataComparisonResultBO(false, "车辆类型", 
                reviewData.getType(), orderData.getType());
        }
        if (!Objects.equals(reviewData.getUseCharacter(), orderData.getUseCharacter())) {
            return new VehicleDataComparisonResultBO(false, "使用性质", 
                reviewData.getUseCharacter(), orderData.getUseCharacter());
        }
        if (!Objects.equals(reviewData.getPassengers(), orderData.getPassengers())) {
            return new VehicleDataComparisonResultBO(false, "核定载客", 
                reviewData.getPassengers(), orderData.getPassengers());
        }
        
        return new VehicleDataComparisonResultBO(true, null, null, null);
    }
}
