package com.ets.apply.application.common.consts.reviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ReviewsReviewStatusEnum {

    REVIEW_STATUS_WAIT(0, "待审核"),
    REVIEW_STATUS_PROCESSING(1, "审核中"),
    REVIEW_STATUS_PASS(2, "审核通过"),
    REVIEW_STATUS_REFUSE(3, "审核拒绝"),
    REVIEW_STATUS_CANCEL(4, "审核取消");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(ReviewsReviewStatusEnum.values()).collect(Collectors.toMap(ReviewsReviewStatusEnum::getValue, ReviewsReviewStatusEnum::getDesc));
    }
}
