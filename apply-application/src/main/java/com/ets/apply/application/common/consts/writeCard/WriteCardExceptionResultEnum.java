package com.ets.apply.application.common.consts.writeCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum WriteCardExceptionResultEnum {
    CONFIRM(0, "确认圈存"),
    CANCEL(1, "取消圈存"),
    MANUAL_HANDLE(2, "人工处理");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        WriteCardExceptionResultEnum[] enums = WriteCardExceptionResultEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
