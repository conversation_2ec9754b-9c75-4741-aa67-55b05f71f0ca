package com.ets.apply.application.app.factory.statistic;

import com.ets.apply.application.app.factory.statistic.impl.StatisticBank;
import com.ets.apply.application.app.factory.statistic.impl.StatisticBase;
import com.ets.apply.application.app.factory.statistic.impl.StatisticOrder;
import com.ets.apply.application.app.factory.statistic.impl.StatisticProductOrder;
import com.ets.common.SpringBeanHelper;
import com.ets.common.ToolsHelper;

public class StatisticFactory {
    public static StatisticBase create(String classStr) {
        switch (classStr){
            case "order":
                return SpringBeanHelper.getBean(StatisticOrder.class);
            case "bank":
                return SpringBeanHelper.getBean(StatisticBank.class);
            case "productOrder":
                return SpringBeanHelper.getBean(StatisticProductOrder.class);
            default:
                ToolsHelper.throwException("类型不存在："+classStr);
                break;
        }
        return null;

    }
}
