package com.ets.apply.application.common.vo.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MapRecommendListVO {
    private Integer id;
    /**
     * 模块
     */
    private String type;


    /**
     * 模块名称
     */
    private String recommendKey;

    private String keyDesc;
    /**
     * 模块类型
     */
    private String recommendValues;

    private String recommendValuesStr;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private String statusStr;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
