package com.ets.apply.application.common.vo.creditCardBank;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class CreditCardBankDetailVO {
    private Integer id;

    /**
     * 银行id
     */
    private Integer bankId;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 标签
     */
    private List<Object> tagList;

    /**
     * 主权益
     */
    private String welfare;

    /**
     * 权益说明
     */
    private List<Object> welfareDetail;

    /**
     * 附加信息
     */
    private Object extraInfo;

    /**
     * 上下架状态[0-下架 1-上架]
     */
    private Integer bankStatus;

    /**
     * 银行类型[1-申办 2-活动]
     */
    private Integer bankType;

    private String operator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
