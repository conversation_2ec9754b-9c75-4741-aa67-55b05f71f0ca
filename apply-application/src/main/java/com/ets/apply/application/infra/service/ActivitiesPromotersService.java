package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.ActivitiesPromotersEntity;
import com.ets.apply.application.infra.mapper.ActivitiesPromotersMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广西地推用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Service
@DS("db-etc")
public class ActivitiesPromotersService extends BaseService<ActivitiesPromotersMapper, ActivitiesPromotersEntity> {

    public ActivitiesPromotersEntity getByUid(Long uid){
        Wrapper<ActivitiesPromotersEntity> wrapper = Wrappers.<ActivitiesPromotersEntity>lambdaQuery()
                .eq(ActivitiesPromotersEntity::getUid, uid)
                .last("LIMIT 1");

        return super.baseMapper.selectOne(wrapper);
    }

}
