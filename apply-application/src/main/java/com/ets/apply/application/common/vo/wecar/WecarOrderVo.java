package com.ets.apply.application.common.vo.wecar;
import lombok.Data;

@Data
public class WecarOrderVo {
    /*
     * ⻋牌号
     */
    private String vehicleNo;
    /*
     * ⻋订单状态 1: 待⽀付 2: 已取消 3: 已⽀
        付（可使⽤） 4: 已过期 5: 已完成（已
        使⽤） 6: 退款中 7: 退款失败 8: 已退
        款 9: 退款失败已处理(SP ⼿动退款成
        功) 10: 已点评扩展状态 21: 派单中 22:
        服务中(进⾏中) 32：SP申请退款中
        33: SP 处理失败(救援失败)34：SP已
        发货
     */
    private Integer status;
    /*
     * 发⾏商id
     */
    private String cardProviderId;
    /*
     * 发⾏商名称
     */
    private String cardProviderName;
    private Long createTime;
    private String spOrderId;
    private String applyProductId;
    private String applyProductName;

}
