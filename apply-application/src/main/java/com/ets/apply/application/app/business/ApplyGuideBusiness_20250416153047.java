package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionGroupVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultWithItemVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideTagVO;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;

import cn.hutool.core.bean.BeanUtil;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ApplyGuideBusiness {

    @Autowired
    private ApplyGuideQuestionService applyGuideQuestionService;

    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private ApplyGuideMapService applyGuideMapService;

    @Autowired
    private ApplyGuideResultService applyGuideResultService;

    @Autowired
    private ApplyGuideTagService applyGuideTagService;

    public List<ApplyGuideMapVO> getQuestionMap(Integer mapType) {
        if (mapType == null) {
            mapType = 1;
        }
        List<ApplyGuideMap> allNodes = applyGuideMapService.getNodeByType(mapType);
        if (ObjectUtils.isEmpty(allNodes)) {
            return new ArrayList<>();
        }

        // 收集所有问题ID
        List<Integer> questionIds = allNodes.stream()
                .map(ApplyGuideMap::getQuestionId)
                .collect(Collectors.toList());

        // 一次性查询所有问题
        LambdaQueryWrapper<ApplyGuideQuestion> wrapper = new LambdaQueryWrapper<ApplyGuideQuestion>()
                .in(ApplyGuideQuestion::getId, questionIds)
                .eq(ApplyGuideQuestion::getStatus, 1);
        List<ApplyGuideQuestion> questions = applyGuideQuestionService.list(wrapper);

        // 建立问题ID到问题的映射
        Map<Integer, ApplyGuideQuestion> questionMap = questions.stream()
                .collect(Collectors.toMap(ApplyGuideQuestion::getId, question -> question));

        // 按父节点ID分组
        Map<Integer, List<ApplyGuideMap>> parentMap = allNodes.stream()
                .collect(Collectors.groupingBy(ApplyGuideMap::getParentId));
 
        // 获取根节点（parentId为0的节点）
        List<ApplyGuideMap> rootNodes = parentMap.getOrDefault(0, new ArrayList<>());

        // 构建树状结构
        return rootNodes.stream()
                .map(root -> buildTree(root, parentMap, questionMap))
                .collect(Collectors.toList());
    }

    public List<ApplyGuideResultVO> getResultMap(Integer mapType) {
        if (mapType == null) {
            mapType = 1;
        }

        // 查询结果映射
        LambdaQueryWrapper<ApplyGuideResult> resultWrapper = new LambdaQueryWrapper<ApplyGuideResult>()
                .eq(ApplyGuideResult::getMapType, mapType);
        List<ApplyGuideResult> results = applyGuideResultService.list(resultWrapper);

        if (ObjectUtils.isEmpty(results)) {
            return new ArrayList<>();
        }

        // 收集所有产品包编号
        List<String> packageSns = results.stream()
                .map(ApplyGuideResult::getPackageSn)
                .collect(Collectors.toList());

        // 一次性查询所有产品包
        List<ProductPackageEntity> productPackages = productPackageService.getPackageBySns(packageSns);

        // 建立产品包编号到产品的映射
        Map<String, ProductPackageEntity> productMap = productPackages.stream()
                .collect(Collectors.toMap(ProductPackageEntity::getPackageSn, product -> product));

        // 构建返回结果
        return results.stream()
                .map(result -> {
                    ApplyGuideResultVO vo = new ApplyGuideResultVO();
                    vo.setId(result.getId());
                    vo.setQuestionMapGroup(result.getQuestionMapGroup());
                    vo.setPackageSn(result.getPackageSn());

                    // 设置产品信息
                    ProductPackageEntity productPackage = productMap.get(result.getPackageSn());
                    if (productPackage != null) {
                        JSONObject frontendConfig = JSON.parseObject(productPackage.getFrontedConfig());
                        JSONObject applyGuideInfo = frontendConfig.getJSONObject("applyGuideInfo");
                        vo.setProductTitle(frontendConfig.getString("title"));
                        if (ObjectUtils.isNotEmpty(applyGuideInfo.getString("product_desc"))) {
                            vo.setProductDesc(Arrays.stream(applyGuideInfo.getString("product_desc").split("\n")).toList());
                        }
                        vo.setProductImgUrl(frontendConfig.getString("product_img"));
                        if (ObjectUtils.isNotEmpty(applyGuideInfo.getString("product_tag"))) {
                            vo.setProductTag(Arrays.stream(applyGuideInfo.getString("product_tag").split("\\|")).toList());
                        }
                        vo.setDiscountTag(applyGuideInfo.getString("discount_tag"));
                        vo.setPageUrl(applyGuideInfo.getString("page_url"));
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    private ApplyGuideMapVO buildTree(ApplyGuideMap node, Map<Integer, List<ApplyGuideMap>> parentMap, Map<Integer, ApplyGuideQuestion> questionMap) {
        ApplyGuideMapVO vo = new ApplyGuideMapVO();
        vo.setId(node.getId());
        vo.setLevel(node.getLevel());
        vo.setSortOrder(node.getSortOrder());
        vo.setDefaultChoose(node.getDefaultChoose());
        vo.setPageUrl(node.getPageUrl());

        // 从映射中获取问题
        ApplyGuideQuestion question = questionMap.get(node.getQuestionId());
        if (ObjectUtils.isNotEmpty(question)) {
            vo.setQuestionId(question.getId());
            vo.setTitle(question.getTitle());
            vo.setContent(question.getContent());
            vo.setTag(question.getTag());
            vo.setIconUrl(question.getIconUrl());
            vo.setTagIconUrl(question.getTagIconUrl());
            vo.setSelectType(question.getSelectType());
        }

        // 获取子节点
        List<ApplyGuideMap> children = parentMap.getOrDefault(node.getId(), new ArrayList<>());
        if (!children.isEmpty()) {
            vo.setChildren(children.stream()
                    .map(child -> buildTree(child, parentMap, questionMap))
                    .collect(Collectors.toList()));
        }

        return vo;
    }

    /**
     * 根据地图类型和问题类型列表获取问题组列表
     *
     * @param mapType 地图类型
     * @param questionTypeList 问题类型列表
     * @return 问题组列表
     */
    public List<ApplyGuideQuestionGroupVO> getQuestionGroupList(Integer mapType, List<Integer> questionTypeList) {
        if (mapType == null) {
            return new ArrayList<>();
        }

        // 1. 先获取地图节点
        List<ApplyGuideMap> allNodes = applyGuideMapService.getNodeByType(mapType);
        if (ObjectUtils.isEmpty(allNodes)) {
            return new ArrayList<>();
        }

        // 2. 获取所有相关的问题
        List<Integer> questionIds = allNodes.stream()
                .map(ApplyGuideMap::getQuestionId)
                .collect(Collectors.toList());
        
        Map<Integer, ApplyGuideQuestion> questionMap = applyGuideQuestionService.listByIds(questionIds).stream()
                .filter(q -> questionTypeList.contains(q.getQuestionType()))
                .collect(Collectors.toMap(ApplyGuideQuestion::getId, q -> q));

        // 3. 按问题类型分组
        Map<Integer, List<ApplyGuideMap>> questionTypeGroups = allNodes.stream()
                .filter(node -> questionMap.containsKey(node.getQuestionId()))
                .collect(Collectors.groupingBy(node -> {
                    ApplyGuideQuestion question = questionMap.get(node.getQuestionId());
                    return question.getQuestionType();
                }));

        // 4. 构建返回结果
        return questionTypeGroups.entrySet().stream()
                .map(entry -> {
                    ApplyGuideQuestionGroupVO groupVO = new ApplyGuideQuestionGroupVO();
                    groupVO.setQuestionType(entry.getKey());

                    // 获取该组下所有问题
                    List<ApplyGuideQuestionVO> questions = entry.getValue().stream()
                            .map(node -> {
                                ApplyGuideQuestion question = questionMap.get(node.getQuestionId());
                                ApplyGuideQuestionVO vo = new ApplyGuideQuestionVO();
                                vo.setId(node.getId());
                                vo.setQuestionId(question.getId());
                                vo.setTitle(question.getTitle());
                                vo.setContent(question.getContent());
                                vo.setTag(question.getTag());
                                vo.setIconUrl(question.getIconUrl());
                                vo.setTagIconUrl(question.getTagIconUrl());
                                vo.setSelectType(question.getSelectType());
                                vo.setSortOrder(node.getSortOrder());
                                vo.setDefaultChoose(node.getDefaultChoose());
                                vo.setPageUrl(node.getPageUrl());
                                return vo;
                            })
                            .sorted(Comparator.comparing(ApplyGuideQuestionVO::getSortOrder))
                            .collect(Collectors.toList());

                    groupVO.setQuestions(questions);
                    return groupVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据地图类型和问题组合获取结果和问题项
     *
     * @param mapType 地图类型
     * @param questionMapGroup 问题组合
     * @return ApplyGuideResultWithItemVO
     */
    public ApplyGuideResultWithItemVO getResultWithItemMap(Integer mapType, String questionMapGroup) {
        // 1. 查询结果
        LambdaQueryWrapper<ApplyGuideResult> resultWrapper = new LambdaQueryWrapper<ApplyGuideResult>()
                .eq(ApplyGuideResult::getMapType, mapType)
                .eq(ApplyGuideResult::getQuestionMapGroup, questionMapGroup);
        ApplyGuideResult result = applyGuideResultService.getOne(resultWrapper);
        if (result == null) {
            return null;
        }

        // 2. 获取产品包信息
        ProductPackageEntity productPackage = productPackageService.getBySn(result.getPackageSn());
        if (productPackage == null) {
            return null;
        }

        // 3. 构建返回结果
        ApplyGuideResultWithItemVO vo = new ApplyGuideResultWithItemVO();
        vo.setId(result.getId());
        vo.setQuestionMapGroup(result.getQuestionMapGroup());
        vo.setPackageSn(result.getPackageSn());

        // 4. 设置产品信息
        JSONObject frontendConfig = JSON.parseObject(productPackage.getFrontedConfig());
        vo.setProductTitle(frontendConfig.getString("title"));
        vo.setProductImgUrl(frontendConfig.getString("product_img"));
        vo.setPackageFee(frontendConfig.getBigDecimal("fee"));
        vo.setOriginalPrice(frontendConfig.getBigDecimal("ori_fee"));

        // 4.1 设置标签列表
        if (ObjectUtils.isNotEmpty(result.getTagIds())) {
            List<Integer> tagIds = Arrays.stream(result.getTagIds().split("\\|"))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            
            if (!tagIds.isEmpty()) {
                List<ApplyGuideTag> tags = applyGuideTagService.listByIds(tagIds);
                List<ApplyGuideTagVO> tagVOs = tags.stream()
                        .map(tag -> BeanUtil.copyProperties(tag, ApplyGuideTagVO.class))
                        .collect(Collectors.toList());
                vo.setTagList(tagVOs);
            }
        }

        // 5. 获取问题项列表
        List<ApplyGuideMap> allNodes = applyGuideMapService.getNodeByType(result.getItemMapType());
        if (!ObjectUtils.isEmpty(allNodes)) {
            // 收集所有问题ID
            List<Integer> questionIds = allNodes.stream()
                    .map(ApplyGuideMap::getQuestionId)
                    .collect(Collectors.toList());

            // 一次性查询所有问题
            LambdaQueryWrapper<ApplyGuideQuestion> wrapper = new LambdaQueryWrapper<ApplyGuideQuestion>()
                    .in(ApplyGuideQuestion::getId, questionIds)
                    .eq(ApplyGuideQuestion::getStatus, 1);
            List<ApplyGuideQuestion> questions = applyGuideQuestionService.list(wrapper);

            // 建立问题ID到问题的映射
            Map<Integer, ApplyGuideQuestion> questionMap = questions.stream()
                    .collect(Collectors.toMap(ApplyGuideQuestion::getId, question -> question));

            // 获取已选择的问题ID列表
            List<String> chooseQuestionIds = ObjectUtils.isNotEmpty(result.getChooseQuestionIds()) 
                ? Arrays.asList(result.getChooseQuestionIds().split("\\|"))
                : new ArrayList<>();

            // 构建问题项列表
            List<ApplyGuideMapVO> questionItems = allNodes.stream()
                    .map(node -> {
                        ApplyGuideMapVO mapVO = new ApplyGuideMapVO();
                        mapVO.setId(node.getId());
                        mapVO.setLevel(node.getLevel());
                        mapVO.setSortOrder(node.getSortOrder());
                        // 根据 chooseQuestionIds 设置 defaultChoose
                        mapVO.setDefaultChoose(chooseQuestionIds.contains(node.getQuestionId().toString()) ? 1 : 0);
                        mapVO.setPageUrl(node.getPageUrl());

                        // 从映射中获取问题
                        ApplyGuideQuestion question = questionMap.get(node.getQuestionId());
                        if (ObjectUtils.isNotEmpty(question)) {
                            mapVO.setQuestionId(question.getId());
                            mapVO.setTitle(question.getTitle());
                            mapVO.setContent(question.getContent());
                            mapVO.setTag(question.getTag());
                            mapVO.setIconUrl(question.getIconUrl());
                            mapVO.setTagIconUrl(question.getTagIconUrl());
                            mapVO.setSelectType(question.getSelectType());
                        }

                        return mapVO;
                    })
                    .collect(Collectors.toList());

            vo.setQuestionItems(questionItems);
        }

        return vo;
    }
}
