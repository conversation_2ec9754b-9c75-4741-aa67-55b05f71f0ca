package com.ets.apply.application.common.dto.request.applyPage;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class ApplyPageSetStatusDTO {

    /**
     * 订单状态
     */
    @Min(1)
    @Max(2)
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     *
     */

    @NotBlank(message = "页面sn不能为空")
    private String pageSn;
}
