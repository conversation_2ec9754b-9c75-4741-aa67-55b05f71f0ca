package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.infra.entity.ApplyGuideMap;
import com.ets.apply.application.infra.mapper.ApplyGuideMapMapper;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 申办指引问题产品映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Service
@DS("db-etc")
public class ApplyGuideMapService extends BaseService<ApplyGuideMapMapper, ApplyGuideMap> {

    public List<ApplyGuideMap> getNodeByType(Integer mapType){
         // 获取所有节点
         LambdaQueryWrapper<ApplyGuideMap> wrapper = new LambdaQueryWrapper<>();
         wrapper.eq(ApplyGuideMap::getMapType, mapType)
                 .orderByAsc(ApplyGuideMap::getSortOrder);
         return this.list(wrapper);
    }
}
