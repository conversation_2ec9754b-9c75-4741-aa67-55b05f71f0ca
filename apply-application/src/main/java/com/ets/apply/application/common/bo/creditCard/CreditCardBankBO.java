package com.ets.apply.application.common.bo.creditCard;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
public class CreditCardBankBO {


    /**
     * 银行进件日期
     */
    private LocalDate bankRegisterDate;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 激活时间
     */
    private LocalDateTime activateTime;

    /**
     * 是否新户 1-是 2-否
     */
    private Integer isNewUser;





}
