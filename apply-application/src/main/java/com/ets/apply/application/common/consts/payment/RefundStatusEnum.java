package com.ets.apply.application.common.consts.payment;

import com.ets.apply.application.common.consts.productOrder.ChannelProductOrderStatusEnum;
import com.ets.common.BizException;
import lombok.Getter;

/**
 * 支付网关的退款枚举
 */
@Getter
public enum RefundStatusEnum {
    REFUND_WAIT(1, "待退款"),
    REFUND_DOING(2, "退款中"),
    REFUND_SUCCESS(3, "退款成功"),
    REFUND_FAIL(4, "退款失败"),
    ;


    public static RefundStatusEnum of(Integer value) {
        for (RefundStatusEnum anEnum : RefundStatusEnum.values()) {
            if (value.equals(anEnum.getValue())) {
                return anEnum;
            }
        }
        throw new BizException("尚未匹配到支付网关退款状态:" + value);
    }


    public ChannelProductOrderStatusEnum toChannelProductOrderRefundStatusEnum(){
        switch (this) {
            case REFUND_FAIL:
                return ChannelProductOrderStatusEnum.REFUND_FAILED;
            case REFUND_SUCCESS:
                return ChannelProductOrderStatusEnum.REFUNDED;
            default:
                throw new BizException("只处理退款成功、失败的状态:" + value);
        }
    }



    private final Integer value;
    private final String desc;


    RefundStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
