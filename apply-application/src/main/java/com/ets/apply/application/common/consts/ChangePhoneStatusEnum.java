package com.ets.apply.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ChangePhoneStatusEnum {

    STATUS_DEFAULT(0, "未更换"),
    STATUS_FAIL(4, "更换失败"),
    STATUS_SUCCESS(9, "更换成功"),
    ;

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(ChangePhoneStatusEnum.values()).collect(Collectors.toMap(ChangePhoneStatusEnum::getValue, ChangePhoneStatusEnum::getDesc));
    }
}
