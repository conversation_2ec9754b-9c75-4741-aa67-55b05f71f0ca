package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ReviewOrderLog;
import com.ets.apply.application.infra.mapper.ReviewOrderLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
@DS("db-order")
public class ReviewOrderLogService extends BaseService<ReviewOrderLogMapper, ReviewOrderLog> {

}
