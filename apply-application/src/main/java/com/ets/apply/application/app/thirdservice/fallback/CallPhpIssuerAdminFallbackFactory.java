package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CallPhpIssuerAdminFeign;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesOrderChangeDTO;
import com.ets.common.JsonResult;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;


@Component
public class CallPhpIssuerAdminFallbackFactory implements FallbackFactory<CallPhpIssuerAdminFeign> {
    @Override
    public CallPhpIssuerAdminFeign create(Throwable throwable) {
        return new CallPhpIssuerAdminFeign() {
            @Override
            public String orderChange(@RequestHeader(name = "ISSUER-CODE") String issuerCode, OrderAftersalesOrderChangeDTO dto) {
                return JsonResult.error("请求issuerAdmin服务异常").toString();
            }


        };
    }
}
