package com.ets.apply.application.common.dto.request.comment;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class CommentSubmitDTO {

    @Min(value = 1, message = "评星最小为1")
    @Max(value = 5, message = "评星最大为5")
    Integer stars;
    @NotEmpty(message = "车牌号码不能为空")
    String plateNo;
    @NotNull(message = "车牌颜色不能为空")
    Integer plateColor;
    Integer anonymous = 0;
    String pics = "";
    @Length(max = 500, message = "评论内容最多500字")
    String content;
}
