package com.ets.apply.application.infra.entity.aftersalesreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@TableName("etc_aftersales_reviews")
public class AftersalesReviews extends BaseEntity<AftersalesReviews> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 业务单号
     */
    private String orderSn;

    /**
     * 业务类型
     */
    private String orderType;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审核状态[0-待审核 1-审核通过 2-审核驳回 3-已取消]
     */
    private Integer reviewStatus;

    /**
     * 审核驳回状态编码
     */
    private String reviewCode;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    @TableField()
}
