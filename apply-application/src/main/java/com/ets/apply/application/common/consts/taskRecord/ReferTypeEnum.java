package com.ets.apply.application.common.consts.taskRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

@Getter
@AllArgsConstructor
public enum ReferTypeEnum {
    //申办订单相关  ---公共的
    TASK_ORDER_CANCEL("order_cancel", "取消订单"),
    TASK_FORCE_ORDER_CANCEL("force_order_cancel", "强制取消订单"),
    TASK_NOTIFY_ORDER_CANCEL("notify_order_cancel", "通知取消订单"),
    TASK_SUBMIT_LOGISTIC_ORDER_TO_ISSUER_ADMIN("submit_logistic_order_to_issuer_admin", "推送发货单"),
    TASK_NOTIFY_LOGISTIC_ORDER_SHIP("notify_logistic_order_ship", "通知发货"),
    TASK_NOTIFY_ORDER_AUDIT("notify_order_audit", "审核结果通知"),
    TASK_SUBMIT_REVIEW_ORDER_TO_ISSUER_ADMIN("submit_review_order_to_issuer_admin", "推送审核单"),
    TASK_PUSH_LOGISTIC_ORDER_TO_ISSUER("push_logistic_order_to_issuer", "送发卡方"),
    TASK_LOGISTICS_SENDBACK_JD_QUERY_ORDER("review_push_issuer", "推送发货单"),


    //申办订单相关  ---非公共的
    TASK_S2D_ADD_REFUND_PLAN("s2d_add_refund_plan", "储值卡转记账卡退款计划"),
    TASK_NOTIFY_CREDIT_APPLY_ACTIVATE("notify_credit_card_apply_activate", "信用卡订单激活通知"),
    TASK_JS_STORE_CARD_CANCEL("js_store_card_cancel", "江苏储值卡注销"),
    TASK_MP_NOTICE_NO_ACTIVE("mp_notice_no_active", "用户收货未激活"),
    TASK_MINI_NOTICE_NO_UPLOAD("mini_notice_no_upload", "用户未上传资料"),

    //北京货车改签相关
    TASK_CHANGE_SIGN_NOTICE_ISSUER("change_sign_notice_issuer", "改签通知卡方"),
    TASK_CHANGE_SIGN_NOTICE_USER("change_sign_notice_user", "通知用户改签"),
    TASK_CHANGE_SIGN_NOTICE_CONTRACT("change_sign_notice_contract", "通知用户签免密"),
    TASK_CHANGE_SIGN_INIT("change_sign_init", "改签订单初始化"),
    TASK_CHANGE_SIGN_BLACK("change_sign_black", "微众未改签用户拉黑"),
    TASK_CHANGE_SIGN_BLACK_V2("change_sign_black_v2", "微众未改签用户拉黑"),
    TASK_CHANGE_SIGN_PUT_UNSIGN_IN_BLACK("change_sign_put_unsign_in_black", "微众已改签未签约用户拉黑"),
    TASK_CHANGE_SIGN_MODIFY_TOLL_PAY_CHANNEL("change_sign_modify_toll_pay_channel", "修改改签后的扣费渠道"),
    TASK_CHANGE_SIGN_WEBANK_CONTRACT("change_sign_webank_contract", "未改签用户补全微众签约记录"),

    //其他
    TASK_CHANGE_WECHAT("change_wechat", "更换微信号"),
    TASK_WECHAT_AD_SEND_BACK("wechat_ad_send_back", "微信广告回传"),
    TASK_RECALL_CHECK_PLATE_NO("recall_check_plate_no", "用户召回校验车牌号码");



    private final String code;
    private final String description;


    public static String getDescByCode(String code) {
        for (ReferTypeEnum node : ReferTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static HashMap<String,String> getMap() {
        //创建map对象
        HashMap<String,String> referTypeMap = new HashMap<>();
        for (ReferTypeEnum value : ReferTypeEnum.values()) {
            referTypeMap.put(value.getCode(),value.getDescription());
        }

        return referTypeMap;
    }
}
