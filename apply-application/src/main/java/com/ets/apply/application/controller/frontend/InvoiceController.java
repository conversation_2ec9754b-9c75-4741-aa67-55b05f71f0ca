package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.invoice.InvoiceBusiness;
import com.ets.apply.application.common.dto.request.invoice.UserCardListDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.invoice.UserCardListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/frontend/invoice")
public class InvoiceController {

    @Autowired
    private InvoiceBusiness invoiceBusiness;

    @RequestMapping("/user-card-list")
    public JsonResult<List<UserCardListVO>> userCardList(){
        return JsonResult.ok(invoiceBusiness.getUserCardList(UserUtil.getUid()));
    }

 }
