package com.ets.apply.application.common.consts.recall;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RecallType {

    RECALL_WITH_GIFT(1, "召回赠送礼包"),
    RECALL_WITH_PLATE_OCCUPANCY(2, "车牌占用召回"),
    RECALL_WITH_CUIMI(3, "召回未访问用户催米召回"),
    RECALL_ORDER_ON_PLATE_OCCUPY(4, "车牌占用召回订单"),
    RECALL_TRUCK_APPLY(5, "货车申办召回用户申办");


    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (RecallType node : RecallType.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
