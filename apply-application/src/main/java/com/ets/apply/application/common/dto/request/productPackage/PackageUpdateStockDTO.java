package com.ets.apply.application.common.dto.request.productPackage;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class PackageUpdateStockDTO {

    @NotNull(message = "packageSn不可为空")
    private String packageSn;

    @NotNull(message = "operate不可为空")
    private String operate;

    @NotNull(message = "stock不可为空")
    private Integer stock;

    @NotNull(message = "msg不可为空")
    private String msg;
}
