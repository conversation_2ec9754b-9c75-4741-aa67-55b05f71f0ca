package com.ets.apply.application.app.business.map;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.service.common.notice.TemplateMsgService;
import com.ets.apply.application.common.consts.map.MapStatusEnum;
import com.ets.apply.application.common.consts.order.FlowTypeEnum;
import com.ets.apply.application.common.consts.order.PurchasePartyEnum;
import com.ets.apply.application.common.consts.order.PurchaseTypeEnum;
import com.ets.apply.application.common.consts.productPackage.DeviceTypeEnum;
import com.ets.apply.application.common.consts.productPackage.ManufacturerEnum;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.vo.map.MapConfigVO;
import com.ets.apply.application.common.vo.map.MapModuleDetailVO;
import com.ets.apply.application.common.vo.map.MapModulePackageListVO;
import com.ets.apply.application.common.vo.productOrder.ProductOrderLogListVO;
import com.ets.apply.application.infra.entity.CardsEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.entity.map.MapAssignEntity;
import com.ets.apply.application.infra.entity.map.MapLogEntity;
import com.ets.apply.application.infra.entity.map.MapPackageSnEntity;
import com.ets.apply.application.infra.entity.mapModule.MapModuleEntity;
import com.ets.apply.application.infra.entity.mapRule.MapRuleCombineEntity;
import com.ets.apply.application.infra.entity.mapRule.MapRuleItemEntity;
import com.ets.apply.application.infra.service.*;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ets.apply.application.common.vo.map.MapModuleListVO;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Component
public class MapModuleBusiness {
    @Autowired
    private MapModuleService mapModuleService;
    @Autowired
    private MapAssignService mapAssignService;

    @Autowired
    private MapPackageSnService mapPackageSnService;
    @Autowired
    private ProductPackageService productPackageService;
    @Autowired
    private MapRuleCombineService mapRuleCombineService;
    @Autowired
    private MapRuleItemService mapRuleItemService;
    @Autowired
    private MapBusiness mapBusiness;
    @Autowired
    private MapConfigBusiness mapConfigBusiness;
    @Autowired
    private MapLogService mapLogService;

    /*
     * 获取全部模块列表
     */
    public IPage<MapModuleListVO> getAllList(MapModuleDTO dto) {
        // 分页设置
        IPage<MapModuleEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<MapModuleEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MapModuleEntity::getParentModule, dto.getParentModule())
               .orderByDesc(MapModuleEntity::getSort);
        IPage<MapModuleEntity> pageList = mapModuleService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            MapModuleListVO vo = new MapModuleListVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }

    /**
     * 获取模块详情
     * @param dto
     * @return
     */
    public MapModuleDetailVO getModuleDetail(MapModuleDetailDTO dto) {
        MapModuleEntity mapModuleEntity = mapModuleService.getInfoByModule(dto.getModule());
        if (ObjectUtil.isEmpty(mapModuleEntity)) {
            return null;
        }
        // 校验多一下，目前按模块去获取详情
        if (!mapModuleEntity.getId().equals(dto.getId())) {
            ToolsHelper.throwException("模块信息错误");
        }
        MapModuleDetailVO vo = new MapModuleDetailVO();
        BeanUtils.copyProperties(mapModuleEntity, vo);
        return vo;
    }

    /*
     * 获取全部产品包列表
     */
    public IPage<MapModulePackageListVO> getPackageList(MapPackageDTO dto) {
        // 分页设置
        IPage<MapPackageSnEntity> oPage = new Page<>(1, 100000, true);

        // 查询条件设置
        LambdaQueryWrapper<MapPackageSnEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MapPackageSnEntity::getObjectType, dto.getObjectType())
            .eq(StringUtils.isNotEmpty(dto.getPackageSn()),MapPackageSnEntity::getPackageSn, dto.getPackageSn())
            .eq(dto.getLabelId()>0,MapPackageSnEntity::getLabelId, dto.getLabelId())
            .eq(MapPackageSnEntity::getObjectId, dto.getObjectId())
            .eq(MapPackageSnEntity::getStatus, MapStatusEnum.STAUTS_NORMAL.getCode())
            .orderByDesc(MapPackageSnEntity::getSort);
        IPage<MapPackageSnEntity> pageList = mapPackageSnService.getPageListByWrapper(oPage, wrapper);

        //通过objectType和objectId
        String moduleName = "";
        switch (dto.getObjectType()){
            case "module":
                MapModuleEntity mapModuleEntity = mapModuleService.getById(dto.getObjectId());
                moduleName = mapModuleEntity == null ? "":mapModuleEntity.getModuleName();
                break;
            case "assign":
            case "assignAlternative":
                MapAssignEntity mapAssignEntity = mapAssignService.getById(dto.getObjectId());
                moduleName = mapAssignEntity == null ? "":mapAssignEntity.getAssignKeyName();
                break;

        }
        String finalModuleName = moduleName;

        //获取标签
        MapConfigDTO mapConfigDTO = new MapConfigDTO();
        mapConfigDTO.setConfigKey("labels");
        MapConfigVO mapConfigVo = mapConfigBusiness.getInfoByKey(mapConfigDTO);
        //log.info(mapConfigVo.toString());
        return pageList.convert(record-> {
            MapModulePackageListVO vo = new MapModulePackageListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setModuleName(finalModuleName);
            //产品包名称
            ProductPackageEntity productPackageEntity = productPackageService.getBySn(record.getPackageSn());
            vo.setPackageName(productPackageEntity==null ? "" : productPackageEntity.getPackageName());
            if(JSON.parseArray(vo.getCombineRules()).size() > 0 ){
                //combineRulesList
                ArrayList combineRulesList = new ArrayList();
                List<MapRuleCombineEntity> combineEntities = mapRuleCombineService.getListBySns(JSON.parseArray(vo.getCombineRules()),MapRuleCombineEntity::getId);
                for (MapRuleCombineEntity mapRuleCombineEntity: combineEntities) {
                    MapModulePackageListVO.OptionsVo optionsVo = new MapModulePackageListVO.OptionsVo();
                    optionsVo.setId(mapRuleCombineEntity.getId());
                    optionsVo.setName(mapRuleCombineEntity.getCombineName());
                    combineRulesList.add(optionsVo);
                }
                vo.setCombineRulesList(combineRulesList);
            }

            if(JSON.parseArray(vo.getItemRules()).size() > 0){
                //itemRulesList
                ArrayList itemRulesList = new ArrayList();
                List<MapRuleItemEntity> itemEntities = mapRuleItemService.getListBySns(JSON.parseArray(vo.getItemRules()),MapRuleItemEntity::getId);
                for (MapRuleItemEntity mapRuleItemEntity: itemEntities) {
                    MapModulePackageListVO.OptionsVo optionsVo = new MapModulePackageListVO.OptionsVo();
                    optionsVo.setId(mapRuleItemEntity.getId());
                    optionsVo.setName(mapRuleItemEntity.getItemName());
                    itemRulesList.add(optionsVo);
                }
                vo.setItemRulesList(itemRulesList);
            }
            if(vo.getLabelId() > 0){
                mapConfigVo.getConfigValuesObject().forEach(item ->{
                    //log.info(item.toString());
                    if(((JSONObject) item).getInteger("key").equals(vo.getLabelId())){
                        vo.setLabelStr(((JSONObject) item).getString("value"));
                    }
                });


            }

            return vo;
        });
    }

    /*
     * 新增产品包
     */
    public Boolean addPackage(MapModuleAddPackageDTO dto,String loginCode){
        MapPackageSnEntity mapPackageSnEntity = new MapPackageSnEntity();
        mapPackageSnEntity.setObjectType(dto.getObjectType());
        mapPackageSnEntity.setObjectId(dto.getObjectId());
        mapPackageSnEntity.setPackageSn(dto.getPackageSn());
        mapPackageSnEntity.setSort(dto.getSort());
        mapPackageSnEntity.setLabelId(dto.getLabelId());
        mapPackageSnEntity.setCombineRules(JSON.toJSONString(dto.getCombineRules().length>0?dto.getCombineRules():new Integer[0]));
        mapPackageSnEntity.setItemRules(JSON.toJSONString(dto.getItemRules().length>0?dto.getItemRules():new Integer[0]));
        mapPackageSnEntity.setOperator(loginCode);
        mapPackageSnService.create(mapPackageSnEntity);

        //记录日志
        mapLogService.addLog(
            loginCode,
            mapPackageSnEntity.getObjectType(),
            mapPackageSnEntity.getObjectId(),
            "addPackage",
            "新增产品包",
            "",
            JSONObject.toJSONString(dto)
        );
        //更新缓存
        mapBusiness.updatePreviewCache(mapPackageSnEntity.getId());

        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean updatePackage(MapModuleAddPackageDTO dto,String loginCode){
        //事务处理
        updatePackageTransaction(dto,loginCode);
        //更新缓存
        mapBusiness.updatePreviewCache(dto.getId());
        return true;
    }

    @DSTransactional
    public Boolean updatePackageTransaction(MapModuleAddPackageDTO dto,String loginCode){
        MapPackageSnEntity mapPackageSnEntity = mapPackageSnService.getById(dto.getId());
        if(mapPackageSnEntity == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        //对比数据
        JSONObject compareDifffObject = compareDiff(mapPackageSnEntity,dto);
        if((compareDifffObject.getJSONObject("diff")).isEmpty()){
            ToolsHelper.throwException("数据无变更");
        }
        LambdaUpdateWrapper<MapPackageSnEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapPackageSnEntity::getId, dto.getId())
            .set(dto.getStatus()>0,MapPackageSnEntity::getStatus, dto.getStatus())
            .set(dto.getSort()>0,MapPackageSnEntity::getSort, dto.getSort())
            .set(dto.getLabelId()!=null,MapPackageSnEntity::getLabelId, dto.getLabelId())
            .set(StringUtils.isNotEmpty(loginCode),MapPackageSnEntity::getOperator, loginCode)
            .set(StringUtils.isNotEmpty(dto.getPackageSn()),MapPackageSnEntity::getPackageSn, dto.getPackageSn())
            .set(StringUtils.isNotEmpty(JSON.toJSONString(dto.getCombineRules())),MapPackageSnEntity::getCombineRules, JSON.toJSONString(dto.getCombineRules()))
            .set(StringUtils.isNotEmpty(JSON.toJSONString(dto.getItemRules())),MapPackageSnEntity::getItemRules,JSON.toJSONString(dto.getItemRules()))
            .set(MapPackageSnEntity::getUpdatedAt, LocalDateTime.now());
        mapPackageSnService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
            loginCode,
            mapPackageSnEntity.getObjectType(),
            mapPackageSnEntity.getObjectId(),
            "updatePackage",
            "更新产品包",
            (compareDifffObject.getJSONObject("preOperate")).toJSONString(),
            (compareDifffObject.getJSONObject("afterOperate")).toJSONString()
        );
        return true;
    }

    public JSONObject compareDiff(MapPackageSnEntity mapPackageSnEntity,MapModuleAddPackageDTO dto){

        JSONObject preOperate = new JSONObject();
        JSONObject afterOperate = new JSONObject();
        JSONObject diff = new JSONObject();
        preOperate.put("id",mapPackageSnEntity.getId());
        preOperate.put("package_sn",mapPackageSnEntity.getPackageSn());
        //状态
        if(!mapPackageSnEntity.getStatus().equals(dto.getStatus())){
            preOperate.put("status",mapPackageSnEntity.getStatus());
            afterOperate.put("status",dto.getStatus());
            diff.put("status","{\"old\":"+mapPackageSnEntity.getStatus()+",\"new\":" + dto.getStatus() + "}");
        }
        //排序
        if(!mapPackageSnEntity.getSort().equals(dto.getSort())){
            preOperate.put("sort",mapPackageSnEntity.getSort());
            afterOperate.put("sort",dto.getSort());
            diff.put("sort","{\"old\":"+mapPackageSnEntity.getSort()+",\"new\":" + dto.getSort() + "}");
        }
        //标签
        if(!mapPackageSnEntity.getLabelId().equals(dto.getLabelId())){
            preOperate.put("label_id",mapPackageSnEntity.getLabelId());
            afterOperate.put("label_id",dto.getLabelId());
            diff.put("label_id","{\"old\":"+mapPackageSnEntity.getLabelId()+",\"new\":" + dto.getLabelId() + "}");
        }
        //package_sn
        if(!mapPackageSnEntity.getPackageSn().equals(dto.getPackageSn())){
            preOperate.put("package_sn",mapPackageSnEntity.getPackageSn());
            afterOperate.put("package_sn",dto.getPackageSn());
            diff.put("package_sn","{\"old\":"+mapPackageSnEntity.getPackageSn()+",\"new\":" + dto.getPackageSn() + "}");
        }
        //item_rules
        String newItemRules = JSON.toJSONString(dto.getItemRules());
        if(!mapPackageSnEntity.getItemRules().isEmpty()){
            JSONArray oldItemRules = JSON.parseArray(mapPackageSnEntity.getItemRules());
            Integer[] oldArray = new Integer[oldItemRules.size()];
            for(int i=0;i<oldItemRules.size();i++){
                oldArray[i] = oldItemRules.getInteger(i);
            }
            if(!Arrays.equals(oldArray,dto.getItemRules())){
                preOperate.put("item_rules",mapPackageSnEntity.getItemRules());
                afterOperate.put("item_rules",newItemRules);
                diff.put("item_rules","{\"old\":"+mapPackageSnEntity.getItemRules()+",\"new\":" + newItemRules + "}");
            }

        }else{
            //旧数据为空
            if(dto.getItemRules().length > 0 ){
                preOperate.put("item_rules",mapPackageSnEntity.getItemRules());
                afterOperate.put("item_rules",newItemRules);
                diff.put("item_rules","{\"old\":"+mapPackageSnEntity.getItemRules()+",\"new\":" + newItemRules + "}");
            }
        }

        //combine_rules
        String newCombineRules = JSON.toJSONString(dto.getCombineRules());

        if(!mapPackageSnEntity.getCombineRules().isEmpty()){
            JSONArray oldCombineRules = JSON.parseArray(mapPackageSnEntity.getCombineRules());
            Integer[] oldCombineRulesArray = new Integer[oldCombineRules.size()];
            for(int i=0;i<oldCombineRules.size();i++){
                oldCombineRulesArray[i] = oldCombineRules.getInteger(i);
            }
            if(!Arrays.equals(oldCombineRulesArray,dto.getCombineRules())){
                preOperate.put("combine_rules",mapPackageSnEntity.getCombineRules());
                afterOperate.put("combine_rules",newCombineRules);
                diff.put("combine_rules","{\"old\":"+mapPackageSnEntity.getCombineRules()+",\"new\":" + newCombineRules + "}");
            }
        }else{
            //旧数据为空
            if(dto.getCombineRules().length > 0 ){
                preOperate.put("combine_rules",mapPackageSnEntity.getCombineRules());
                afterOperate.put("combine_rules",newCombineRules);
                diff.put("combine_rules","{\"old\":"+mapPackageSnEntity.getCombineRules()+",\"new\":" + newCombineRules + "}");
            }
        }



        JSONObject returnObject = new JSONObject();
        returnObject.put("preOperate",preOperate);
        returnObject.put("afterOperate",afterOperate);
        returnObject.put("diff",diff);
        return returnObject;
    }

    /*
     * 更新产品包
     */
    public Boolean delPackage(MapModuleAddPackageDTO dto,String loginCode){
        //更新产品包
        delPackageTransaction(dto,loginCode);
        //更新缓存
        mapBusiness.updatePreviewCache(dto.getId());
        return true;
    }
    @DSTransactional
    public Boolean delPackageTransaction(MapModuleAddPackageDTO dto,String loginCode){
        MapPackageSnEntity mapPackageSnEntity = mapPackageSnService.getById(dto.getId());
        if(mapPackageSnEntity == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }

        LambdaUpdateWrapper<MapPackageSnEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapPackageSnEntity::getId, dto.getId())
                .set(MapPackageSnEntity::getOperator, loginCode)
                .set(MapPackageSnEntity::getUpdatedAt, LocalDateTime.now())
                .set(dto.getStatus()>0,MapPackageSnEntity::getStatus, dto.getStatus());
        mapPackageSnService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
                loginCode,
                mapPackageSnEntity.getObjectType(),
                mapPackageSnEntity.getObjectId(),
                "delPackage",
                "删除产品包",
                JSONObject.toJSONString(mapPackageSnEntity),
                ""
        );
        return true;
    }


    /*
     * 获取全部模块列表
     */
    public IPage<MapLogEntity> getLoglist(MapLogGetListDTO dto) {
        // 分页设置
        IPage<MapLogEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        // 查询条件设置
        LambdaQueryWrapper<MapLogEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MapLogEntity::getObjectId, dto.getObjectId())
            .eq(MapLogEntity::getObjectType, dto.getObjectType())
            .orderByDesc(MapLogEntity::getCreatedAt);
        IPage<MapLogEntity> pageList = mapLogService.getPageListByWrapper(oPage, wrapper);
        return pageList.convert(record -> {
            record.setPreOperate(logOutPutDeal(record.getPreOperate()));
            record.setAfterOperate(logOutPutDeal(record.getAfterOperate()));
            return record;
        });

    }
    /*
     *  操作日志输出转化
     */
    public String logOutPutDeal(String str){
        if(str.length() < 1){
            return "";
        }
        JSONObject strJsonObject = JSONObject.parseObject(str);
        Map<String, Object> outPut = new LinkedHashMap<>();
         if(ObjectUtils.isNotEmpty(strJsonObject)){
             String packageSn = strJsonObject.getString("package_sn");
             if (packageSn != null) {
                 outPut.put("产品包",packageSn);
             }
             Integer sort = strJsonObject.getInteger("sort");
             if(sort != null){
                 outPut.put("排序",sort);
             }
             Integer objectId = strJsonObject.getInteger("objectId");
             if(objectId != null){
                 outPut.put("objectId",objectId);
             }
             String packageSn1 = strJsonObject.getString("packageSn");
             if(packageSn1 != null){
                 outPut.put("产品包",packageSn1);
             }
             String assignKey = strJsonObject.getString("assignKey");
             if(assignKey != null){
                 outPut.put("指定Key",assignKey);
             }
             String assignKeyName = strJsonObject.getString("assignKeyName");
             if(assignKeyName != null){
                 outPut.put("指定Key名称",assignKeyName);
             }
             String message = strJsonObject.getString("message");
             if(message != null){
                 outPut.put("提示语",message);
             }
             Integer labelId = strJsonObject.getInteger("label_id");
             if (labelId != null) {
                 //获取标签
                 MapConfigDTO mapConfigDTO = new MapConfigDTO();
                 mapConfigDTO.setConfigKey("labels");
                 MapConfigVO mapConfigVo = mapConfigBusiness.getInfoByKey(mapConfigDTO);
                 mapConfigVo.getConfigValuesObject().forEach(item ->{
                     if(((JSONObject) item).getInteger("key").equals(labelId)){
                         outPut.put("标签",((JSONObject) item).getString("value"));
                     }
                 });

             }
            String itemRules = strJsonObject.getString("item_rules");
            if (JSON.parseArray(itemRules) != null) {
                ArrayList itemRulesList = new ArrayList();
                List<MapRuleItemEntity> itemEntities = mapRuleItemService.getListBySns(JSON.parseArray(itemRules),MapRuleItemEntity::getId);
                if(itemEntities != null){
                    for (MapRuleItemEntity mapRuleItemEntity: itemEntities) {
                        itemRulesList.add(mapRuleItemEntity.getItemName());
                    }
                    outPut.put("子规则",itemRulesList);
                }

            }
            String combineRules = strJsonObject.getString("combine_rules");
            if (JSON.parseArray(combineRules) != null) {
                ArrayList combineRulesList = new ArrayList();
                List<MapRuleCombineEntity> combineEntities = mapRuleCombineService.getListBySns(JSON.parseArray(combineRules),MapRuleCombineEntity::getId);
                if(combineEntities != null){
                    for (MapRuleCombineEntity mapRuleCombineEntity: combineEntities) {
                        combineRulesList.add(mapRuleCombineEntity.getCombineName());
                    }
                    outPut.put("组合规则",combineRulesList);
                }

            }

             Integer decorationType = strJsonObject.getInteger("decoration_type");
             if (!Objects.isNull(decorationType)) {
                 outPut.put("推荐功能", decorationType);
             }

         }
        return outPut.toString();
    }

    public void modifyDecorationType(MapModuleModifyDecorationTypeDTO dto) {
        MapModuleEntity mapModuleEntity = mapModuleService.getInfoByModule(dto.getModule());
        if (ObjectUtil.isEmpty(mapModuleEntity)) {
            ToolsHelper.throwException("模块不存在");
        }
        if (mapModuleEntity.getDecorationType().equals(dto.getDecorationType())) {
            return;
        }
        mapModuleEntity.setDecorationType(dto.getDecorationType());
        mapModuleService.updateById(mapModuleEntity);

    }

}
