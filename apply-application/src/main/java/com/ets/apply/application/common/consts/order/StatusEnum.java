package com.ets.apply.application.common.consts.order;

import com.ets.apply.application.common.consts.comm.ApplyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StatusEnum {
    WAIT_FOR_PAY(1, "待支付"),

    WAIT_FOR_DELIVER(2, "待发货"),

    WAIT_FOR_RECEIVE(3, "待收货"),

    FINISH(4, "已完成"),

    CANCELED(5, "已取消");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (StatusEnum node : StatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StatusEnum getNodeByCode(int status){
        for (StatusEnum node : StatusEnum.values()) {
            if (node.getCode() == status) {
                return node;
            }
        }
        return null;
    }
}
