package com.ets.apply.application.common.dto.request.productOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductOrderGoodsShipDTO {
    private String expressCompany;
    private String expressNumber;
    private String businessOrderSn;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expressTime;


}
