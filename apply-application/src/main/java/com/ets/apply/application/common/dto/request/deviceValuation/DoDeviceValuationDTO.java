package com.ets.apply.application.common.dto.request.deviceValuation;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DoDeviceValuationDTO {

    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    @NotNull(message = "车牌颜色不能为空")
    private Integer plateColor;

    /**
     * 设备类型 0 -普通插卡设备 1-充电 2-单卡设备
     */
    @NotNull(message = "设备类型不能为空")
    private Integer deviceType;

    /**
     * 0-不确定 1-小于两年 2-大于两年
     */
    @NotNull(message = "使用年限不能为空")
    private Integer useYear;

    /**
     * 0-不确定 1-正常使用 2-无法使用
     */
    @NotNull(message = "使用状态不能为空")
    private Integer useStatus;

    /**
     * 发行时间
     */
    private String issuedTime;
}
