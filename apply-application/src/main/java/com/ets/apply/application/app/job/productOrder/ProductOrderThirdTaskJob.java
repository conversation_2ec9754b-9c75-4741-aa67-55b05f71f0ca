package com.ets.apply.application.app.job.productOrder;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.productOrder.ProductOrderThirdTaskBusiness;
import com.ets.apply.application.common.bo.productOrderThirdTask.ProductOrderThirdTaskNotifyHandlerBO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class ProductOrderThirdTaskJob {

    @Autowired
    private ProductOrderThirdTaskBusiness productOrderThirdTaskBusiness;

    /**
     * 处理14 天内未通知完成的订单，并通知第三方
     */
    @XxlJob("thirdTaskNotifyHandler")

    public ReturnT<String> thirdTaskNotifyHandler(String params) {

        ProductOrderThirdTaskNotifyHandlerBO thirdTaskNotifyHandlerBO = JSONObject.parseObject(params,
                ProductOrderThirdTaskNotifyHandlerBO.class);
        if (ObjectUtil.isEmpty(thirdTaskNotifyHandlerBO.getStartTime()) || ObjectUtil.isEmpty(thirdTaskNotifyHandlerBO.getEndTime())) {
            // 获取开始时间和结束时间 默认间隔14 天的数据都获取
            LocalDateTime beginDateTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays());
            LocalDateTime endDateTime = LocalDateTime.now();
            thirdTaskNotifyHandlerBO.setStartTime(beginDateTime);
            thirdTaskNotifyHandlerBO.setEndTime(endDateTime);
        }
        productOrderThirdTaskBusiness.syncUnfinishedTask(thirdTaskNotifyHandlerBO);
        return ReturnT.SUCCESS;

    }

    /**
     * 大于14 天到1个月的数据再捞取进行通知
     */

    @XxlJob("thirdTaskNotifyGapDaysHandler")
    public ReturnT<String> thirdTaskNotifyGapDaysHandler(String params) {

        ProductOrderThirdTaskNotifyHandlerBO thirdTaskNotifyHandlerBO = JSONObject.parseObject(params,
                ProductOrderThirdTaskNotifyHandlerBO.class);

        LocalDateTime startTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays() + 30);
        LocalDateTime endTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays());

        thirdTaskNotifyHandlerBO.setStartTime(startTime);
        thirdTaskNotifyHandlerBO.setEndTime(endTime);
        productOrderThirdTaskBusiness.syncUnfinishedTask(thirdTaskNotifyHandlerBO);
        return ReturnT.SUCCESS;

    }
}
