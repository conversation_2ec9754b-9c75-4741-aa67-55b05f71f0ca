package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.allow-apply-product-package-rule")
public class AllowApplyProductPackageRule {

    private List<AllowRule> allowRules;

    // 新的公共匹配规则
    private AllowRule allowCommonRule;

    // 信用卡的校验规则
    private AllowRule allowCreditRule;

    @Data
    public static class AllowRule {
        private List<Integer> manufacturer;
        private List<Integer> issuerId;
        private List<Integer> deviceType;
        private List<Integer> bizType;
    }
}
