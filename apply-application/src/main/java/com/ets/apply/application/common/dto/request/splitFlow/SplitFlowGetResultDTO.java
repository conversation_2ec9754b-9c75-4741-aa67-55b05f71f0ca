package com.ets.apply.application.common.dto.request.splitFlow;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import jakarta.validation.constraints.NotNull;


@Data
public class SplitFlowGetResultDTO {
    /**
     * 分流类型[etc_2.0_before_pay-2.0支付前]
     */
    @Length(min = 1, max = 20, message = "分流类型请输入20个以内字符")
    private String splitType;

    /**
     * 分流的周期：20210420
     */
    private String splitTerm;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 非必填参数
     */
    private Integer pl;
    /**
     * 非必填参数
     */
    private String orderSn;
}
