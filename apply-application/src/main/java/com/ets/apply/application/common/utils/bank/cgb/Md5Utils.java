package com.ets.apply.application.common.utils.bank.cgb;

import java.security.MessageDigest;
import java.util.Base64;

/**
 * 计算md5值
 *
 * <AUTHOR>
 * @date 2022-07-07-12:16
 */

public class Md5Utils {

    private Md5Utils() {
    }

    public static String getMd5Hex(String pubKey) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(Base64.getDecoder().decode(pubKey));
        byte[] tmp = md5.digest();
        return byte2hex(tmp);
    }

    public static String byte2hex(byte[] tmp) {
        StringBuilder builder = new StringBuilder();
        String stmp;
        for (byte b : tmp) {
            stmp = Integer.toHexString(b & 0xFF).toUpperCase();
            if (stmp.length() == 1) {
                builder.append("0").append(stmp);
            } else {
                builder.append(stmp);
            }
        }
        return builder.toString();
    }
}

