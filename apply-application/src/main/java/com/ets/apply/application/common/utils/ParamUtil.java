package com.ets.apply.application.common.utils;

import java.time.LocalDate;
import java.time.Period;

public class ParamUtil {

    public static int getYearOfIdCard(String idCard) {

        String year = idCard.substring(6, 10);
        String month = idCard.substring(10, 12);
        String day = idCard.substring(12, 14);

        LocalDate birthDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), Integer.parseInt(day));
        LocalDate currentDate = LocalDate.now();

        Period age = Period.between(birthDate, currentDate);

        return age.getYears();
    }

}
