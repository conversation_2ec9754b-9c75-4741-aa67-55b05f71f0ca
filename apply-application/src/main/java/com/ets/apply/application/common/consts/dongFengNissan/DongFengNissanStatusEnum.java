package com.ets.apply.application.common.consts.dongFengNissan;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DongFengNissanStatusEnum {
    //订单状态 1: 待⽀付 2: 已取消 3: 已⽀付 4: 已过期 5: 已完成 6: 退款中 7: 退款失败 8: 已退款 9: 退款失败已处理(SP ⼿动退款成功)
    DEFAULT(0, "默认"),
    WAIT_PAY(1, "待支付"),
    CANCEL(2, "已取消"),
    PAID(3, "已支付"),
    EXPIRED(4, "已过期"),
    FINISHED(5, "已完成"),
    REFUNDING(6, "退款中"),
    REFUND_FAILED(7, "退款失败"),
    REFUNDED(8, "已退款"),
    REFUND_FAILED_PROCESSED(9, "退款失败已处理(SP 手动退款成功)");

    private final Integer value;
    private final String desc;

    /**
     * 根据产品订单状态获取订单状态
     * 支付、激活、关闭订单状态与订单状态一致，关闭时已退款，由程序另外处理退款状态
     */
    public static DongFengNissanStatusEnum ofProductOrderStatus(ProductOrderStatusEnum productOrderStatus){
        DongFengNissanStatusEnum status = DongFengNissanStatusEnum.DEFAULT;
        switch (productOrderStatus){
            case PAID:
            case SHIPPED:
            case RECEIVED:
                status = DongFengNissanStatusEnum.PAID;
                break;
            case ACTIVATED:
                status = DongFengNissanStatusEnum.FINISHED;
                break;
            case CLOSED:
                status = DongFengNissanStatusEnum.CANCEL;
                break;
            default:
                break;
        }
        return status;
    }
}
