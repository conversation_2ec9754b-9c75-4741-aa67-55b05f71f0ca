package com.ets.apply.application.common.vo.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MapRuleCombineListVO {
    private Integer id;
    /**
     * 模块
     */


    private Integer sort;
    /**
     * 模块名称
     */
    private String combineName;

    /*
     * 组合规则值
     */
    private String combineValues;

    private List<MapRuleCombineListVO.OptionsVo> itemRulesList;
    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private String statusStr;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String operator;

    @Data
    public static class OptionsVo {
        private Integer id;
        private String name;
    }
}
