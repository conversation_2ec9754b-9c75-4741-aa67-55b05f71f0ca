package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfReviewsReviewDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsDetailDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsDetailVO;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核管理
 */
@RestController
@RequestMapping("/admin/aftersales-reviews")
public class AdminAfterSalesReviewsController {

    @Autowired
    private AfterSalesReviewsBusiness afterSalesReviewsBusiness;

    @PostMapping("/get-list")
    public JsonResult<IPage<AdminAfterSalesReviewsListVO>> getList(@RequestBody @Valid AdminAfterSalesReviewsGetListDTO dto) {
        return JsonResult.ok(afterSalesReviewsBusiness.getList(dto));
    }

    @PostMapping("/get-detail")
    public JsonResult<AdminAfterSalesReviewsDetailVO> getDetail(@RequestBody @Valid AdminAfterSalesReviewsDetailDTO dto) {
        return JsonResult.ok(afterSalesReviewsBusiness.getDetail(dto.getReviewSn()));
    }

    @PostMapping("/review")
    public JsonResult<Void> review(@RequestBody @Valid AdminAfReviewsReviewDTO dto) {
        afterSalesReviewsBusiness.review(dto);
        return JsonResult.ok();
    }

}
