package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivatedStatusEnum {

    DEFAULT(0, "未激活"),

    ACTIVATED(1, "已激活"),

    VOIDED(2, "已注销");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivatedStatusEnum node : ActivatedStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
