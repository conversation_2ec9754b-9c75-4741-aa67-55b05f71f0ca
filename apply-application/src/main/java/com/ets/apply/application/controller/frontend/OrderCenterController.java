package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.OrderCenterBusiness;
import com.ets.apply.application.common.dto.order.GetUnpaidOrderDetailDTO;
import com.ets.apply.application.common.dto.request.order.OrderFindDTO;
import com.ets.apply.application.common.vo.orderCenter.OrderCenterApplyOrderInfoVO;
import com.ets.apply.application.common.vo.orderCenter.OrderCenterApplyOrderListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/frontend/orderCenter")
public class OrderCenterController extends BaseController {

    @Autowired
    OrderCenterBusiness orderCenterBusiness;

    @RequestMapping("/applyOrderList")
    public JsonResult<List<OrderCenterApplyOrderListVO>> applyOrderList() {
        return JsonResult.ok(orderCenterBusiness.getApplyOrderList());
    }
    @RequestMapping("/getOperateParamsByOrderSn")
    public JsonResult<OrderCenterApplyOrderInfoVO> getOperateParamsByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        return JsonResult.ok(orderCenterBusiness.getOperateParamsByOrderSn(dto.getOrderSn()));
    }

}
